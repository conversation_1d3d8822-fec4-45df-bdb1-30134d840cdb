# Local Development Setup

This project now uses embedded PostgreSQL for local development instead of H2, providing better compatibility with the production Aurora PostgreSQL environment.

## Running Locally

### Prerequisites
- Java 17+
- Gradle

### Starting the Application

1. **Using the `local` profile:**
   ```bash
   ./gradlew bootRun --args='--spring.profiles.active=local'
   ```

2. **Using environment variable:**
   ```bash
   export SPRING_PROFILES_ACTIVE=local
   ./gradlew bootRun
   ```

3. **Using IDE:**
   Set the active profile to `local` in your IDE's run configuration.

### What happens when using the `local` profile:

- **Embedded PostgreSQL** starts automatically on port 5432
- **Flyway migrations** run automatically using the same PostgreSQL migrations as production
- **Database schema** is identical to production (Aurora PostgreSQL)
- **Snowflake fallback** uses the same embedded PostgreSQL instance
- **Logging** is configured for local development with readable console output

### Database Access

The embedded PostgreSQL database is accessible at:
- **Host:** localhost
- **Port:** 5432
- **Database:** postgres (default)
- **Username:** postgres (default)
- **Password:** (none)

You can connect using any PostgreSQL client like pgAdmin, DBeaver, or psql:
```bash
psql -h localhost -p 5432 -U postgres
```

### Benefits of Embedded PostgreSQL

1. **Dialect Compatibility:** Same PostgreSQL dialect as production
2. **Migration Testing:** Flyway migrations work exactly as in production
3. **No External Dependencies:** No need to install PostgreSQL locally
4. **Automatic Cleanup:** Database is cleaned up when application stops
5. **Consistent Environment:** Same database engine across all environments

### Troubleshooting

**Port 5432 already in use:**
If you have PostgreSQL installed locally, you might get a port conflict. The embedded PostgreSQL will automatically find an available port and log the actual port being used.

**Migration Issues:**
If you encounter Flyway migration issues, check that your migrations in `src/main/resources/db/migration/aurora/` are compatible with PostgreSQL.

**Performance:**
Embedded PostgreSQL is optimized for development and testing. For better performance during development, consider using a local PostgreSQL installation and updating the `application-local.yml` configuration.

## Testing

Tests also use embedded PostgreSQL (on port 5433) instead of H2, ensuring test database behavior matches production more closely.

Run tests with:
```bash
./gradlew test
```
