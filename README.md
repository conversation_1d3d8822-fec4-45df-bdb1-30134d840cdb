# Fleet Vehicle Register 

## Overview

This project ingests, validates, and reconstructs vehicle data from multiple sources (such as Frida, Autosys, and the national ship register), providing a robust event-sourced history of fleet vehicles. It supports both real-time and historic queries, and exposes APIs for vehicle and job management.

---

## Data Flow

- **Ingestion**
  - Reads data from Frida and Autosys (with plans for additional sources such as the national ship register).
  - Fields are flattened into typeless/string-based EventData.
  - Uses SCD2 (Slowly Changing Dimension Type 2) for versioning and event sourcing, storing all event data in a single table.
  - Allows temporary manual overrides while upstream data is fixed.

- **Validation**
  - Each EventData is checked using quality validators.
  - Field quality issues are reported to Snowflake and relevant stakeholders for correction at the source (e.g., Frida).

- **Reconstruction**
  - Vehicles are reconstructed from EventData using processors.
  - Each field in the reconstructed vehicle is assigned a quality metric (MISSING, UNKNOWN, CORRECTED, ENRICHED, INVALID, STALE, VALID), which is also saved as SCD2.
  - Reconstructed vehicles are saved as SCD2 snapshots.

- **Distribution**
  - Latest reconstructed vehicles are published to Kafka.
  - Historic vehicle states are accessible via the REST API.

---

## Features

- **Job Management**
  - Start, cancel, and continue jobs for data processing and reconstruction.
  - API endpoints for job and vehicle management.

- **Database as Source of Truth**
  - Event sourcing enables eventual consistency and manual overrides.
  - All changes and corrections are traceable.

- **Quality Reporting**
  - Field quality is systematically validated and reported for rapid issue detection and correction upstream.

---

## Architecture Diagram

```mermaid
flowchart TD
    Frida["Frida"] -- EventData --> EventData["EventData Table"]
    Autosys["Autosys"] -- EventData --> EventData
    EventData -- Processors --> ReconstructedVehicles["ReconstructedVehicles"]
    EventData -- Processors --> ReconstructedVehicleQuality
    ReconstructedVehicleQuality -- Kafka --> Customers
    ReconstructedVehicleQuality -- API --> REST["REST"]
    ReconstructedVehicles -- Kafka --> Customers["Customers"]
    ReconstructedVehicles -- API --> REST["REST"]
    EventData -- Validators --> QualityReporting["Frida Quality Reporting"]
    QualityReporting --> FridaMaintainers["FridaMaintainers"]
```

---

## Getting Started

1. **Clone the repository**
2. **Configure data sources and Kafka**
3. **Run the application**
4. **Use the REST API or Kafka consumers to access vehicle data**

---

## Roadmap

- Add support for (historic) contracts.
- Integrate data from additional teams and sources.

---