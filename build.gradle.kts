import org.jetbrains.kotlin.gradle.internal.KaptGenerateStubsTask

plugins {
    // Spring
    id("org.springframework.boot") version "3.4.0"
    id("io.spring.dependency-management") version "1.1.6"

    // Kotlin
    kotlin("jvm") version "2.1.10"
    kotlin("plugin.spring") version "2.1.10"
    kotlin("plugin.serialization") version "2.1.10"
    kotlin("plugin.jpa") version "2.1.10"
    kotlin("plugin.allopen") version "2.1.10"
    kotlin("kapt") version "2.1.10"

    // Other
    id("ch.acanda.gradle.fabrikt") version "1.7.0"
    id("org.jetbrains.gradle.plugin.idea-ext") version "1.1.7"
    id("org.jlleitschuh.gradle.ktlint") version "12.1.1"

    // For locally compiling avro so we can test a new vehicle object
    id("com.github.davidmc24.gradle.plugin.avro") version "1.9.1"
}

group = "no.ruter.kosi"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

object Versions {
    const val JAKARTA_PERSISTENCE = "3.1.+"
    const val JACKSON = "2.18.+"
    const val OKHTTP = "4.12.+"
    const val MOSHI = "1.15.+"
    const val CAFFEINE = "3.1.+"
    const val QUARTZ = "2.5.+"

    const val SWAGGER_CORE = "2.2.26"
    const val HIBERNATE = "6.6.9.Final"
    const val JUNIT_JUPITER = "5.10.0"
    const val MOCKK = "1.13.5"
    const val CONFLUENT = "7.8.0"

    const val COMMON_AVRO_SCHEMA = "1.13"
    const val VEHICLE_AVRO_SCHEMA = "1.37"

    const val KOTLIN_ARROW = "2.1.+"
    const val KTLINT = "1.5.0"
}

dependencies {
    // Spring Boot + Kotlin basics
    implementation("org.springframework.boot:spring-boot-starter")
    implementation(kotlin("reflect"))
    implementation(kotlin("stdlib"))
    implementation("io.arrow-kt:arrow-core:${Versions.KOTLIN_ARROW}")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("io.arrow-kt:arrow-fx-coroutines:${Versions.KOTLIN_ARROW}")

    // Scheduling
    implementation("org.quartz-scheduler:quartz:${Versions.QUARTZ}")
    implementation("org.springframework.boot:spring-boot-starter-quartz")
    implementation("com.mchange:c3p0:0.10.2")

    // Web, JPA, Security
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.security:spring-security-oauth2-resource-server")
    implementation("org.springframework.security:spring-security-oauth2-jose")
    implementation("org.springframework.security:spring-security-config")
    implementation("com.nimbusds:nimbus-jose-jwt:9.31")
    implementation("org.bouncycastle:bcprov-jdk18on:1.80")
    implementation("org.bouncycastle:bcpkix-jdk18on:1.80")

    // Cache
    implementation("com.github.ben-manes.caffeine:caffeine:${Versions.CAFFEINE}")

    // Jackson
    implementation("com.fasterxml.jackson.core:jackson-core:${Versions.JACKSON}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${Versions.JACKSON}")
    implementation("com.fasterxml.jackson.core:jackson-annotations:${Versions.JACKSON}")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:${Versions.JACKSON}")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${Versions.JACKSON}")

    // Mapping
    implementation("org.mapstruct:mapstruct:1.5.5.Final")
    kapt("org.mapstruct:mapstruct-processor:1.5.5.Final")

    // Swagger, HTTP, Moshi
    implementation("com.squareup.okhttp3:okhttp:${Versions.OKHTTP}")
    implementation("com.squareup.moshi:moshi-kotlin:${Versions.MOSHI}")
    implementation("io.swagger.core.v3:swagger-core:${Versions.SWAGGER_CORE}")

    // Jakarta/Hibernate
    implementation("jakarta.persistence:jakarta.persistence-api:${Versions.JAKARTA_PERSISTENCE}")
    implementation("org.hibernate.orm:hibernate-core:${Versions.HIBERNATE}")
    implementation("jakarta.servlet:jakarta.servlet-api:6.1.0")
    implementation("io.hypersistence:hypersistence-utils-hibernate-63:3.9.10")

    // Kafka
    implementation("org.springframework.kafka:spring-kafka")
    implementation("org.apache.kafka:kafka-streams")
    implementation("io.confluent:kafka-schema-registry-client:${Versions.CONFLUENT}")
    implementation("io.confluent:kafka-streams-avro-serde:${Versions.CONFLUENT}")
    implementation("io.confluent:kafka-avro-serializer:${Versions.CONFLUENT}")
    implementation("no.ruter.rdp.avro:common:${Versions.COMMON_AVRO_SCHEMA}")
    implementation("no.ruter.rdp.avro:vehicle:${Versions.VEHICLE_AVRO_SCHEMA}")

    // Flyway
    implementation("org.flywaydb:flyway-core")
    implementation("org.flywaydb:flyway-database-postgresql")
    implementation("org.flywaydb:flyway-database-snowflake")

    // Database
    implementation("org.postgresql:postgresql:42.7.5")
    implementation("com.h2database:h2")
    implementation("net.snowflake:snowflake-jdbc:3.23.1")
    implementation("io.zonky.test:embedded-postgres:2.0.7")
    testImplementation("org.testcontainers:postgresql:1.19.3")
    testImplementation("org.testcontainers:junit-jupiter:1.19.3")

    // Serialization
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.8.0")

    // Logging
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")
    implementation("org.codehaus.janino:janino:3.1.9")

    // Testing
    testImplementation("org.junit.jupiter:junit-jupiter:${Versions.JUNIT_JUPITER}")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.mockk:mockk:${Versions.MOCKK}")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testImplementation("com.squareup.okhttp3:mockwebserver:${Versions.OKHTTP}")
    testImplementation("com.ninja-squad:springmockk:4.0.2")
    testImplementation("org.springframework.kafka:spring-kafka-test")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    // Configuration processor
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
}

repositories {
    mavenLocal()
    mavenCentral()
    maven("https://packages.confluent.io/maven/")
    maven("https://repo.osgeo.org/repository/release/")
    maven("https://gitlab.com/api/v4/groups/6569239/-/packages/maven") {
        gitLabCredentials()
    }
}

fun MavenArtifactRepository.gitLabCredentials() {
    credentials(HttpHeaderCredentials::class.java) {
        System.getenv("CI_JOB_TOKEN")?.let {
            name = "Job-Token"
            value = it
            return@credentials
        }
        name = "Private-Token"
        value = project.properties["gitLabPrivateToken"] as String? ?: error(
            "Couldn't find CI_JOB_TOKEN env variable. If locally, set 'gitLabPrivateToken=<gitlab-personal-access-token>' in ~/.gradle/gradle.properties!",
        )
    }
    authentication {
        create<HttpHeaderAuthentication>("header")
    }
}

kotlin {
    compilerOptions {
        freeCompilerArgs.add("-Xjsr305=strict")
    }
    sourceSets.all {
        languageSettings.optIn("arrow.core.Validated")
        languageSettings.optIn("arrow.core.raise.ExperimentalRaiseApi")
    }
}

avro {
    stringType = "String"
}

tasks.withType<KaptGenerateStubsTask>().configureEach {
    dependsOn("fabriktGenerate")
}

tasks.withType<Test> {
    useJUnitPlatform()
    jvmArgs("--add-opens=java.base/java.lang=ALL-UNNAMED")
}

sourceSets {
    main {
        java {
            srcDir(layout.buildDirectory.dir("generated"))
        }
    }
}

kapt {
    correctErrorTypes = true
}

fabrikt {
    generate("frida") {
        apiFile = layout.projectDirectory.file("src/main/resources/fridaswagger.json")
        basePackage = "no.ruter.kosi.fridaclient"
        validationLibrary = Jakarta
        quarkusReflectionConfig = enabled
        typeOverrides {
            datetime = OffsetDateTime
            binary = ByteArray
        }
        client {
            generate = enabled
            target = OkHttp
            resilience4j = disabled
            suspendModifier = disabled
            springResponseEntityWrapper = disabled
        }
        controller {
            generate = disabled
            authentication = enabled
            suspendModifier = disabled
            completionStage = disabled
            target = Spring
        }
        model {
            generate = enabled
            extensibleEnums = disabled
            javaSerialization = disabled
            quarkusReflection = disabled
            micronautIntrospection = disabled
            micronautReflection = disabled
            includeCompanionObject = disabled
            sealedInterfacesForOneOf = disabled
            ignoreUnknownProperties = disabled
            serializationLibrary = Jackson
        }
        skip = false
    }
    generate("autosys") {
        apiFile = layout.projectDirectory.file("src/main/resources/autosysswagger.json")
        basePackage = "no.ruter.kosi.autosysclient"
        validationLibrary = Jakarta
        quarkusReflectionConfig = enabled
        typeOverrides {
            datetime = OffsetDateTime
            binary = ByteArray
        }
        client {
            generate = enabled
            target = OkHttp
            resilience4j = disabled
            suspendModifier = disabled
            springResponseEntityWrapper = disabled
        }
        controller {
            generate = disabled
            authentication = enabled
            suspendModifier = disabled
            completionStage = disabled
            target = Spring
        }
        model {
            generate = enabled
            extensibleEnums = disabled
            javaSerialization = disabled
            quarkusReflection = disabled
            micronautIntrospection = disabled
            micronautReflection = disabled
            includeCompanionObject = disabled
            sealedInterfacesForOneOf = disabled
            ignoreUnknownProperties = disabled
            serializationLibrary = Jackson
        }
        skip = false
    }
}

allOpen {
    annotation("jakarta.persistence.Entity")
    annotation("jakarta.persistence.MappedSuperclass")
    annotation("jakarta.persistence.Embeddable")
}

ktlint {
    version = Versions.KTLINT
    filter {
        exclude { element ->
            element.file.path.contains("generated") ||
                element.file.path.contains("fabrikt")
        }
        include("**/kotlin/**")
    }
}

tasks.named("ktlintKotlinScriptCheck") {
    onlyIf { gradle.startParameter.taskNames.contains("ktlintCheck") }
}

tasks.named("runKtlintCheckOverMainSourceSet") {
    onlyIf { gradle.startParameter.taskNames.contains("ktlintCheck") }
}

tasks.named("ktlintTestSourceSetCheck") {
    onlyIf { gradle.startParameter.taskNames.contains("ktlintCheck") }
}

tasks.named("runKtlintCheckOverTestSourceSet") {
    dependsOn(tasks.named("generateTestAvroJava"))
}
