#!/usr/bin/env python3
import json
import argparse
import difflib

RED = "\033[31m"
GREEN = "\033[32m"
YELLOW = "\033[33m"
RESET = "\033[0m"

def remove_entityheader(obj):
    if isinstance(obj, dict):
        new_obj = {}
        for key, value in obj.items():
            if key == "entityHeader":
                continue
            new_obj[key] = remove_entityheader(value)
        return new_obj
    elif isinstance(obj, list):
        return [remove_entityheader(item) for item in obj]
    else:
        return obj

def read_file_to_dict(filename):
    mapping = {}
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split(maxsplit=1)
            if len(parts) < 2:
                print(f"Skipping malformed line: {line}")
                continue
            vehicle, json_str = parts[0], parts[1]
            try:
                data = json.loads(json_str)
            except Exception as e:
                print(f"Error parsing JSON for vehicle '{vehicle}' in line: {line}\nError: {e}")
                continue
            mapping[vehicle] = data
    return mapping

def print_diff(paxtelling_obj, kosi_obj):
    paxtelling_str = json.dumps(paxtelling_obj, indent=2, sort_keys=True)
    kosi_str = json.dumps(kosi_obj, indent=2, sort_keys=True)
    diff_lines = difflib.unified_diff(
        paxtelling_str.splitlines(),
        kosi_str.splitlines(),
        fromfile='paxtelling',
        tofile='kosi',
        lineterm=''
    )
    for line in diff_lines:
        if line.startswith('---') or line.startswith('+++'):
            print(YELLOW + line + RESET)
        elif line.startswith('@@'):
            print(YELLOW + line + RESET)
        elif line.startswith('-'):
            print(RED + line + RESET)
        elif line.startswith('+'):
            print(GREEN + line + RESET)
        else:
            print(line)

def main():
    parser = argparse.ArgumentParser(description="Compare two kafka capacity outputs")
    parser.add_argument("--paxtelling", required=True, help="Path to current paxtelling file")
    parser.add_argument("--kosi", required=True, help="Path to new kosi file")
    args = parser.parse_args()

    paxtelling_data = read_file_to_dict(args.paxtelling)
    kosi_data = read_file_to_dict(args.kosi)

    paxtelling_data = {vehicle: remove_entityheader(data) for vehicle, data in paxtelling_data.items()}
    kosi_data = {vehicle: remove_entityheader(data) for vehicle, data in kosi_data.items()}

    differences_found = False

    missing_in_kosi = sorted([vehicle for vehicle in paxtelling_data if vehicle not in kosi_data])
    extra_in_kosi = sorted([vehicle for vehicle in kosi_data if vehicle not in paxtelling_data])

    if missing_in_kosi:
        differences_found = True
        print(f"{RED}vehicles missing in kosi file (present in paxtelling):{RESET}")
        for vehicle in missing_in_kosi:
            print(f"  {vehicle}")
        print()

    if extra_in_kosi:
        differences_found = True
        print(f"{RED}vehicles extra in kosi file (not present in paxtelling):{RESET}")
        for vehicle in extra_in_kosi:
            print(f"  {vehicle}")
        print()

    common_vehicles = sorted(set(paxtelling_data.keys()) & set(kosi_data.keys()))
    for vehicle in common_vehicles:
        paxtelling_obj = paxtelling_data[vehicle]
        kosi_obj = kosi_data[vehicle]
        if paxtelling_obj != kosi_obj:
            differences_found = True
            print(f"Differences for vehicle: {vehicle}")
            print_diff(paxtelling_obj, kosi_obj)
            print()

    if not differences_found:
        print(f"{GREEN}No differences found between the files.{RESET}")

if __name__ == '__main__':
    main()
