{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Accessibility", "fields": [{"name": "lowFloor", "type": ["null", "boolean"], "default": null}, {"name": "lowEntry", "type": ["null", "string"], "default": null}, {"name": "lift", "type": ["null", "boolean"], "default": null}, {"name": "ramp", "type": ["null", "boolean"], "default": null}, {"name": "kneeling", "type": ["null", "boolean"], "default": null}, {"name": "accessibilityRamp", "type": ["null", "boolean"], "default": null}, {"name": "wheelchairPlaces", "type": ["null", "int"], "default": null}, {"name": "walker<PERSON><PERSON>s", "type": ["null", "int"], "default": null}, {"name": "covid19CapacityLimit", "type": ["null", "int"], "default": null}]}