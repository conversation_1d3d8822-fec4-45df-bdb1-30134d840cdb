{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Capacity", "fields": [{"name": "seated", "type": ["null", "int"], "default": null}, {"name": "standing", "type": ["null", "int"], "default": null}, {"name": "total", "type": ["null", "int"], "default": null}, {"name": "standingAreaM2", "type": ["null", "double"], "default": null}, {"name": "predictedTotal2PerM2", "type": ["null", "int"], "default": null}, {"name": "predictedTotal3PerM2", "type": ["null", "int"], "default": null}, {"name": "predictedTotal4PerM2", "type": ["null", "int"], "default": null}]}