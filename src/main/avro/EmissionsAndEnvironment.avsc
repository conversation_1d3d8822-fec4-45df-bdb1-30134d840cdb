{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "EmissionsAndEnvironment", "fields": [{"name": "environmentalClass", "type": ["null", "string"], "default": null}, {"name": "euroClass", "type": ["null", "string"], "default": null}, {"name": "emissionUnitId", "type": ["null", "int"], "default": null}, {"name": "emissionValues", "type": ["null", "EmissionValues"], "default": null}, {"name": "certification", "type": ["null", "Certification"], "default": null}, {"name": "energyUse", "type": ["null", "double"], "default": null}, {"name": "consumptionMixedDriving", "type": ["null", "double"], "default": null}, {"name": "EEVVehicle", "type": ["null", "boolean"], "default": null}]}