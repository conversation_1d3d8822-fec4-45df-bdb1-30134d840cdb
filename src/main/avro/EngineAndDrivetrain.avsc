{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "EngineAndDrivetrain", "fields": [{"name": "engineTypeId", "type": ["null", "string"], "default": null}, {"name": "primaryCategory", "type": ["null", "string"], "default": null}, {"name": "hybridVehicle", "type": ["null", "boolean"], "default": null}, {"name": "gearbox", "type": ["null", "Gearbox"], "default": null}, {"name": "maxSpeed", "type": ["null", {"type": "array", "items": "double"}], "default": null}, {"name": "motor", "type": ["null", "Motor"], "default": null}, {"name": "noise", "type": ["null", "Noise"], "default": null}]}