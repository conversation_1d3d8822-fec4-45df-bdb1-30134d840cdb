{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Features", "fields": [{"name": "comfort", "type": ["null", "Comfort"], "default": null}, {"name": "accessibility", "type": ["null", "Accessibility"], "default": null}, {"name": "safety", "type": ["null", "Safety"], "default": null}, {"name": "signage", "type": ["null", "Signage"], "default": null}, {"name": "APC", "type": ["null", "APCInfo"], "default": null}, {"name": "other", "type": ["null", "OtherFeatures"], "default": null}]}