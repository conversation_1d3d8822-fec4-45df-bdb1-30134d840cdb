{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Identification", "fields": [{"name": "vehicleId", "type": ["null", "int"], "default": null}, {"name": "vehicleSISId", "type": ["null", "string"], "default": null}, {"name": "publicId", "type": ["null", "string"], "default": null}, {"name": "licensePlate", "type": ["null", "string"], "default": null}, {"name": "chassisNumber", "type": ["null", "string"], "default": null}, {"name": "manufacturer", "type": ["null", "Manufacturer"], "default": null}, {"name": "modelYear", "type": ["null", "string"], "default": null}, {"name": "model", "type": ["null", "ModelInfo"], "default": null}, {"name": "color", "type": ["null", "string"], "default": null}, {"name": "busType", "type": ["null", "BusType"], "default": null}]}