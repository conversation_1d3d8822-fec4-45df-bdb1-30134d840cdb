{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "OtherFeatures", "fields": [{"name": "bikeRack", "type": ["null", "boolean"], "default": null}, {"name": "boosterSeat", "type": ["null", "boolean"], "default": null}, {"name": "childSeats", "type": ["null", "boolean"], "default": null}, {"name": "strollerPlaces", "type": ["null", "int"], "default": null}, {"name": "passengerCounter", "type": ["null", "boolean"], "default": null}, {"name": "ticketing", "type": ["null", "Ticketing"], "default": null}, {"name": "vehicleComputer", "type": ["null", "string"], "default": null}]}