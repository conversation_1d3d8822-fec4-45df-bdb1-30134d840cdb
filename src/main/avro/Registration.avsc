{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Registration", "fields": [{"name": "registrationDate", "type": ["null", "string"], "default": null}, {"name": "firstRegistrationNorwayDate", "type": ["null", "string"], "default": null}, {"name": "firstRegistrationOnOwnership", "type": ["null", "string"], "default": null}, {"name": "status", "type": ["null", "RegistrationStatus"], "default": null}, {"name": "use", "type": ["null", "VehicleUse"], "default": null}, {"name": "businessCode", "type": ["null", "string"], "default": null}, {"name": "businessDescription", "type": ["null", "string"], "default": null}, {"name": "periodicControl", "type": ["null", "PeriodicControl"], "default": null}, {"name": "permitPeriod", "type": ["null", "PermitPeriod"], "default": null}]}