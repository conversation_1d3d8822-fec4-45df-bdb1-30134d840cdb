{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Safety", "fields": [{"name": "seatBelts", "type": ["null", "string"], "default": null}, {"name": "seatBeltReminder", "type": ["null", "boolean"], "default": null}, {"name": "visualBeltReminder", "type": ["null", "boolean"], "default": null}, {"name": "airbag<PERSON><PERSON><PERSON><PERSON>", "type": ["null", "boolean"], "default": null}, {"name": "alcoLock", "type": ["null", "string"], "default": null}, {"name": "fireExtinguisher", "type": ["null", "boolean"], "default": null}, {"name": "fireExtinguisherInEngineCompartment", "type": ["null", "boolean"], "default": null}, {"name": "defibrillators", "type": ["null", "boolean"], "default": null}, {"name": "cameraSurveillanceSafety", "type": ["null", "boolean"], "default": null}, {"name": "cameraSurveillanceTrafficSafety", "type": ["null", "boolean"], "default": null}]}