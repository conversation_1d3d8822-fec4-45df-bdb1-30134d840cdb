{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Signage", "fields": [{"name": "destinationSign", "type": ["null", "string"], "default": null}, {"name": "internalInformationSign", "type": ["null", "boolean"], "default": null}, {"name": "externalDestinationCall", "type": ["null", "boolean"], "default": null}, {"name": "stopAnnouncements", "type": ["null", "boolean"], "default": null}]}