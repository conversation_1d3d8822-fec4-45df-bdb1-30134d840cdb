{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Technical", "fields": [{"name": "dimensions", "type": ["null", "Dimensions"], "default": null}, {"name": "weights", "type": ["null", "Weights"], "default": null}, {"name": "axles", "type": ["null", "<PERSON><PERSON><PERSON>"], "default": null}, {"name": "wheelsAndBrakes", "type": ["null", "WheelsAndBrakes"], "default": null}, {"name": "engineAndDrivetrain", "type": ["null", "EngineAndDrivetrain"], "default": null}, {"name": "emissionsAndEnvironment", "type": ["null", "EmissionsAndEnvironment"], "default": null}, {"name": "doors", "type": ["null", "string"], "default": null}]}