{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "TyreRimCombination", "fields": [{"name": "axleId", "type": ["null", "int"], "default": null}, {"name": "tyre", "type": ["null", "string"], "default": null}, {"name": "rim", "type": ["null", "string"], "default": null}, {"name": "loadIndex", "type": ["null", "int"], "default": null}, {"name": "speedCode", "type": ["null", "string"], "default": null}, {"name": "twin", "type": ["null", "boolean"], "default": null}]}