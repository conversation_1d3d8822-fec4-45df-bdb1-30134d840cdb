{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "VehicleV3", "fields": [{"name": "identification", "type": ["null", "Identification"], "default": null}, {"name": "registration", "type": ["null", "Registration"], "default": null}, {"name": "features", "type": ["null", "Features"], "default": null}, {"name": "capacity", "type": ["null", "Capacity"], "default": null}, {"name": "contractor", "type": ["null", "Contractor"], "default": null}, {"name": "technical", "type": ["null", "Technical"], "default": null}]}