{"namespace": "no.ruter.avro.entity.vehicle.details.v2", "type": "record", "name": "Weights", "fields": [{"name": "curbWeight", "type": ["null", "double"], "default": null}, {"name": "totalWeight", "type": ["null", "double"], "default": null}, {"name": "unitWeights", "type": ["null", "UnitWeights"], "default": null}, {"name": "payload", "type": ["null", "int"], "default": null}, {"name": "grossVehicleWeight", "type": ["null", "int"], "default": null}]}