{"name": "EntityHeaderV2", "namespace": "no.ruter.avro.common", "type": "record", "doc": "Common header for entities", "fields": [{"name": "eventTimestamp", "doc": "Timestamp as the one tagged in the event by the source in ISO format. Eg. GPS timestamp. If the source does not have a timestamp it should be the same as receivedTimestamp. If a change in an entity is done as a consequence of a change in another entity partition or a command the eventTimestamp is copied from that record.", "type": ["null", "string"], "default": null}, {"name": "receivedTimestamp", "doc": "Timestamp as the close to the timestamp we first received the event on RDP Eg. when the event was pulled from the source before it was put on Kafka. If a change in an entity is done as a consequence of a change in another entity partition or a command the receivedTimestamp is copied from that record. Otherwise a new unique receivedTimestamp is created.", "type": ["null", "string"], "default": null}, {"name": "publishedTimestamp", "doc": "Timestamp as close to the time before this specific entity was produced to Kafka as possible", "type": ["null", "string"], "default": null}, {"name": "traceId", "doc": "Identify a transaction uniquely across multiple changes in entities and commands. If a command is done as a consequence of a change in another entity partition or a command the traceId is copied from that record. Otherwise a new unique UUID is created.", "type": ["null", "string"], "default": null}, {"name": "spanId", "doc": "Identify a request uniquely. There is a one-to-many relationship between traceId and spanId. See the following documentation for more information: https://gitlab.com/ruter-as/arkitektur/arkitekturprinsipper/-/blob/main/docs/veileder-til-bruk-av-header-i-kafka.md?ref_type=heads#spanid", "type": ["null", "string"], "default": null}, {"name": "originId", "doc": "Identify the originating publisherId of a transaction across multiple changes in entities and commands. If the entity is done as a consequence of a change in another entity or a command the originId is copied from that record. Otherwise same value as in publisherId is used.", "type": ["null", "string"], "default": null}, {"name": "publisherId", "doc": "The name of the application that created this specific instance", "type": ["null", "string"], "default": null}, {"name": "ownerId", "doc": "Owner of this entity. Following is the known values -as of now-, but take into consideration that this can change without a major update in the future when using this value: RUTER, OSTFOLD, BRAKAR, AGDER, KOLUMBUS, SKYSS, TROMS, TET, COMMON. COMMON is to be used when the data is not owned by TET or one of the PTAs", "type": "string", "default": "RUTER"}, {"name": "name", "doc": "The name of this entity, without version and partition. E.g. 'order', 'delivery'", "type": ["null", "string"], "default": null}, {"name": "version", "doc": "Version of this entity. e.g.'2'", "type": ["null", "int"], "default": null}, {"name": "partition", "doc": "The name of the sub-entity. E.g:\n entity.vehicle.key -> partition: key\n entity.vehicle.progress -> partition: progress", "type": ["null", "string"], "default": null}, {"name": "key", "doc": "This is the key of the entity", "type": ["null", "string"], "default": null}]}