package no.ruter.kosi.fleet

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@ConfigurationPropertiesScan
@EntityScan(basePackages = ["no.ruter.kosi.fleet"])
@EnableScheduling
class FleetApplication

fun main(args: Array<String>) {
    runApplication<FleetApplication>(*args)
}
