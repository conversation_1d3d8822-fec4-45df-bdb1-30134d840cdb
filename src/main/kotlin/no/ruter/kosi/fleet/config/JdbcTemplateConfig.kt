package no.ruter.kosi.fleet.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.core.JdbcTemplate
import javax.sql.DataSource

@Configuration
class JdbcTemplateConfig {
    @Bean("snowflakeJdbcTemplate")
    fun jdbcTemplate(@Qualifier("snowflakeDataSource") dataSource: DataSource): JdbcTemplate = JdbcTemplate(dataSource)
}
