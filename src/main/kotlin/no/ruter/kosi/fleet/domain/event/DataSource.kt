package no.ruter.kosi.fleet.domain.event

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter

enum class DataSource(
    val priority: Int,
) {
    MANUAL(3),
    AUTOSYS(2),
    FRIDA(1),
    UNKNOWN(0),
}

@Converter(autoApply = true)
class VehicleDataSourceConverter : AttributeConverter<DataSource, String> {
    override fun convertToDatabaseColumn(attribute: DataSource): String = attribute.name

    override fun convertToEntityAttribute(dbData: String?): DataSource = dbData?.let { DataSource.valueOf(it) } ?: DataSource.UNKNOWN
}
