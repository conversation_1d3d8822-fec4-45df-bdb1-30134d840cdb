package no.ruter.kosi.fleet.domain.event.entities

import com.fasterxml.jackson.annotation.JsonManagedReference
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import java.time.Instant

@Entity
@Table(name = "events")
class Event private constructor(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val eventId: Int = 0,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id", nullable = false)
    val vehicle: InternalVehicle,
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "job_id", nullable = false)
    val job: FleetJob,

    @OneToMany(mappedBy = "event", cascade = [CascadeType.ALL], fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonManagedReference
    val eventDataList: MutableList<EventData> = mutableListOf(),

    val timestamp: Instant = Instant.now(),
    val source: String,
    @Column(nullable = false)
    val priority: Int
) {
    fun addEventData(
        fieldName: String,
        fieldValue: Any?,
    ): EventData {
        val eventData =
            EventData(
                event = this,
                fieldName = fieldName,
                fieldValue = fieldValue?.toString(),
                businessKey = "${vehicle.vehicleRef!!}-$fieldName", // !! here is a bit iffy, but should be okay at this point
            )
        eventDataList.add(eventData)
        return eventData
    }

    companion object {
        fun create(
            eventId: Int = 0,
            vehicle: InternalVehicle,
            job: FleetJob,
            source: String,
            priority: Int,
            eventDataList: List<EventData> = mutableListOf(),
            timestamp: Instant = Instant.now(),
        ): Event =
            Event(
                eventId = eventId,
                vehicle = vehicle,
                job = job,
                eventDataList = eventDataList.toMutableList(),
                timestamp = timestamp,
                source = source,
                priority = priority
            )

    }

}
