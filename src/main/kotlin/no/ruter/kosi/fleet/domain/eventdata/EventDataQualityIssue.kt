package no.ruter.kosi.fleet.domain.eventdata

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2Entity
import java.time.Instant

@Entity
@Table(
    name = "event_data_quality_issues",
    indexes = [
        Index(name = "idx_event_data_quality_issue_current", columnList = "is_current"),
        Index(name = "idx_event_data_quality_issue_business_key", columnList = "business_key"),
    ]
)
class EventDataQualityIssue(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Int = 0,

    @Column(name = "event_data_id", nullable = false)
    val eventDataId: Int,
    
    @Column(name = "field_name", nullable = false)
    val fieldName: String,
    
    @Column(name = "actual_value")
    val actualValue: String?,
    
    @Column(name = "expected_value")
    val expectedValue: String?,
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    val type: QualityType,
    
    @Column(nullable = false)
    val timestamp: Instant = Instant.now(),
    
    @Column(name = "business_key", nullable = false)
    final override var businessKey: String = "",

    @Column(name = "effective_from", nullable = false)
    final override var effectiveFrom: Instant = Instant.now(),
    
    @Column(name = "effective_to", nullable = true)
    final override var effectiveTo: Instant? = null,
    
    @Column(name = "is_current", nullable = false)
    final override var isCurrent: Boolean = true
) : SCD2BaseEntity<Int, String>(), SCD2Entity<Int, String> {
    
    init {
        if (businessKey.isBlank()) {
            businessKey = "$eventDataId-$fieldName"
        }
    }

    override fun toString(): String =
        "EventDataQualityIssue(id=$id, eventDataId=$eventDataId, fieldName='$fieldName', " +
            "type=$type, effectiveFrom=$effectiveFrom, effectiveTo=$effectiveTo, isCurrent=$isCurrent, businessKey='$businessKey')"
            
    companion object {
        fun createNewVersion(
            previous: EventDataQualityIssue,
            newActualValue: String? = null,
            newExpectedValue: String? = null,
            newType: QualityType? = null,
            businessKey: String? = null
        ): EventDataQualityIssue {
            return EventDataQualityIssue(
                eventDataId = previous.eventDataId,
                fieldName = previous.fieldName,
                actualValue = newActualValue ?: previous.actualValue,
                expectedValue = newExpectedValue ?: previous.expectedValue,
                type = newType ?: previous.type,
                timestamp = Instant.now(),
                businessKey = businessKey ?: previous.businessKey
            )
        }
    }
}
