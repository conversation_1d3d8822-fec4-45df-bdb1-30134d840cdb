package no.ruter.kosi.fleet.domain.eventdata

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface EventDataQualityIssueRepositoryPort {
    fun save(qualityIssue: EventDataQualityIssue): EventDataQualityIssue
    fun findById(id: Int): EventDataQualityIssue?
    fun findByEventDataId(eventDataId: Int): List<EventDataQualityIssue>
    fun findCurrentByEventDataId(eventDataId: Int): List<EventDataQualityIssue>
    fun findCurrentByEventDataIds(eventDataIds: List<Int>): List<EventDataQualityIssue>

    fun findWithFilters(
        eventDataId: Int?,
        fieldName: String?,
        type: String?,
        isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventDataQualityIssue>
}
