package no.ruter.kosi.fleet.domain.eventdata

import no.ruter.kosi.fleet.domain.eventdata.validators.common.EventDataQualityValidator
import org.springframework.stereotype.Component

@Component
class EventDataQualityValidatorRepository(
    fieldProcessors: List<EventDataQualityValidator>,
) {
    private val processors: List<EventDataQualityValidator> = fieldProcessors

    fun getProcessors(): List<EventDataQualityValidator> = processors
}
