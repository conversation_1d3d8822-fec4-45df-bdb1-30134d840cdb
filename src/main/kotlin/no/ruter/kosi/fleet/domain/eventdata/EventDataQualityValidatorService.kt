package no.ruter.kosi.fleet.domain.eventdata

import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import org.springframework.stereotype.Service

@Service
class EventDataQualityValidatorService(
    private val validatorRepository: EventDataQualityValidatorRepository,
) {
    fun validateEventData(
        vehicle: InternalVehicle,
        allEventData: List<EventData>,
    ): List<EventDataQualityIssue> {
        val validators = validatorRepository.getProcessors()
            .associateBy { it.name }

        return allEventData.flatMap { event ->
            validators[event.fieldName]?.let { validator ->
                listOf(validator.process(vehicle, event))
            }?.filterNotNull() ?: emptyList()
        }
    }
}