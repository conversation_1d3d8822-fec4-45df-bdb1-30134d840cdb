package no.ruter.kosi.fleet.domain.eventdata

import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class EventDataValidationService(
    private val internalVehicleRepository: AuroraInternalVehicleRepository,
    private val eventDataQualityValidatorService: EventDataQualityValidatorService,
    private val eventDataQualityIssueRepositoryPort: EventDataQualityIssueRepositoryPort,
) {
    @Transactional
    fun validateAllVehicles() {
        val allVehicles = internalVehicleRepository.findAll()
        allVehicles.forEach { vehicle ->
            MdcUtils.setVehicleId(vehicle.vehicleId)
            MdcUtils.setVehicleRef(vehicle.vehicleRef)

            persistAll(eventDataQualityValidatorService.validateEventData(
                vehicle, vehicle.events.flatMap { it.eventDataList }
            ))
        }
    }

    @Transactional
    fun persistAll(
        eventDataQualityIssues: List<EventDataQualityIssue>,
    ): List<EventDataQualityIssue> =
        eventDataQualityIssues.map { eventDataQualityIssueRepositoryPort.save(it) }
}