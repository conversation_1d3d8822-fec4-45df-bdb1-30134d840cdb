package no.ruter.kosi.fleet.domain.eventdata.entities

import com.fasterxml.jackson.annotation.JsonBackReference
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import java.time.Instant

@Entity
@Table(
    name = "event_data",
    indexes = [
        Index(name = "idx_event_data_current", columnList = "is_current"),
    ],
)
class EventData(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Int = 0,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_id", nullable = false)
    @JsonBackReference
    var event: Event,

    @Column(nullable = false)
    val fieldName: String,

    @Column(nullable = true)
    val fieldValue: String?,
    val timestamp: Instant = Instant.now(),

    override var businessKey: String, // vehicleRef + '-' + fieldname in this case?
) : SCD2BaseEntity<Int, String>() {
    override fun toString(): String =
        "EventData(eventDataId=$id, event=${event.eventId}, fieldName='$fieldName', " +
            "fieldValue=$fieldValue, effectiveFrom=$effectiveFrom, effectiveTo=$effectiveTo, isCurrent=$isCurrent, businessKey='$businessKey')"

    companion object {
        fun createNewVersion(
            previous: EventData,
            newValue: String?,
            newEvent: Event? = null,
            businessKey: String,
        ): EventData =
            EventData(
                event = newEvent ?: previous.event,
                fieldName = previous.fieldName,
                fieldValue = newValue,
                timestamp = Instant.now(),
                businessKey = businessKey,
            )
    }
}
