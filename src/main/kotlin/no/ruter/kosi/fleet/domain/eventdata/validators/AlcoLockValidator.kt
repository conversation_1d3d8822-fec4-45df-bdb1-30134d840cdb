package no.ruter.kosi.fleet.domain.eventdata.validators

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.eventdata.validators.common.BaseValidator
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.QualityType
import org.springframework.stereotype.Service

@Service
class AlcoLockValidator : BaseValidator("alcoLock") {
    override fun processInternal(
        vehicle: InternalVehicle,
        eventData: EventData
    ): EventDataQualityIssue {
        return try {
            eventData.fieldValue.toBoolean()
            EventDataQualityIssue(
                eventDataId = eventData.id,
                fieldName = eventData.fieldName,
                actualValue = eventData.fieldValue,
                expectedValue = eventData.fieldValue,
                type = QualityType.VALID,
            )
        } catch (e: Exception) {
            EventDataQualityIssue(
                eventDataId = eventData.id,
                fieldName = eventData.fieldName,
                actualValue = eventData.fieldValue,
                expectedValue = "true/false/null",
                type = QualityType.INVALID_VALUE,
            )
        }
    }
}
