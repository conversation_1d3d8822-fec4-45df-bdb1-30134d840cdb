package no.ruter.kosi.fleet.domain.eventdata.validators

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.eventdata.validators.common.BaseValidator
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.QualityType
import org.springframework.stereotype.Service

@Service
class PassengersSeatedValidator : BaseValidator("passengersSeated") {
    override fun processInternal(
        vehicle: InternalVehicle,
        eventData: EventData): EventDataQualityIssue
    {
        // TODO implement me
        return EventDataQualityIssue(
            eventDataId = eventData.id,
            fieldName = eventData.fieldName,
            actualValue = eventData.fieldValue,
            expectedValue = eventData.fieldValue,
            type = QualityType.VALID,
        )
    }
}
