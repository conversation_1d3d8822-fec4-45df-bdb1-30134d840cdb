package no.ruter.kosi.fleet.domain.eventdata.validators.common

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import org.slf4j.LoggerFactory

abstract class BaseValidator(
    override val name: String,
) : EventDataQualityValidator {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun process(
        vehicle: InternalVehicle,
        eventData: EventData,
    ): EventDataQualityIssue? =
        try {
            processInternal(vehicle, eventData)
        } catch (e: Exception) {
            logger.error("Unexpected error processing field $name: ${e.message}", e)
            null
        }

    protected abstract fun processInternal(
        vehicle: InternalVehicle,
        eventData: EventData,
    ): EventDataQualityIssue
}
