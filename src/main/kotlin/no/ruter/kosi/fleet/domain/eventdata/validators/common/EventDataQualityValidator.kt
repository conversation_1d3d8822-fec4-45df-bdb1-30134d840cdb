package no.ruter.kosi.fleet.domain.eventdata.validators.common

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle

interface EventDataQualityValidator {
    val name: String

    fun process(
        vehicle: InternalVehicle,
        eventData: EventData,
    ): EventDataQualityIssue?
}
