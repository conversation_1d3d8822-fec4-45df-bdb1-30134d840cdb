package no.ruter.kosi.fleet.domain.internalvehicle.entities
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedDate
import java.time.LocalDateTime

// our Aggregate root, basically
@Entity
@Table(name = "internal_vehicles")
class InternalVehicle(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val vehicleId: Int = 0,
    // where this vehicle originally was registered
    var dataSource: DataSource,
    // id in the source system
    var sourceId: Int = 0,
    var publicIdString: String? = null,
    var vehicleRef: String,
    @OneToMany(mappedBy = "vehicle", fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    val events: List<Event> = emptyList(),

    @Column(updatable = false)
    @CreationTimestamp
    private val createdAt: LocalDateTime? = null,
    @UpdateTimestamp
    private val updatedAt: LocalDateTime? = null,
)
