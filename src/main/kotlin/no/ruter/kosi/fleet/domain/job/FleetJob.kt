package no.ruter.kosi.fleet.domain.job

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.event.entities.Event
import java.time.Instant
import java.time.LocalDate

@Entity
@Table(name = "jobs")
class FleetJob(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val jobId: Int = 0,
    @OneToMany(mappedBy = "job", fetch = FetchType.LAZY)
    @JsonIgnore
    val events: List<Event> = emptyList(),
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    var type: JobType,
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    var status: JobStatus = JobStatus.PENDING,
    @Column(nullable = false)
    val manual: Boolean = false,
    @Column(name = "quartz_job_id", nullable = true)
    var quartzJobId: String?,
    @Column(name = "quartz_job_group", nullable = true)
    var quartzJobGroup: String?,
    @Column(name = "parent_job_id", nullable = true)
    var parentJobId: Int? = null,
    var totalItems: Int = 0,
    var processedItems: Int = 0,
    var errorCount: Int = 0,
    var lastError: String? = null,

    @Column(name = "stream_group")
    var streamGroup: String? = null,

    @Column(name = "import_date")
    var importDate: LocalDate? = null,

    @Column(nullable = false)
    val createdAt: Instant = Instant.now(),
    @Column(nullable = true)
    var startedAt: Instant? = null,
    @Column(nullable = true)
    var completedAt: Instant? = null,
) {
    enum class JobType {
        IMPORT_FRIDA_CONTRACTS,
        IMPORT_FRIDA_VEHICLES, // main import job
        VEHICLE_BATCH, // individual batch jobs
        IMPORT_AUTOSYS,
        EVENTDATA_VALIDATION,
        VEHICLE_RECONSTRUCTION,
        VEHICLE_PUBLISHING_KAFKA,
        VEHICLE_PUBLISHING_SNOWFLAKE,
        VEHICLE_CAPACITY_PUBLISHING,
        COMPLETE_IMPORT, // parent job that includes multiple child jobs
    }

    enum class JobStatus {
        PENDING, // job is created but not started
        PAUSED, // job is started but paused
        SCHEDULED, // job is scheduled for execution
        RUNNING, // job is currently running
        COMPLETED, // job completed successfully
        FAILED, // job execution failed
        CANCELLED, // job was cancelled manually
        DESCHEDULED, // job was removed from scheduler
    }

    val inProgress: Boolean
        get() = status == JobStatus.RUNNING || status == JobStatus.SCHEDULED

    val isCompleted: Boolean
        get() = status == JobStatus.COMPLETED || status == JobStatus.FAILED || status == JobStatus.CANCELLED

    val progressPercentage: Double
        get() = if (totalItems > 0) (processedItems.toDouble() / totalItems) * 100 else 0.0

    // helper methods
    fun markRunning() {
        status = JobStatus.RUNNING
        startedAt = Instant.now()
    }

    fun markCompleted() {
        if (status != JobStatus.CANCELLED) {
            status = JobStatus.COMPLETED
            completedAt = Instant.now()
        }
    }

    fun markFailed(error: String?) {
        if (status != JobStatus.CANCELLED) {
            status = JobStatus.FAILED
            lastError = error
            completedAt = Instant.now()
        }
    }

    fun markCancelled() {
        status = JobStatus.CANCELLED
        completedAt = Instant.now()
    }

    fun updateProgress(processed: Int) {
        status = JobStatus.RUNNING
        this.processedItems = processed
    }

    fun recordError(error: String) {
        errorCount++
        lastError = error
    }
}
