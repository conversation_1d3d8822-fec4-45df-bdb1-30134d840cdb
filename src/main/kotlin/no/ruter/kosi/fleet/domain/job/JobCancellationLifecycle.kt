package no.ruter.kosi.fleet.domain.job

import org.slf4j.LoggerFactory
import org.springframework.context.SmartLifecycle
import org.springframework.stereotype.Component

@Component
class JobCancellationLifecycle(
    private val jobService: JobService,
) : SmartLifecycle {
    private val logger = LoggerFactory.getLogger(JobCancellationLifecycle::class.java)

    @Volatile private var running = false

    override fun start() {
        running = true
    }

    override fun stop(callback: Runnable) {
        try {
            val activeJob = jobService.findActiveJobs()
            activeJob.forEach {
                logger.info("Stopping: cancelling Quartz job for job {}", it.jobId)
                jobService.cancelJob(it.jobId)
            }
        } finally {
            running = false
            callback.run()
        }
    }

    override fun stop() {
    }

    override fun isRunning(): Boolean = running

    // very low phase value to make sure this bean stops early (especially before we lose the datasources)
    override fun getPhase(): Int = Int.MIN_VALUE
}
