package no.ruter.kosi.fleet.domain.job

import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class JobService(
    private val jobRepository: AuroraJobRepository,
) {
    private val logger = LoggerFactory.getLogger(JobService::class.java)

    @Transactional
    fun createJob(
        type: FleetJob.JobType,
        manual: Boolean,
        quartzJobId: String? = null,
        quartzJobGroup: String? = null,
        parentJobId: Int? = null,
        parameters: String = "{}",
    ): FleetJob {
        val fleetJob =
            FleetJob(
                type = type,
                manual = manual,
                quartzJobId = quartzJobId,
                quartzJobGroup = quartzJobGroup,
                parentJobId = parentJobId,
            )
        jobRepository.save(fleetJob)
        jobRepository.flush()
        return fleetJob
    }

    @Transactional
    fun createChildJob(
        parentFleetJob: FleetJob,
        type: FleetJob.JobType,
        quartzJobId: String? = null,
        quartzJobGroup: String? = null,
        parameters: String = "{}",
    ): FleetJob =
        createJob(
            type = type,
            manual = parentFleetJob.manual,
            quartzJobId = quartzJobId,
            quartzJobGroup = quartzJobGroup,
            parentJobId = parentFleetJob.jobId,
            parameters = parameters,
        )

    @Transactional
    fun updateJobProgress(
        jobId: Int,
        processed: Int,
        total: Int,
    ) {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")

        job.totalItems = total
        job.updateProgress(processed)
        jobRepository.save(job)
    }

    @Transactional
    fun completeJob(jobId: Int) {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")

        job.markCompleted()
        jobRepository.save(job)

        updateParentJobStatus(job)
    }

    @Transactional
    fun failJob(
        jobId: Int,
        error: String,
    ) {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")
        logger.info("[failJob] BEFORE: jobId=$jobId, status=${job.status}")
        job.markFailed(error)
        logger.info("[failJob] AFTER markFailed: jobId=$jobId, status=${job.status}")
        jobRepository.save(job)
        logger.info("[failJob] AFTER save: jobId=$jobId, status=${job.status}")

        updateParentJobStatus(job)
    }

    @Transactional
    fun cancelJob(jobId: Int) {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")
        logger.info("[cancelJob] BEFORE: jobId=$jobId, status=${job.status}")
        job.markCancelled()
        logger.info("[cancelJob] AFTER markCancelled: jobId=$jobId, status=${job.status}")
        jobRepository.save(job)
        jobRepository.flush()
        logger.info("[cancelJob] AFTER save: jobId=$jobId, status=${job.status}")

        // cancel all child jobs if this is a parent job
        val childJobs = jobRepository.findByParentJobId(jobId)
        childJobs.forEach { childJob ->
            logger.info("[cancelJob] CHILD BEFORE: jobId=${childJob.jobId}, status=${childJob.status}")
            if (childJob.status == FleetJob.JobStatus.PENDING ||
                childJob.status == FleetJob.JobStatus.SCHEDULED ||
                childJob.status == FleetJob.JobStatus.RUNNING
            ) {
                childJob.markCancelled()
                logger.info("[cancelJob] CHILD AFTER markCancelled: jobId=${childJob.jobId}, status=${childJob.status}")
                jobRepository.save(childJob)
                jobRepository.flush()
                logger.info("[cancelJob] CHILD AFTER save: jobId=${childJob.jobId}, status=${childJob.status}")
            }
        }

        updateParentJobStatus(job)
    }

    @Transactional
    fun recordJobError(
        jobId: Int,
        error: String,
    ) {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")

        job.recordError(error)
        jobRepository.save(job)
    }

    private fun updateParentJobStatus(childFleetJob: FleetJob) {
        val parentJobId = childFleetJob.parentJobId ?: return

        val parentJob = jobRepository.findById(parentJobId) ?: throw IllegalStateException("Parent job $parentJobId not found")

        val childJobs = jobRepository.findByParentJobId(parentJobId)

        // if any child job is cancelled, the parent job is cancelled
        if (childJobs.any { it.status == FleetJob.JobStatus.CANCELLED }) {
            parentJob.markCancelled()
            jobRepository.save(parentJob)
            return
        }

        // if any child job failed, the parent job is failed
        if (childJobs.any { it.status == FleetJob.JobStatus.FAILED }) {
            parentJob.markFailed("One or more child jobs failed")
            jobRepository.save(parentJob)
            return
        }

        // if all child jobs are completed, the parent job is completed
        if (childJobs.all { it.status == FleetJob.JobStatus.COMPLETED }) {
            parentJob.markCompleted()
            jobRepository.save(parentJob)
            return
        }
    }

    fun getJobStatus(jobId: Int): Map<String, Any?> {
        val job = jobRepository.findById(jobId) ?: throw IllegalStateException("Job $jobId not found")

        if (job.type == FleetJob.JobType.COMPLETE_IMPORT) {
            val childJobs = jobRepository.findByParentJobId(jobId)
            val childStatuses = childJobs.map { "${it.type}: ${it.status}" }
            logger.debug("Child job statuses for job $jobId: $childStatuses")
        }

        return buildJobStatusMap(job)
    }

    private fun buildJobStatusMap(fleetJob: FleetJob): Map<String, Any?> {
        val childJobsInfo =
            if (fleetJob.type == FleetJob.JobType.COMPLETE_IMPORT) {
                val childJobs = jobRepository.findByParentJobId(fleetJob.jobId)
                childJobs.map { buildJobStatusMap(it) }
            } else {
                null
            }

        return mapOf(
            "jobId" to fleetJob.jobId,
            "type" to fleetJob.type,
            "status" to fleetJob.status,
            "manual" to fleetJob.manual,
            "createdAt" to fleetJob.createdAt,
            "startedAt" to fleetJob.startedAt,
            "completedAt" to fleetJob.completedAt,
            "totalItems" to fleetJob.totalItems,
            "processedItems" to fleetJob.processedItems,
            "progressPercentage" to fleetJob.progressPercentage,
            "errorCount" to fleetJob.errorCount,
            "lastError" to fleetJob.lastError,
            "inProgress" to fleetJob.inProgress,
            "parentJobId" to fleetJob.parentJobId,
            "childJobs" to childJobsInfo,
        )
    }

    fun findAll(): List<FleetJob> = jobRepository.findAll()

    fun findJob(jobId: Int): FleetJob? = jobRepository.findById(jobId)

    fun save(fleetJob: FleetJob): FleetJob = jobRepository.save(fleetJob)

    fun findActiveJobs(): List<FleetJob> = jobRepository.findByStatusIn(listOf(FleetJob.JobStatus.RUNNING, FleetJob.JobStatus.SCHEDULED))

    fun findLatestJobByType(type: FleetJob.JobType): FleetJob? = jobRepository.findLatestJobByType(type)

    fun findChildJobs(parentJobId: Int): List<FleetJob> = jobRepository.findByParentJobId(parentJobId)


    fun findPendingOrScheduledJobs(): List<FleetJob> =
        jobRepository.findByStatusIn(listOf(FleetJob.JobStatus.PENDING, FleetJob.JobStatus.SCHEDULED))

    fun deschedulePendingOrScheluduledJobs() =
        findPendingOrScheduledJobs()
            .filter { !it.manual && it.type == FleetJob.JobType.COMPLETE_IMPORT }
            .forEach {
                it.status = FleetJob.JobStatus.DESCHEDULED
                jobRepository.save(it)
            }
}
