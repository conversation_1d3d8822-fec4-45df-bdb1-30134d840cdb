package no.ruter.kosi.fleet.domain.quality

import java.time.Instant

enum class QualityDimension {
    ACCURACY,
    COMPLETENESS,
    CONSISTENCY,
    UNIQUENESS,
    TIMELINESS,
    VALIDITY,
}

enum class QualityType(
    val dimension: QualityDimension,
) {
    MISSING_VALUE(QualityDimension.COMPLETENESS),
    UNKNOWN_VALUE(QualityDimension.VALIDITY),
    CORRECTED_VALUE(QualityDimension.VALIDITY),
    ENRICHED_VALUE(QualityDimension.COMPLETENESS),
    INVALID_VALUE(QualityDimension.VALIDITY),
    STALE_VALUE(QualityDimension.TIMELINESS),
    VALID(QualityDimension.VALIDITY),
}

// importnr, importdato, vehicleRef, fieldName, qualityType
data class VehicleFieldQuality(
    val name: String? = null,
    val value: Any? = null,
    val type: QualityType,
    val message: String = "",
    val createdAt: Instant = Instant.now()
)
