package no.ruter.kosi.fleet.domain.quality

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2Entity
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleBusinessKey
import java.time.Instant

@Entity
@Table(name = "vehicle_field_quality")
class VehicleFieldQualityEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Int = 0,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_quality_id", nullable = false, updatable = false)
    var vehicleQuality: VehicleQualityEntity,

    @Column(name = "field_name", nullable = false, updatable = false)
    val fieldName: String,

    @Column(name = "field_value")
    val fieldValue: String? = null,

    @Column(name = "quality_type", nullable = false)
    val qualityType: String,

    @Column(name = "message")
    val message: String? = null,

    @Column(name = "created_at", nullable = false)
    val createdAt: Instant = Instant.now()
)