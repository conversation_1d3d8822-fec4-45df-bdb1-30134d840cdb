package no.ruter.kosi.fleet.domain.quality

import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2Entity
import java.time.Instant

@Entity
@Table(name = "vehicle_quality")
class VehicleQualityEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Int = 0,

    @Column(name = "vehicle_id", nullable = false, updatable = false)
    val vehicleId: Int,

    @OneToMany(
        mappedBy = "vehicleQuality",
        cascade = [CascadeType.ALL],
        orphanRemoval = true,
        fetch = FetchType.LAZY
    )
    val fieldQualities: MutableList<VehicleFieldQualityEntity> = mutableListOf(),

    override var businessKey: String
) : SCD2BaseEntity<Int, String>()