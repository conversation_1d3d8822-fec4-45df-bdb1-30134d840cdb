package no.ruter.kosi.fleet.domain.quarantine

import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraQuarantinedEventDataRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class QuarantineService(
    private val quarantineRepository: AuroraQuarantinedEventDataRepository,
) {
    private val logger = LoggerFactory.getLogger(QuarantineService::class.java)

    @Transactional
    fun quarantineField(
        event: Event,
        fieldName: String,
        reason: String,
    ): QuarantinedEventData {
        logger.debug(
            "Quarantining field '{}' for vehicle {}: {}",
            fieldName,
            event.vehicle.vehicleId,
            reason,
        )

        val quarantinedData =
            QuarantinedEventData.create(
                event = event,
                fieldName = fieldName,
                businessKey = "${event.vehicle.vehicleRef!!}-$fieldName",
            )

        return quarantineRepository.save(quarantinedData)
    }

    @Transactional
    fun quarantineProcessingError(
        event: Event,
        fieldName: String,
        error: ProcessingError,
    ): QuarantinedEventData {
        val reason = formatProcessingError(error)
        when (error) {
            is ProcessingError.InvalidFormat -> error.value?.toString()
            else -> null
        }

        return quarantineField(event, fieldName, reason)
    }

    @Transactional
    fun clearQuarantineForVehicle(vehicleId: Int) {
        logger.debug("Clearing quarantined data for vehicle {}", vehicleId)
        quarantineRepository.deleteByVehicleId(vehicleId)
    }

    private fun formatProcessingError(error: ProcessingError): String =
        when (error) {
            is ProcessingError.MissingRequiredData ->
                "Missing data for '${error.processor.simpleName}': ${error.message}"
            is ProcessingError.InvalidFormat ->
                "Invalid format in '${error.processor.simpleName}', value '${error.value}': ${error.message}"
            is ProcessingError.ExternalServiceFailure ->
                "External service '${error.service}' error: ${error.message}"
            is ProcessingError.UnexpectedError ->
                "Unexpected error: ${error.exception.message ?: "unknown error"}"
        }
}
