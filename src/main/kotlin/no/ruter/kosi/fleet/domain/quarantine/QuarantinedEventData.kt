package no.ruter.kosi.fleet.domain.quarantine

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import java.time.Instant

// honestly not even sure if this should use SCD2
@Entity
@Table(name = "quarantined_fields")
class QuarantinedEventData(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    override var id: Int = 0,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "event_id", nullable = false)
    var event: Event,

    @Column(nullable = false)
    val fieldName: String? = null,

    val timestamp: Instant = Instant.now(),

    override var businessKey: String, // eventdataid?
) : SCD2BaseEntity<Int, String>() {
    companion object {
        fun create(
            event: Event,
            fieldName: String,
            businessKey: String,
        ): QuarantinedEventData =
            QuarantinedEventData(
                event = event,
                fieldName = fieldName,
                timestamp = Instant.now(),
                businessKey = businessKey,
            )
    }
}
