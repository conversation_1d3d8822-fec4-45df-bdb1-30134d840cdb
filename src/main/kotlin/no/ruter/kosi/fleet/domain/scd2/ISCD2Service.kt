package no.ruter.kosi.fleet.domain.scd2

import java.time.Instant

// this is to force spring to wrap the scd2service instance in a jdk dynamic proxy, so we can use construction injection and get the repository there
interface ISCD2Service<T,ID,K> {
    fun findCurrentByBusinessKey(businessKey:K):T?
    fun saveNewVersion(entity:T, now: Instant = Instant.now()): T
    fun saveNewVersions(entities: List<T>, timestamp: Instant = Instant.now()): List<T>
    fun saveNewVersionsBulk(entities: List<T>, timestamp: Instant = Instant.now()): List<T>
    fun findVersionAtTime(businessKey: K, timestamp: Instant): T?
    fun findAllVersionsByBusinessKey(businessKey: K): List<T>
    fun isInitialized(instant: Instant): Boolean
}