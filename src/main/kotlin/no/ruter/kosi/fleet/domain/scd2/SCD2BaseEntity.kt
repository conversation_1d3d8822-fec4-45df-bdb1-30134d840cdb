package no.ruter.kosi.fleet.domain.scd2

import jakarta.persistence.Column
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.MappedSuperclass
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.full.memberProperties
import java.io.Serializable
import java.time.Instant

/**
 * Base class for SCD2 entities that provides common SCD2 functionality
 * Subclasses should implement [hasSameBusinessDataAs] to compare business fields
 */
@MappedSuperclass
abstract class SCD2BaseEntity<ID, K> : SCD2Entity<ID, K> {
    abstract override var id: ID

    @Column(nullable = false)
    override var effectiveFrom: Instant = Instant.now()

    @Column(nullable = true)
    override var effectiveTo: Instant? = null

    @Column(name = "is_current", nullable = false)
    override var isCurrent: Boolean = true

    @Transient
    protected val excludedFromBusinessDataComparison: Set<String> = setOf(
        "excludedFromBusinessDataComparison",
        "effectiveFrom",
        "effectiveTo",
        "isCurrent",
        "id",
    )

    override fun hasSameBusinessDataAs(other: SCD2Entity<ID, K>?): Boolean {
        if (other == null || this::class != other::class) return false
        
        val thisClass = this::class as KClass<Any>
        val otherClass = other::class as KClass<Any>
        
        return thisClass.memberProperties.all { property ->
            if (property.name in excludedFromBusinessDataComparison) {
                true
            } else {
                val thisValue = property.get(this)
                val otherValue = property.get(other)
                
                when {
                    thisValue is Collection<*> && otherValue is Collection<*> -> 
                        thisValue.toSet() == otherValue.toSet()
                    thisValue is Map<*, *> && otherValue is Map<*, *> -> 
                        thisValue == otherValue
                    else -> thisValue == otherValue
                }
            }
        }
    }
}