package no.ruter.kosi.fleet.domain.scd2

import java.time.Instant

/**
 * Base interface for SCD2 entities
 * Mostly you'll want to use SCD2BaseEntity instead
 *
 * @param ID The type of the SCD2 row's ID (primary key)
 * @param K The type of the SCD2 row's business key
 */
interface SCD2Entity<ID, K> {
    var id: ID
    var businessKey: K
    var effectiveFrom: Instant
    var effectiveTo: Instant?
    var isCurrent: Boolean

    fun hasSameBusinessDataAs(other: SCD2Entity<ID, K>?): Bo<PERSON>an
}