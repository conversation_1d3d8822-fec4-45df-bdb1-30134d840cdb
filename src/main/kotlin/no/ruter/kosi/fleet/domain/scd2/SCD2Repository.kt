package no.ruter.kosi.fleet.domain.scd2

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.NoRepositoryBean

/**
 * Repository interface for SCD2 entities that provides common SCD2 operations
 * Extends both JpaRepository and our custom SCD2RepositoryBase for SCD2-specific functionality
 *
 * @param T The entity type
 * @param ID The type of the entity's ID
 * @param ID The type of the entity's business key
 */
@NoRepositoryBean
interface SCD2Repository<T : SCD2Entity<ID, K>, ID, K> : SCD2RepositoryBase<T, ID, K>, JpaRepository<T, ID>