package no.ruter.kosi.fleet.domain.scd2

import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleBusinessKey
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.NoRepositoryBean
import org.springframework.data.repository.query.Param
import java.time.Instant

/**
 * Base repository interface for SCD2 entities that provides default implementations
 * for common SCD2 operations
 *
 * @param T The entity type
 * @param ID The type of the entity's ID
 */
@NoRepositoryBean
interface SCD2RepositoryBase<T : SCD2Entity<ID, K>, ID, K> : JpaRepository<T, ID> {
    
    @Query("""
        SELECT e FROM #{#entityName} e 
        WHERE e.businessKey = :businessKey 
        AND e.effectiveFrom <= :timestamp 
        AND (e.effectiveTo IS NULL OR e.effectiveTo > :timestamp)
        ORDER BY e.effectiveFrom DESC
        LIMIT 1
    """)
    fun findVersionAtTime(businessKey: K, timestamp: Instant): T?

    fun findAllVersionsByBusinessKey(businessKey: K): List<T> =
        findByBusinessKeyOrderByEffectiveFrom(businessKey)

    fun findByBusinessKeyOrderByEffectiveFrom(businessKey: K): List<T>

    @Query("""
        SELECT DISTINCT e FROM #{#entityName} e
        WHERE e.businessKey = :key
        AND e.isCurrent = true
    """)
    fun findCurrentByBusinessKey(@Param("key") businessKey: K): T?

    @Query("""
        SELECT DISTINCT e FROM #{#entityName} e
        WHERE e.businessKey IN :keys
        AND e.isCurrent = true
    """)
    fun findCurrentByBusinessKeys(@Param("keys") businessKeys: Collection<K>): List<T>
}
