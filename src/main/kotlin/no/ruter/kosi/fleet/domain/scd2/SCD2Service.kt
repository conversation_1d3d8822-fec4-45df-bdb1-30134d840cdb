package no.ruter.kosi.fleet.domain.scd2

import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

open class SCD2Service<T : SCD2Entity<ID, K>, ID, K>(
    private val repository: SCD2RepositoryBase<T, ID, K>
) : ISCD2Service<T,ID,K>  {
    @Transactional
    override fun saveNewVersion(
        entity: T,
        now: Instant,
    ): T {
        var currentVersion = findCurrentByBusinessKey(entity.businessKey)
        if (!isInitialized(entity.effectiveFrom)) {
            entity.effectiveFrom = now
        }
        currentVersion?.let {
            if (entity.effectiveFrom.isBefore(it.effectiveFrom)) {
                throw IllegalArgumentException("Cannot create a version with effectiveFrom earlier than the earliest existing version")
            }
        }
        entity.isCurrent = true
        currentVersion = currentVersion?.apply {
            if (this != entity) {
                effectiveTo = entity.effectiveFrom
                isCurrent = false
                repository.save(this)
            }
        }
        return repository.save(entity)
    }

    @Transactional
    override fun saveNewVersions(
        entities: List<T>,
        timestamp: Instant,
    ): List<T> {
        if (entities.isEmpty()) {
            return emptyList()
        }
        val groupedEntities = entities.groupBy { it.businessKey}

        val currentVersionsList = repository.findCurrentByBusinessKeys(groupedEntities.keys)
        val currentVersions = currentVersionsList.associateBy { it.businessKey }
        val entitiesToSave = mutableListOf<T>()
        groupedEntities.forEach { (id, groupEntities) ->
            val newEntity = groupEntities.maxByOrNull {
                if (isInitialized(it.effectiveFrom)) it.effectiveFrom else timestamp
            } ?: return@forEach
            if (!isInitialized(newEntity.effectiveFrom)) {
                newEntity.effectiveFrom = timestamp
            }
            newEntity.isCurrent = true
            val currentVersion = currentVersions[id]
            currentVersion?.let {
                if (it != newEntity) {
                    it.effectiveTo = newEntity.effectiveFrom
                    it.isCurrent = false
                    entitiesToSave.add(it)
                }
            }
            entitiesToSave.add(newEntity)
        }
        entitiesToSave.forEach { entity ->
            if (entity is VehicleQualityEntity) {
                entity.fieldQualities.clear()
            }
        }
        return repository.saveAll(entitiesToSave).toList()
    }

    @Transactional
    override fun saveNewVersionsBulk(
        entities: List<T>,
        timestamp: Instant,
    ): List<T> {
        if (entities.isEmpty()) {
            return emptyList()
        }

        val groupedEntities = entities.groupBy { it.businessKey }
        val entitiesToSave = mutableListOf<T>()

        groupedEntities.forEach { (_, groupEntities) ->
            val newEntity = groupEntities.maxByOrNull {
                if (isInitialized(it.effectiveFrom)) it.effectiveFrom else timestamp
            } ?: return@forEach

            if (!isInitialized(newEntity.effectiveFrom)) {
                newEntity.effectiveFrom = timestamp
            }
            newEntity.isCurrent = true
            newEntity.effectiveTo = null

            entitiesToSave.add(newEntity)
        }

        entitiesToSave.forEach { entity ->
            if (entity is VehicleQualityEntity) {
                entity.fieldQualities.clear()
            }
        }

        return repository.saveAll(entitiesToSave).toList()
    }

    @Suppress("UNCHECKED_CAST")
    override fun findCurrentByBusinessKey(businessKey: K): T? {
        return repository.findCurrentByBusinessKey(businessKey)
    }

    @Suppress("UNCHECKED_CAST")
    override fun findVersionAtTime(
        businessKey: K,
        timestamp: Instant,
    ): T? {
        return repository.findVersionAtTime(businessKey, timestamp)
    }

    @Suppress("UNCHECKED_CAST")
    override fun findAllVersionsByBusinessKey(businessKey: K): List<T> {
        return repository.findAllVersionsByBusinessKey(businessKey)
    }

    override fun isInitialized(instant: Instant): Boolean =
        instant != Instant.EPOCH && instant.toEpochMilli() != 0L
}
