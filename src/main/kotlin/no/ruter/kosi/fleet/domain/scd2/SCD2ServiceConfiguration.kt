package no.ruter.kosi.fleet.domain.scd2

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataQualityIssueRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleRepository
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class SCD2ServiceConfiguration {
    @Bean
    fun eventDataSCD2Service(repository: SpringDataEventDataRepository):
        SCD2Service<EventData, Int, String> = SCD2Service(repository)

    @Bean("vehicleSCD2Service")
    fun vehicleSCD2Service(
        springDataVehicleRepository: SpringDataVehicleRepository
    ): SCD2Service<VehicleEntity, Int, String> {
        return SCD2Service(springDataVehicleRepository)
    }

    @Bean("vehicleQualitySCD2Service")
    fun vehicleQualitySCD2Service(
        springDataVehicleQualityRepository: SpringDataVehicleQualityRepository
    ): SCD2Service<VehicleQualityEntity, Int, String> {
        return SCD2Service(springDataVehicleQualityRepository)
    }

    @Bean("eventDataQualityIssueSCD2Service")
    fun eventDataQualityIssueSCD2Service(
        springDataEventDataQualityIssueRepository: SpringDataEventDataQualityIssueRepository
    ): SCD2Service<EventDataQualityIssue, Int, String> {
        return SCD2Service(springDataEventDataQualityIssueRepository)
    }
}
