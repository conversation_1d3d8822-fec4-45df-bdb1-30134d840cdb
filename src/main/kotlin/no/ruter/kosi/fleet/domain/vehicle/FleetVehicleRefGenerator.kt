package no.ruter.kosi.fleet.domain.vehicle

import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality

object FleetVehicleRefGenerator {
    fun generateFleetVehicleRef(fridaId: Int): Pair<String, VehicleFieldQuality> {
        val ref = "fleetref_$fridaId"
        return ref to VehicleFieldQuality(
            value = ref,
            type = QualityType.VALID,
            message = "Generated vehicle reference using Frida ID"
        )
    }
}
