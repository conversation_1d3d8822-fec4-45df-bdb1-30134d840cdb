package no.ruter.kosi.fleet.domain.vehicle

import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.fleet.infrastructure.kafka.GenericKafkaProducerService
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Service

@Service
class KafkaVehicleProducerService(
    kafkaTemplate: KafkaTemplate<String, VehicleV3>,
    @Value("\${spring.kafka.topics.vehicles}") topic: String,
    @Value("\${spring.kafka.publishing.enabled}") kafkaPublishingEnabled: <PERSON>olean,
) : GenericKafkaProducerService<String, VehicleV3>(kafkaTemplate, topic, kafkaPublishingEnabled)

