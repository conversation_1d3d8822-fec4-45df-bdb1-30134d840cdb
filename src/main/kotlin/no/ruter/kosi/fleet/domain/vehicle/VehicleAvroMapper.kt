package no.ruter.kosi.fleet.domain.vehicle

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntityMapper
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
object VehicleAvroMapper {
    @Autowired
    private lateinit var vehicleEntityMapper: VehicleEntityMapper

    fun buildVehicleAvroRecord(snapshot: VehicleEntity): Either<VehicleAvroMappingError, VehicleV3> {
        val mdcContext = MDC.getCopyOfContextMap()

        if (snapshot.identification.chassisNumber == null) {
            return Either.Left(
                VehicleAvroMappingError.MissingChassisNumberError(
                    vehicleId = snapshot.id,
                    message = "Vehicle ${snapshot.id} has no chassis number, cannot build Avro record",
                    mdc = mdcContext,
                )
            )
        }

        try {
            val entityData = vehicleEntityMapper.toAvro(snapshot)
            return entityData.right()
        } catch (e: Exception) {
            return VehicleAvroMappingError.RecordBuildError(
                vehicleId = snapshot.id,
                vehicleRef = snapshot.identification.chassisNumber,
                cause = e,
                mdc = mdcContext,
                message = "Unknown error when building Avro record for vehicle ${snapshot.id}",
            ).left()
        }
    }
}

sealed class VehicleAvroMappingError {
    abstract val vehicleId: Int
    abstract val vehicleRef: String?

    abstract val cause: Throwable?

    abstract val mdc: Map<String, String>

    data class MissingChassisNumberError(
        val message: String,
        override val vehicleId: Int,
        override val vehicleRef: String? = null,
        override val cause: Throwable? = null,
        override val mdc: Map<String, String> = emptyMap(),
    ) : VehicleAvroMappingError()

    data class RecordBuildError(
        val message: String,
        override val vehicleId: Int,
        override val vehicleRef: String? = null,
        override val cause: Throwable? = null,
        override val mdc: Map<String, String> = emptyMap(),
    ) : VehicleAvroMappingError()
}