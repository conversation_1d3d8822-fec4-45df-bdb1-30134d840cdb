package no.ruter.kosi.fleet.domain.vehicle

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.raise.either
import arrow.core.raise.ensure
import arrow.core.raise.mapOrAccumulate
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.domain.vehicle.VehicleAvroMapper.buildVehicleAvroRecord
import no.ruter.kosi.fleet.domain.vehicle.capacity.VehicleCapacityService.Companion.captureMdcContext
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDtoMapper
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.logging.LogCodes
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant

@Service
@Primary
class VehicleService(
    private val reconstructionService: VehicleReconstructionService,
    @Qualifier("vehicleSCD2Service") private val vehicleSCD2Service: ISCD2Service<VehicleEntity, Int, String>,
    @Qualifier("vehicleQualitySCD2Service") private val vehicleQualitySCD2Service: ISCD2Service<VehicleQualityEntity, Int, String>,
    @Qualifier("auroraVehicleRepository") private val vehicleRepository: AuroraVehicleRepository,
    private val vehicleQualityRepository: AuroraVehicleQualityRepository,
    private val kafkaVehicleProducerService: KafkaVehicleProducerService,
    private val vehicleDtoMapper: VehicleDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(VehicleService::class.java)

    /**
     * reconstructs the vehicle and its quality, writes both as SCD2 and JSONB aggregates
     */
    @Transactional
    fun reconstructAndSave(
        vehicle: InternalVehicle,
        timestamp: Instant? = Instant.now()
    ) {
        MdcUtils.setVehicleRef(vehicle.vehicleRef)
        logger.debug("Reconstructing and saving vehicle ${vehicle.vehicleRef}")

        val result: Pair<VehicleDto, VehicleQualityDto> =
            reconstructionService.reconstructVehicle(vehicle, timestamp)
                ?: run {
                    logger.info("No data for vehicle ${vehicle.vehicleRef} at $timestamp")
                    return
                }

        val (dto, qualityDto) = result
        val now = Instant.now()

        val vehicleEntity = vehicleDtoMapper.toEntity(dto).apply {
            effectiveFrom = now
            businessKey = FleetVehicleRefGenerator.generateFleetVehicleRef(vehicle.sourceId).first
        }

        val currentlyStoredVehicle = vehicleSCD2Service.findCurrentByBusinessKey(vehicleEntity.businessKey)
        val savedVehicle = if (currentlyStoredVehicle == null || !currentlyStoredVehicle.hasSameBusinessDataAs(vehicleEntity)) {
            logger.debug("Saving new version of vehicle ${vehicleEntity.vehicleRef}")
            vehicleSCD2Service.saveNewVersion(vehicleEntity, now)
        } else {
            currentlyStoredVehicle
        }

        val qualEntity = VehicleQualityEntity(
            vehicleId = savedVehicle.id,
            businessKey = FleetVehicleRefGenerator.generateFleetVehicleRef(vehicle.sourceId).first
        )

        qualityDto.fieldQualities.forEach { fq ->
            qualEntity.fieldQualities.add(
                VehicleFieldQualityEntity(
                    vehicleQuality = qualEntity,
                    fieldName = fq.fieldName,
                    fieldValue = fq.fieldValue,
                    qualityType = fq.qualityType,
                    message = fq.message,
                )
            )
        }

        vehicleQualitySCD2Service.saveNewVersion(qualEntity, now)

        logger.info("Saved new vehicle ${savedVehicle.id} with quality data")
    }


    fun publishVehiclesToKafka(onVehicleProcessed: ((count: Int) -> Unit)? = null): Either<NonEmptyList<VehiclePublishError>, Unit> =
        either {
            val vehicles = vehicleRepository.findAllCurrent()

            mapOrAccumulate(vehicles.withIndex()) { (idx, entity) ->
                MdcUtils.setVehicleRef(entity.identification.chassisNumber)

                val record = buildVehicleAvroRecord(entity).mapLeft {
                    VehiclePublishError.RecordBuildError(
                        vehicleId = entity.internalVehicleId,
                        vehicleRef = entity.identification.chassisNumber,
                        cause = it.cause ?: RuntimeException("Unknown error during Avro record build"),
                        mdc = captureMdcContext()
                    )
                }.bind()

                ensure(!entity.identification.chassisNumber.isNullOrBlank()) {
                    VehiclePublishError.MissingChassisNumberError(
                        vehicleId = entity.internalVehicleId,
                        message = "Missing chassis number",
                        mdc = captureMdcContext()
                    )
                }
                val chassisNumber = entity.identification.chassisNumber!!

                Either.catch {
                    kafkaVehicleProducerService.sendMessage(chassisNumber, record)
                }.mapLeft { ex ->
                    VehiclePublishError.KafkaPublishError(
                        key = chassisNumber,
                        cause = ex,
                        mdc = captureMdcContext()
                    )
                }.bind()

                onVehicleProcessed?.invoke(idx + 1)
            }

            Unit
        }

    fun getLatestVehicle(vehicleRef: String): VehicleDto? {
        MdcUtils.setVehicleRef(vehicleRef)
        val latestVehicle = vehicleRepository.findCurrentByVehicleRef(vehicleRef)
        return if (latestVehicle != null) {
            vehicleDtoMapper.toDto(latestVehicle)
        } else {
            null
        }
    }

    fun getAllVehicles(): List<VehicleDto> =
        vehicleRepository.findAllCurrent()
            .map { vehicleDtoMapper.toDto(it) }

    fun getVehiclesByContract(contractId: Int): List<VehicleDto> =
        vehicleRepository.findAll()
            .filter { it.contractor?.id == contractId }
            .map { vehicleDtoMapper.toDto(it) }

    fun getReconstructedVehicleAtTime(
        vehicleRef: String,
        timestamp: Instant? = Instant.now()
    ): Either<VehicleError, Pair<VehicleDto, VehicleQualityDto>> = try {
        reconstructionService.reconstructVehicle(vehicleRef, timestamp)
?.let { Either.Right(it) }
            ?: Either.Left(VehicleError.NotFound)
    } catch (e: NoSuchElementException) {
        Either.Left(VehicleError.NotFound)
    } catch (e: Exception) {
        Either.Left(VehicleError.Unexpected("Failed to reconstruct vehicle by ref '$vehicleRef': ${e.message}", e))
    }

    @Transactional(readOnly = true)
    fun getAllSnapshotsWithRelationships(): List<VehicleEntity> =
        vehicleRepository.findAllWithRelationships()

    @Transactional(readOnly = true)
    fun getAllVersionsWithRelationships(): List<VehicleEntity> =
        vehicleRepository.findAllVersionsWithRelationships()

    sealed class VehicleError {
        object NotFound : VehicleError()
        data class Unexpected(val message: String, val cause: Throwable? = null) : VehicleError()
    }

    sealed class VehiclePublishError {
        abstract val cause: Throwable?
        abstract val mdc: Map<String, String>

        data class FetchError(
            val message: String,
            override val cause: Throwable,
            override val mdc: Map<String, String> = emptyMap()
        ) : VehiclePublishError()

        data class RecordBuildError(
            val vehicleId: Int,
            val vehicleRef: String?,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class MissingChassisNumberError(
            val vehicleId: Int,
            val message: String,
            override val cause: Throwable? = null,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class KafkaPublishError(
            val key: String,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()

        data class UnexpectedError(
            val message: String,
            override val cause: Throwable?,
            override val mdc: Map<String, String>
        ) : VehiclePublishError()
    }
}
