package no.ruter.kosi.fleet.domain.vehicle.capacity

import no.ruter.kosi.fleet.infrastructure.kafka.GenericKafkaProducerService
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.EntityVehicleCapacityNpraDetails
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Service

@Service
class KafkaVehicleCapacityProducerService(
    kafkaTemplate: KafkaTemplate<String, EntityVehicleCapacityNpraDetails>,
    @Value("\${spring.kafka.topics.entityVehicleCapacityNpraDetails}") topic: String,
    @Value("\${spring.kafka.publishing.enabled}") kafkaPublishingEnabled: Boolean,
) : GenericKafkaProducerService<String, EntityVehicleCapacityNpraDetails>(kafkaTemplate, topic, kafkaPublishingEnabled)

