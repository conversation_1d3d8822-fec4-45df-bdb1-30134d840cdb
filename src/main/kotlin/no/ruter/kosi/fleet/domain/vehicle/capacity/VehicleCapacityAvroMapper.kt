package no.ruter.kosi.fleet.domain.vehicle.capacity

import arrow.core.Either
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.*
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import java.time.Instant
import java.util.*

object VehicleCapacityAvroMapper {
    fun buildCapacityAvroRecord(
        vehicleEntity: VehicleEntity
    ): Either<VehicleCapacityAvroMappingError, EntityVehicleCapacityNpraDetails> {
        val now = Instant.now().toString()

        val entityHeader = EntityHeader(
            eventTimestamp = now,
            publishedTimestamp = now,
            ownerId = UUID.randomUUID().toString(),
            traceId = "",
            originId = "",
            publisherId = ""
        )

        val passengerCapacity = PassengerCapacity(
            numberOfPassengerSeated = vehicleEntity.capacity?.seated,
            predictedTotalNumberOfPassengers2PerM2 = vehicleEntity.capacity?.predictedTotal2PerM2,
            predictedTotalNumberOfPassengers3PerM2 = vehicleEntity.capacity?.predictedTotal3PerM2,
            predictedTotalNumberOfPassengers4PerM2 = vehicleEntity.capacity?.predictedTotal4PerM2,
        )

        val vehicleCapacityNpraDetails = VehicleCapacityNpraDetails(
            vehicleRef = vehicleEntity.vehicleRef,
            sisInternalId = vehicleEntity.identification.vehicleSISId,
            contract = vehicleEntity.contractor?.let { Contract(it.id, it.name) },
            vehicleLength = vehicleEntity.technical.dimensions.lengthInMeters,
            standingArea = vehicleEntity.capacity?.standingArea,
            wheelchairSpaces = vehicleEntity.features.accessibility.wheelchairPlaces,
            totalNumberOfPassengers = vehicleEntity.capacity?.total,
            passengerCapacity = passengerCapacity
        )

        return Either.Right(EntityVehicleCapacityNpraDetails(entityHeader, vehicleCapacityNpraDetails))
    }
}

sealed class VehicleCapacityAvroMappingError {
    abstract val vehicleId: Int
    abstract val capacityId: Int
    abstract val vehicleRef: String?

    abstract val cause: Throwable?

    abstract val mdc: Map<String, String>

    data class MissingChassisNumberError(
        val message: String,
        override val capacityId: Int,
        override val vehicleId: Int,
        override val vehicleRef: String? = null,
        override val cause: Throwable? = null,
        override val mdc: Map<String, String> = emptyMap(),
    ) : VehicleCapacityAvroMappingError()

    data class RecordBuildError(
        val message: String,
        override val capacityId: Int,
        override val vehicleId: Int,
        override val vehicleRef: String? = null,
        override val cause: Throwable? = null,
        override val mdc: Map<String, String> = emptyMap(),
    ) : VehicleCapacityAvroMappingError()
}