package no.ruter.kosi.fleet.domain.vehicle.capacity

import arrow.core.Either
import arrow.core.NonEmptyList
import arrow.core.flatten
import arrow.core.raise.either
import arrow.core.raise.ensure
import arrow.core.raise.mapOrAccumulate
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.EntityVehicleCapacityNpraDetails
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service

@Service
@Primary
class VehicleCapacityService(
    private val vehicleRepository: AuroraVehicleRepository,
    private val kafkaVehicleCapacityProducerService: KafkaVehicleCapacityProducerService,
) {
    private val logger = LoggerFactory.getLogger(VehicleCapacityService::class.java)

    fun publishAllCapacitiesToKafka(): Either<NonEmptyList<CapacityProcessingError>, Unit> =
        either {
            mapOrAccumulate(vehicleRepository.findAll().filter { it.capacity != null }) { vehicle ->
                processVehicle(vehicle).bind()
            }

            Unit
        }

    private fun processVehicle(vehicle: VehicleEntity): Either<CapacityProcessingError, Unit> =
        either {
            val avroRecord = VehicleCapacityAvroMapper.buildCapacityAvroRecord(vehicle)
                .mapLeft { CapacityProcessingError.RecordCreationError(it) }
                .bind()

            val keys = listOfNotNull(
                vehicle.identification.chassisNumber, // this should be vehicleref technically
                vehicle.identification.vehicleSISId
            )

            val errors = keys.flatMap { key ->
                publishToKafka(avroRecord, key)
            }

            ensure(errors.isEmpty()) {
                CapacityProcessingError.PreparationError(
                    message = "Failed to publish capacity for ${vehicle.vehicleRef}",
                    cause = null,
                    mdc = captureMdcContext()
                )
            }
        }.mapLeft {
            CapacityProcessingError.UnexpectedError(
                message = "Unexpected exception during vehicle processing",
                cause = it.cause!!,
                mdc = captureMdcContext()
            )
        }

    private fun publishToKafka(
        avroRecord: EntityVehicleCapacityNpraDetails,
        key: String,
    ): List<CapacityProcessingError> {
        return kafkaVehicleCapacityProducerService.sendMessage(key, avroRecord).fold(
            { kafkaError ->
                listOf(
                    CapacityProcessingError.PublishingError(
                        key = kafkaError.key.toString(),
                        cause = kafkaError.cause ?: RuntimeException("Kafka publishing error"),
                        mdc = captureMdcContext()
                    )
                )
            },
            {
                emptyList()
            }
        )
    }

    sealed class CapacityProcessingError {
        abstract val message: String
        abstract val cause: Throwable?
        abstract val mdc: Map<String, String>

        data class PreparationError(
            override val message: String,
            override val cause: Throwable?,
            override val mdc: Map<String, String>
        ) : CapacityProcessingError()

        data class RecordCreationError(
            val mappingError: VehicleCapacityAvroMappingError
        ) : CapacityProcessingError() {
            override val message: String get() = "Failed to create Avro record"
            override val cause: Throwable? get() = mappingError.cause
            override val mdc: Map<String, String> get() = mappingError.mdc
        }

        data class PublishingError(
            val key: String,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : CapacityProcessingError() {
            override val message: String get() = "Failed to publish to Kafka with key: $key"
        }

        data class UnexpectedError(
            override val message: String,
            override val cause: Throwable,
            override val mdc: Map<String, String>
        ) : CapacityProcessingError()
    }

    companion object {
        fun captureMdcContext(): Map<String, String> {
            return MDC.getCopyOfContextMap() ?: emptyMap()
        }
    }
}