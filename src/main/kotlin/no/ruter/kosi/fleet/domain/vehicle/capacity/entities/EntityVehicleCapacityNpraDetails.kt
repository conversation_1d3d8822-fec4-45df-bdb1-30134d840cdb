package no.ruter.kosi.fleet.domain.vehicle.capacity.entities

import kotlinx.serialization.Serializable

@Serializable
data class EntityHeader(
    val eventTimestamp: String,
    val receivedTimestamp: String? = null,
    val publishedTimestamp: String,
    val ownerId: String,
    val traceId: String,
    val originId: String,
    val publisherId: String,
)

@Serializable
data class EntityVehicleCapacityNpraDetails(
    val entityHeader: EntityHeader,
    val vehicleCapacityNpraDetails: VehicleCapacityNpraDetails,
)

@Serializable
data class VehicleCapacityNpraDetails(
    val vehicleRef: String,
    val sisInternalId: String? = null,
    val contract: Contract? = null,
    val vehicleLength: Double?,
    val standingArea: Double?,
    val wheelchairSpaces: Int?,
    val totalNumberOfPassengers: Int?,
    val passengerCapacity: PassengerCapacity,
)

@Serializable
data class Contract(
    val id: Int,
    val name: String,
)

@Serializable
data class PassengerCapacity(
    val numberOfPassengerSeated: Int?,
    val predictedTotalNumberOfPassengers2PerM2: Int?,
    val predictedTotalNumberOfPassengers3PerM2: Int?,
    val predictedTotalNumberOfPassengers4PerM2: Int?,
)
