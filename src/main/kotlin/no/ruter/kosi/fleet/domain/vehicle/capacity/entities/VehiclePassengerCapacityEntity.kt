package no.ruter.kosi.fleet.domain.vehicle.capacity.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table

@Entity
@Table(name = "vehicle_passenger_capacity")
data class VehiclePassengerCapacityEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val vehiclePassengerCapacityId: Int = 0,
    val standingArea: Double = 0.0,
    @Column(name = "predicted_total_passengers_2_per_m2")
    val predictedTotalNumberOfPassengers2PerM2: Int = 0,
    @Column(name = "predicted_total_passengers_3_per_m2")
    val predictedTotalNumberOfPassengers3PerM2: Int = 0,
    @Column(name = "predicted_total_passengers_4_per_m2")
    val predictedTotalNumberOfPassengers4PerM2: Int = 0,
){

    companion object {
    }
}