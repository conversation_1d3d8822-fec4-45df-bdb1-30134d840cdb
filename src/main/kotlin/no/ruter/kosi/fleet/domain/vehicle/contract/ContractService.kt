package no.ruter.kosi.fleet.domain.vehicle.contract

import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraContractRepository
import org.springframework.stereotype.Service

@Service
class ContractService(
    private val contractRepository: AuroraContractRepository,
) {

    fun getContractors(): List<Contract> {
        return contractRepository.findAll()
    }
}
