package no.ruter.kosi.fleet.domain.vehicle.contract

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import java.time.Instant

@Entity
@Table(name = "contract_events")
class ContractorEvent(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Int = 0,
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id", nullable = false)
    val vehicle: InternalVehicle,
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_id", nullable = false)
    val contract: Contract,
    @Column(nullable = false)
    val timestamp: Instant = Instant.now(),
)
