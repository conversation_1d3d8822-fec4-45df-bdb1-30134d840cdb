package no.ruter.kosi.fleet.domain.vehicle.contract.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.persistence.*

@Entity
@Table(name = "contracts")
data class Contract(
    @Id
    @JsonIgnore // TODO make a proper view entity for this later
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val contractId: Int = 0,

    @JsonProperty("id")
    val contractIdInFrida: Int,
    val name: String,
) {
    companion object {
        fun fromFridaString(input: String): Contract {
            val content = input.trim().removePrefix("{").removeSuffix("}")
            val keyValuePairs = content.split(",").associate {
                val (key, value) = it.split("=")
                key.trim() to value.trim()
            }
            return Contract(
                contractIdInFrida = keyValuePairs["Id"]?.toInt() ?: throw IllegalArgumentException("Missing Id"),
                name = keyValuePairs["Name"] ?: throw IllegalArgumentException("Missing Name")
            )
        }
    }
}
