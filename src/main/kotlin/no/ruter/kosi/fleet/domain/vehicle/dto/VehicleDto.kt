package no.ruter.kosi.fleet.domain.vehicle.dto

import com.fasterxml.jackson.annotation.JsonProperty
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import java.time.Instant
import java.time.LocalDate

// TODO pull all this out into their own files
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
abstract class VehicleDtoMapper {
    abstract fun toDto(src: VehicleEntity): VehicleDto

    open fun toEntity(src: VehicleDto): VehicleEntity {
        return VehicleEntity(
            id = 0,
            internalVehicleId = src.internalVehicleId,
            vehicleRef = src.vehicleRef,
            contractor = src.contractor,
            identification = src.identification,
            registration = src.registration,
            features = src.features,
            capacity = src.capacity,
            technical = src.technical,
            effectiveFrom = src.effectiveFrom,
            effectiveTo = src.effectiveTo,
            businessKey = src.vehicleRef,
        )
    }
}

data class VehicleDto(
    val vehicleId: Int,
    val internalVehicleId: Int,
    val vehicleRef: String,

    val contractor: ContractorDto?,
    val identification: IdentificationDto,
    val registration: RegistrationDto,
    val features: FeaturesDto,
    val capacity: CapacityDto?,
    val technical: TechnicalDto,

    val effectiveFrom: Instant,
    val effectiveTo: Instant?,
)

data class ContractorDto(
    val id: Int,
    val contractIdInFrida: Int,
    val name: String,
)

enum class BusType { REGIONBUSS, BYBUSS, MINIBUSS }
data class IdentificationDto(
    val vehicleId: Int, // this is already mostly covered by internalvehicleid
    val vehicleSISId: String?,
    val publicId: String,
    val licensePlate: String,
    val chassisNumber: String?,
    val manufacturer: ManufacturerDto,
    val modelYear: String,
    val model: ModelInfoDto,
    val color: String,
    val busType: BusType
)

data class ManufacturerDto(
    val chassis: String,
    val brand: String,
    val producer: String,
    val chassisManufacturer: String
)

data class ModelInfoDto(
    val typeDesignation: String,
    val commercialName: String
)

data class RegistrationDto(
    val registrationDate: LocalDate?,
    val firstRegistrationNorwayDate: String,
    val firstRegistrationOnOwnership: String,
    val status: RegistrationStatus,
    val use: VehicleUse,
    val businessCode: String,
    val businessDescription: String,
    val periodicControl: PeriodicControlDto,
    val permitPeriod: PermitPeriodDto,
    val taxiCountyLetter: String,
)

enum class RegistrationStatus { REGISTRERT, AVSKILT, SUSPENDERT, UKJENT }
enum class VehicleUse { UTLEIEVOGN, EGET_BRUK, ANNET_BRUK, UKJENT }

data class PeriodicControlDto(
    val lastApproved: String,
    val nextDue: String
)

data class PermitPeriodDto(
    val start: String,
    val end: String
)

data class FeaturesDto(
    val comfort: ComfortDto,
    val accessibility: AccessibilityDto,
    val safety: SafetyDto,
    val signage: SignageDto,
    val apc: APCInfoDto,
    val other: OtherFeaturesDto
)

data class ComfortDto(
    val airConditioning: Boolean,
    val heater: Boolean?,
    val readingLights: Boolean?,
    val armrest: Boolean?,
    val backrest: Boolean?,
    val foldingSeats: Int?
)

data class AccessibilityDto(
    val lowFloor: Boolean,
    val lowEntry: String,
    val lift: Boolean?,
    val ramp: Boolean,
    val kneeling: Boolean?,
    val accessibilityRamp: Boolean?,
    val wheelchairPlaces: Int?,
    val walkerPlaces: Int?,
    val covid19CapacityLimit: Int?
)

data class SafetyDto(
    val seatBelts: String,
    val seatBeltReminder: Boolean,
    val visualBeltReminder: Boolean?,
    val airbagPassenger: Boolean?,
    val alcoLock: String,
    val fireExtinguisher: Boolean,
    val fireExtinguisherInEngineCompartment: Boolean,
    val defibrillators: Boolean?,
    val cameraSurveillanceSafety: Boolean,
    val cameraSurveillanceTrafficSafety: Boolean
)

data class APCInfoDto(
    val count: Int,
    val type: String
)

data class SignageDto(
    val destinationSign: String,
    val internalInformationSign: Boolean,
    val externalDestinationCall: Boolean?,
    val stopAnnouncements: Boolean
)

data class OtherFeaturesDto(
    val bikeRack: Boolean?,
    val boosterSeat: Boolean?,
    val childSeats: Boolean?,
    val strollerPlaces: Int,
    val passengerCounter: Boolean,
    val ticketing: TicketingDto,
    val vehicleComputer: String
)

data class TicketingDto(
    val cardReaders: Int,
    val machineNumber: String,
    val system: String
)

data class CapacityDto(
    val seated: Int?,
    val standing: Int?,
    val total: Int?,
    val standingArea: Double?,
    val predictedTotal2PerM2: Int?, // TODO, dunno why it's like this, why can't we have predictedTotalPerM2 and let the consumer multiply?
    val predictedTotal3PerM2: Int?,
    val predictedTotal4PerM2: Int?,
)

data class TechnicalDto(
    val technicalApproval: TechnicalApprovalDto,
    val dimensions: DimensionsDto,
    val weights: WeightsDto,
    val axles: AxlesDto,
    val wheelsAndBrakes: WheelsAndBrakesDto,
    val engineAndDrivetrain: EngineAndDrivetrainDto,
    val emissionsAndEnvironment: EmissionsAndEnvironmentDto,
    val doors: String
)

data class TechnicalApprovalDto(
    val firstApproval: FirstApprovalDto,
    val technicalApproval: FirstApprovalDto,
    val classification: ClassificationDto,
    val requirements: String
)

data class FirstApprovalDto(
    val registeredDate: String,
    val approvalId: String,
    val type: ApprovalType,
    val validFrom: String,
    val customsReference: String
)

enum class ApprovalType { ENKELTGODKJENNING, GODKJENNING, REFUSJON }

data class ClassificationDto(
    val feeCode: Int,
    val technicalCode: String,
    val specialCharacteristics: String
)

data class DimensionsDto(
    val lengthInMeters: Double?,
    val widthInMeters: Double?,
    val heightInMeters: Double?,
)

data class WeightsDto(
    val curbWeight: Double?,
    val totalWeight: Double,
    val unitWeights: UnitWeightsDto,
    val payload: Int,
    val grossVehicleWeight: Int
)

data class UnitWeightsDto(
    val frontAxle: Int,
    val rearAxleGroup: Int,
    val total: Int
)

data class AxlesDto(
    val count: Int,
    val groups: List<AxleGroupDto>
)

data class AxleGroupDto(
    val id: Int,
    val placement: Int,
    val allowedLoad: Int,
    val axles: List<AxleDetailDto>
)

data class AxleDetailDto(
    val placement: Int,
    val drivable: Boolean,
    val distanceToNextAxle: Int?,
    val weight: Int?,
    val airSuspension: Boolean
)

data class WheelsAndBrakesDto(
    val tyreRimCombinations: List<TireRimCombinationDto>,
    val trailerBrakeConnection: List<String>
)

data class TireRimCombinationDto(
    val axleId: Int,
    val tyre: String,
    val rim: String,
    val loadIndex: Int,
    val speedCode: String,
    val twin: Boolean
)

data class EngineAndDrivetrainDto(
    val engineTypeId: String,
    val primaryCategory: String,
    val hybridVehicle: Boolean,
    val gearbox: GearboxDto,
    val maxSpeed: List<Double>,
    val motor: MotorDto,
    val noise: NoiseDto
)

data class GearboxDto(
    val type: String,
    val ratios: List<Double>
)

data class MotorDto(
    val maxNetPower: Double,
    val displacement: Int,
    val fuel: String,
    val particleFilter: Boolean
)

data class NoiseDto(
    val interior_dB: Int,
    val external_dB: Int
)

data class EmissionsAndEnvironmentDto(
    val environmentalClass: String,
    val euroClass: String,
    val emissionUnitId: Int,
    val emissionValues: EmissionValuesDto,
    val certification: CertificationDto,
    val energyUse: Double?,
    val consumptionMixedDriving: Double?,
    val eevVehicle: Boolean
)

data class EmissionValuesDto(
    @JsonProperty("hc")
    val HC: Double,
    @JsonProperty("nox")
    val NOx: Double,
    @JsonProperty("pm")
    val PM: Double
)

data class CertificationDto(
    val certificationUnitId: Int,
    val certificationValueHC: Double,
    val certificationValueNOx: Double,
    val certificationValuePM: Double
)
