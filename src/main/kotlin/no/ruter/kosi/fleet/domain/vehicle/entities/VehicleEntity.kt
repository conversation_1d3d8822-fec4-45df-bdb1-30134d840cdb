package no.ruter.kosi.fleet.domain.vehicle.entities

import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2BaseEntity
import no.ruter.kosi.fleet.domain.vehicle.dto.*
import no.ruter.kosi.fleet.domain.vehicle.entities.converters.*
import org.hibernate.annotations.Type
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import java.time.Instant

enum class BusType { REGIONBUSS, BYBUSS, MINIBUSS, SKOLEBUSS, TURISTBUSS }

@Entity
@Table(name = "vehicles")
data class VehicleEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, insertable = false, updatable = false)
    override var id: Int = 0,

    @Column(name = "internal_vehicle_id", nullable = false)
    val internalVehicleId: Int,

    @Column(name = "vehicle_ref", nullable = false)
    val vehicleRef: String,

    @Convert(converter = ContractorConverter::class)
    @Type(JsonType::class)
    @Column(name = "contractor", columnDefinition = "jsonb")
    val contractor: ContractorDto?,

    @Convert(converter = IdentificationConverter::class)
    @Type(JsonType::class)
    @Column(name = "identification", columnDefinition = "jsonb")
    val identification: IdentificationDto,

    @Convert(converter = RegistrationConverter::class)
    @Type(JsonType::class)
    @Column(name = "registration", columnDefinition = "jsonb")
    val registration: RegistrationDto,

    @Convert(converter = FeaturesConverter::class)
    @Type(JsonType::class)
    @Column(name = "features", columnDefinition = "jsonb")
    val features: FeaturesDto,

    @Convert(converter = CapacityConverter::class)
    @Type(JsonType::class)
    @Column(name = "capacity", columnDefinition = "jsonb")
    val capacity: CapacityDto? = null,

    @Convert(converter = TechnicalConverter::class)
    @Type(JsonType::class)
    @Column(name = "technical", columnDefinition = "jsonb")
    val technical: TechnicalDto,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "vehicle_quality_id", nullable = true)
    var vehicleQuality: VehicleQualityEntity? = null,

    @Column(name = "business_key")
    override var businessKey: String,

    @Column(name = "is_current", nullable = false)
    override var isCurrent: Boolean = true,

    @Column(name = "effective_from", nullable = false)
    override var effectiveFrom: Instant,

    @Column(name = "effective_to", nullable = true)
    override var effectiveTo: Instant?,
) : SCD2BaseEntity<Int, String>()

// If the mapper needs to be extended with other @Mapping definitions, move this to a separate file.
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface VehicleEntityMapper {
    fun toAvro(src: VehicleEntity): VehicleV3
    fun toEntity(src: VehicleV3): VehicleEntity
}
