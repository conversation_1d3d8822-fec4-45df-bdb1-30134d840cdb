package no.ruter.kosi.fleet.domain.vehicle.entities.converters

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.persistence.AttributeConverter

abstract class JsonConverter<T : Any>(
    protected val objectMapper: ObjectMapper,
    private val dtoClass: Class<T>
) : AttributeConverter<T, String> {

    override fun convertToDatabaseColumn(attribute: T?): String? =
        attribute?.let { objectMapper.writeValueAsString(it) }

    override fun convertToEntityAttribute(dbData: String?): T? =
        dbData?.let { objectMapper.readValue(it, dtoClass) }
}