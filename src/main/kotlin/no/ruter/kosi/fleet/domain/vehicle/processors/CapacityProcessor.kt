package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.CapacityDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.math.roundToInt
import kotlin.reflect.KClass

@Component
@DependsOn(
    VehicleRefProcessor::class,
    StandingAreaProcessor::class,
    NumberOfPassengersSeatedProcessor::class,
    StandingPlacesProcessor::class,
    TotalNumberPassengersProcessor::class,
    VehicleLengthProcessor::class
)
class CapacityProcessor : BaseFieldProcessor("capacity") {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val vehicleRef = processorResults[VehicleRefProcessor::class]?.value as? String
        val seats = processorResults[SeatsProcessor::class]?.value as? Int
        val standing = processorResults[StandingPlacesProcessor::class]?.value as? Int
        val standingArea = processorResults[StandingAreaProcessor::class]?.value as? Double
        val total = processorResults[TotalNumberPassengersProcessor::class]?.value as? Int
        val vehicleLength = processorResults[VehicleLengthProcessor::class]?.value as? Double

        // TODO could pull this into an "IsTramProcessor"?
        val isTram = listOf("SL79","SL95","SL18").any { vehicleRef?.startsWith(it) == true }
        if (!isTram) {
            logger.debug("$vehicleRef is not a tram, seats: $seats, vehicleLength: $vehicleLength")
            // we only need to check missing critical data for buses for now
            if (vehicleLength == null && lastSnapshot?.technical?.dimensions?.lengthInMeters == null) {
                // no vehicle length, but we can still return seats and total
                val actualSeats = seats ?: 0
                val partialCapacity = CapacityDto(
                    seated = actualSeats,
                    standing = null,
                    total = actualSeats,
                    standingArea = null,
                    predictedTotal2PerM2 = null,
                    predictedTotal3PerM2 = null,
                    predictedTotal4PerM2 = null
                )
                return ProcessedField(
                    originProcessor = this.javaClass.kotlin,
                    value = partialCapacity,
                    quality =
                        VehicleFieldQuality(
                            value = partialCapacity,
                            type = QualityType.ENRICHED_VALUE,
                            message = "Computed capacity from seats only",
                        ),
                ).right()
            }
        } else {
            logger.debug("$vehicleRef is a tram, seats: $seats, vehicleLength: $vehicleLength")
        }

        val capacity =
            CapacityDto(
                seated = seats,
                standing = standing,
                total = total,
                standingArea = standingArea,
                predictedTotal2PerM2 = seats?.let {standingArea?.let {seats + (standingArea * 2).roundToInt()}}, // eh 🤷‍♂️
                predictedTotal3PerM2 = seats?.let {standingArea?.let {seats + (standingArea * 3).roundToInt()}},
                predictedTotal4PerM2 = seats?.let {standingArea?.let {seats + (standingArea * 4).roundToInt()}},
            )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = capacity,
            quality =
                VehicleFieldQuality(
                    value = capacity,
                    type = QualityType.ENRICHED_VALUE,
                    message = "Calculated", // TODO maybe say how and from what data
                ),
        ).right()
    }
}
