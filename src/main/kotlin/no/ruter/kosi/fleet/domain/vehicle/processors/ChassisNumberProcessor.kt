package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class ChassisNumberProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {

        val fieldNames =
            listOf(
                "chassiNumber",
                "customAttr_ChassiNumber",
            )

        val newValue = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
        lastSnapshot?.internalVehicleId ?: 0

        return validateStringField(
            newValue?.fieldValue,
            processor = this.javaClass.kotlin,
        )
            .fold(
                { error ->
                    if (lastSnapshot?.identification?.chassisNumber != null) {
                        ProcessedField(
                            originProcessor = this.javaClass.kotlin,
                            value = lastSnapshot.identification.chassisNumber,
                            quality =
                                VehicleFieldQuality(
                                    value = lastSnapshot.identification.chassisNumber,
                                    type = QualityType.STALE_VALUE,
                                    message = "Using fallback value due to error: ${error.getErrorMessage()}",
                                ),
                        ).right()
                    } else {
                        // critical, propagate error
                        error.left()
                    }
                },
                { quality ->
                    val originalValue = quality.value.toString()
                    val upperCaseValue = originalValue.uppercase().replace(" ", "")
                    val newMessage = if (originalValue != upperCaseValue) "Enriched: Converted to uppercase" else quality.message
                    val enrichedQuality = quality.copy(
                        value = upperCaseValue,
                        message = newMessage,
                    )
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = upperCaseValue,
                        quality = enrichedQuality,
                    ).right()
                },
            )
    }
}
