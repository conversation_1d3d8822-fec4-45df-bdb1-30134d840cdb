package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class ColorProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldNames = listOf(
            "customAttr_Utvendig_lakk",
        )

        val colorEventData = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
        val rawColor = colorEventData?.fieldValue

        if (rawColor.isNullOrBlank()) {
            // Try fallback from snapshot
            val fallbackColor = lastSnapshot?.identification?.color
            if (!fallbackColor.isNullOrBlank()) {
                val processedColor = parseColor(fallbackColor)
                return ProcessedField(
                    originProcessor = this.javaClass.kotlin,
                    value = processedColor,
                    quality = VehicleFieldQuality(
                        value = processedColor,
                        type = QualityType.STALE_VALUE,
                        message = "Using fallback color value from snapshot"
                    )
                ).right()
            }

            return Either.Left(
                ProcessingError.MissingRequiredData(
                    processor = this.javaClass.kotlin,
                    message = "No color data found"
                )
            )
        }

        val processedColor = parseColor(rawColor)

        val (finalColor, qualityType, message) = when (processedColor) {
            null -> Triple(
                "",
                QualityType.UNKNOWN_VALUE,
                "Could not determine color from value '$rawColor'"
            )
            rawColor -> Triple(
                processedColor,
                QualityType.VALID,
                ""
            )
            else -> Triple(
                processedColor,
                QualityType.ENRICHED_VALUE,
                "Parsed color '$rawColor' to '$processedColor'"
            )
        }

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = finalColor,
            quality = VehicleFieldQuality(
                value = finalColor,
                type = qualityType,
                message = message
            )
        ).right()
    }

    private fun parseColor(colorString: String): String? {
        val normalizedColor = colorString.lowercase().trim()

        return when {
            normalizedColor.contains("grønn") ||
            normalizedColor.contains("green") ||
            normalizedColor.contains("grön") -> "Green"

            normalizedColor.contains("rød") ||
            normalizedColor.contains("red") ||
            normalizedColor.contains("röd") -> "Red"

            else -> null
        }
    }
}
