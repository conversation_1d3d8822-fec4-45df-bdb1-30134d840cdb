package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class ContractorProcessor : BaseFieldProcessor("contractor") {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        lastSnapshot?.internalVehicleId ?: 0

//        val contract = try {
////            ContractorDto(
////                id = 0,
////                contractIdInFrida = 0,
////                name = "noname",
////            )
//            Contract.fromFridaString(contractorFromDatabase)
//        } catch (e: IllegalArgumentException) {
//            return ProcessingError.InvalidFormat(
//                processor = this.javaClass.kotlin,
//                message = "can't parse contract json",
//                value = e.message
//            ).left()
//        }

        val contractorFromDatabase = aggregatedEventData["contractor"]?.fieldValue
        if (contractorFromDatabase == null) {
            return ProcessingError.MissingRequiredData(
              processor = this.javaClass.kotlin,
                message = "can't parse contract json",
            ).left()
        }
        val producedContractor = Contract.fromFridaString(contractorFromDatabase)

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = producedContractor,
            quality = VehicleFieldQuality(
                value = producedContractor,
                type = QualityType.VALID,
                message = "Parsed from JSON",
            ),
        ).right()
    }
}
