package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.BusType
import no.ruter.kosi.fleet.domain.vehicle.dto.IdentificationDto
import no.ruter.kosi.fleet.domain.vehicle.dto.ManufacturerDto
import no.ruter.kosi.fleet.domain.vehicle.dto.ModelInfoDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.DependsOn
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
@DependsOn(
    VehicleRefProcessor::class,
    VehicleSisIdProcessor::class,
    ColorProcessor::class,
)
class IdentificationProcessor : BaseFieldProcessor("identification") {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val vehicleRef = processorResults[VehicleRefProcessor::class]?.value as? String ?: "NO_VEHICLE_REF" // TODO fixme
        val vehicleSisId = processorResults[VehicleSisIdProcessor::class]?.value as? String?
        val color = processorResults[ColorProcessor::class]?.value as? String ?: ""

        val identification =
            IdentificationDto(
                vehicleId = 0,
                vehicleSISId = vehicleSisId,
                publicId = "",
                licensePlate = "",
                chassisNumber = vehicleRef,
                manufacturer = ManufacturerDto(
                    chassis = "",
                    brand = "",
                    producer = "",
                    chassisManufacturer = "",
                ),
                modelYear = "",
                model = ModelInfoDto(
                    typeDesignation = "",
                    commercialName = "",
                ),
                color = color,
                busType = BusType.BYBUSS
            )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = identification,
            quality =
                VehicleFieldQuality(
                    value = identification,
                    type = QualityType.VALID,
                ),
        ).right()
    }
}
