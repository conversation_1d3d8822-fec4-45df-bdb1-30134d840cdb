package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class NumberOfPassengersSeatedProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val value =
            getFirstNotNullValueEventData(
                aggregatedEventData,
                listOf(
                    // pretty sure this is wrong, it includes the driver seat, but we need parity with paxtelling
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt",
                    "numberOfPassengersSeated",
                ),
            )

        // existing seats value as fallback
        val totalSeats = lastSnapshot?.capacity?.seated

        return validateIntField(value?.fieldValue, this.javaClass.kotlin)
            .fold(
                { error ->
                    // fallbacks in order: snapshot, seats value, default null
                    when {
                        lastSnapshot?.capacity?.seated != null -> {
                            ProcessedField(
                                originProcessor = this.javaClass.kotlin,
                                value = lastSnapshot.capacity?.seated,
                                quality =
                                    VehicleFieldQuality(
                                        value = lastSnapshot.capacity?.seated,
                                        type = QualityType.STALE_VALUE,
                                        message = "Using previous value due to ${error.getErrorMessage()}",
                                    ),
                            ).right()
                        }
                        totalSeats != null -> {
                            // total seats as fallback
                            ProcessedField(
                                originProcessor = this.javaClass.kotlin,
                                value = totalSeats,
                                quality =
                                    VehicleFieldQuality(
                                        value = totalSeats,
                                        type = QualityType.ENRICHED_VALUE,
                                        message = "Derived from total seats due to ${error.getErrorMessage()}",
                                    ),
                            ).right()
                        }
                        else -> {
                            ProcessedField(
                                originProcessor = this.javaClass.kotlin,
                                value = null,
                                quality =
                                    VehicleFieldQuality(
                                        value = null,
                                        type = QualityType.MISSING_VALUE,
                                        message = "Default value null used due to ${error.getErrorMessage()}",
                                    ),
                            ).right()
                        }
                    }
                },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = quality.value,
                        quality = quality,
                    ).right()
                },
            )
    }
}
