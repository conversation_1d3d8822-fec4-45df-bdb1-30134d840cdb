package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import kotlin.reflect.KClass

@Component
class RegistrationDateProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldName = "registrationDate"
        val value: String? = extractFromEventData(aggregatedEventData, fieldName)?.let { it.toString().takeIf { it2 -> it2.isNotEmpty() }}

        return validateAndFormatDateField(value, this.javaClass.kotlin)
            .fold(
                { error -> error.left() },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = quality.value,
                        quality = quality
                    ).right()
                }
            )
    }

    private fun validateAndFormatDateField(
        newValue: Any?,
        processor: KClass<out VehicleFieldProcessor>,
    ): Either<ProcessingError, VehicleFieldQuality> {
        return when (newValue) {
            is String -> {
                try {
                    val formattedDate = LocalDate.parse(newValue, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mmX"))
                    Either.Right(VehicleFieldQuality(value = formattedDate, type = QualityType.VALID))
                } catch (e: DateTimeParseException) {
                    Either.Left(
                        ProcessingError.InvalidFormat(
                            processor = processor,
                            value = newValue,
                            message = "Invalid date format. ${e}. Expected format: yyyy-MM-dd'T'HH:mmX"
                        )
                    )
                }
            }
            null -> Either.Left(
                ProcessingError.MissingRequiredData(
                    processor = processor,
                    message = "No registration date provided"
                )
            )
            else -> Either.Left(
                ProcessingError.InvalidFormat(
                    processor = processor,
                    value = newValue,
                    message = "Expected String but got ${newValue::class.simpleName}"
                )
            )
        }
    }
}
