package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.*
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDate
import kotlin.reflect.KClass

@DependsOn(
    RegistrationDateProcessor::class,
)
@Component
class RegistrationProcessor : BaseFieldProcessor("registration") {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?
    ): Either<ProcessingError, ProcessedField> {
        val registrationDate = processorResults[RegistrationDateProcessor::class]?.value as? LocalDate

        val registration = RegistrationDto(
            registrationDate = registrationDate,
            firstRegistrationNorwayDate = "",
            firstRegistrationOnOwnership = "",
            status = RegistrationStatus.UKJENT,
            use = VehicleUse.UKJENT,
            businessCode = "",
            businessDescription = "",
            periodicControl = PeriodicControlDto(
                lastApproved = "",
                nextDue = ""
            ),
            permitPeriod = PermitPeriodDto(
                start = "",
                end = "",
            ),
            taxiCountyLetter = "",
        )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = registration,
            quality = VehicleFieldQuality(
                value = registration,
                type = QualityType.VALID
            )
        ).right()
    }
}