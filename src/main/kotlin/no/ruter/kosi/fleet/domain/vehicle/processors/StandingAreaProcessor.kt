package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.DependsOn
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.math.RoundingMode
import java.time.Instant
import kotlin.reflect.KClass

@Component
@DependsOn(
    ChassisNumberProcessor::class,
    VehicleLengthProcessor::class,
    NumberOfPassengersSeatedProcessor::class,
)
class StandingAreaProcessor : BaseFieldProcessor() {
    private val NO_SEATS = 0
    private val ZERO_LENGTH = 0.0

    // Taken from https://ruterwiki.ruter.no/display/DS/Fyllingsgrad
    private val LENGTH_COEFFICIENT = 1.539611185
    private val SEATS_COEFFICIENT = -0.21831636
    private val CUT_POINT = -2.311033554

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        // combine results from other processors
        val chassisNumber = processorResults[ChassisNumberProcessor::class]?.value as? String
        val seats = processorResults[NumberOfPassengersSeatedProcessor::class]?.value as? Int ?: 0 // treat as 0
        val vehicleLength = processorResults[VehicleLengthProcessor::class]?.value as? Double ?: 0.0 // treat as 0

        return when {
            chassisNumber?.startsWith("SL79") ?: false -> {
                val standingArea = 18.7
                validateAndCreateProcessedField(standingArea)
            }
            chassisNumber?.startsWith("SL95") ?: false -> {
                val standingArea = 28.6
                validateAndCreateProcessedField(standingArea)
            }
            chassisNumber?.startsWith("SL18") ?: false -> {
                val standingArea = 42.0
                validateAndCreateProcessedField(standingArea)
            }
            vehicleLength != ZERO_LENGTH && seats != NO_SEATS -> {
                val standingArea = (vehicleLength * LENGTH_COEFFICIENT) + (seats * SEATS_COEFFICIENT) + CUT_POINT
                validateAndCreateProcessedField(standingArea)
            }
            else -> ProcessingError.MissingRequiredData(
                processor = this.javaClass.kotlin,
                message = "Missing vehicle length or seats"
            ).left()
        }
    }

    private fun validateAndCreateProcessedField(standingArea: Double): Either<ProcessingError, ProcessedField> {
        return validateDoubleField(standingArea, this.javaClass.kotlin)
            .fold(
                { error -> error.left() },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = (quality.value as Double).toBigDecimal().setScale(2, RoundingMode.HALF_EVEN).toDouble(),
                        quality = quality
                    ).right()
                }
            )
    }
}
