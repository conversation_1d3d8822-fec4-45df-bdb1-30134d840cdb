package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class StandingPlacesProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldName = "godkjenning.tekniskGodkjenning.tekniskeData.persontall.staplasser"
        val value = extractFromEventData(aggregatedEventData, fieldName)

        return validateIntField(value, this.javaClass.kotlin)
            .fold(
                { error -> error.left() },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = quality.value,
                        quality = quality
                    ).right()
                }
            )
    }
}
