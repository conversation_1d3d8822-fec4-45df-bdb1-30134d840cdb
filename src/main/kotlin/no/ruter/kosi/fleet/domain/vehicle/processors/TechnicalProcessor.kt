package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.dto.*
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@DependsOn(
    VehicleLengthProcessor::class,
)
@Component
class TechnicalProcessor : BaseFieldProcessor("technical") {
    private val logger = LoggerFactory.getLogger(javaClass)

    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?
    ): Either<ProcessingError, ProcessedField> {
        val firstApproval = FirstApprovalDto(
            registeredDate = "",
            approvalId = "",
            type = ApprovalType.GODKJENNING,
            validFrom = "",
            customsReference = ""
        )

        val technicalApproval = TechnicalApprovalDto(
            firstApproval = firstApproval,
            technicalApproval = firstApproval,
            classification = ClassificationDto(
                feeCode = 0,
                technicalCode = "",
                specialCharacteristics = ""
            ),
            requirements = ""
        )

        val dimensions = DimensionsDto(
            lengthInMeters = processorResults[VehicleLengthProcessor::class]?.value as? Double,
            widthInMeters = null,
            heightInMeters = null
        )

        val unitWeights = UnitWeightsDto(
            frontAxle = 0,
            rearAxleGroup = 0,
            total = 0
        )

        val weights = WeightsDto(
            curbWeight = null,
            totalWeight = 0.0,
            unitWeights = unitWeights,
            payload = 0,
            grossVehicleWeight = 0
        )

        val axleDetail = AxleDetailDto(
            placement = 0,
            drivable = false,
            distanceToNextAxle = null,
            weight = null,
            airSuspension = false
        )

        val axleGroup = AxleGroupDto(
            id = 0,
            placement = 0,
            allowedLoad = 0,
            axles = listOf(axleDetail)
        )

        val axles = AxlesDto(
            count = 0,
            groups = listOf(axleGroup)
        )

        val tireRimCombination = TireRimCombinationDto(
            axleId = 0,
            tyre = "",
            rim = "",
            loadIndex = 0,
            speedCode = "",
            twin = false
        )

        val wheelsAndBrakes = WheelsAndBrakesDto(
            tyreRimCombinations = listOf(tireRimCombination),
            trailerBrakeConnection = listOf()
        )

        val gearbox = GearboxDto(
            type = "",
            ratios = listOf()
        )

        val motor = MotorDto(
            maxNetPower = 0.0,
            displacement = 0,
            fuel = "",
            particleFilter = false
        )

        val noise = NoiseDto(
            interior_dB = 0,
            external_dB = 0
        )

        val engineAndDrivetrain = EngineAndDrivetrainDto(
            engineTypeId = "",
            primaryCategory = "",
            hybridVehicle = false,
            gearbox = gearbox,
            maxSpeed = listOf(),
            motor = motor,
            noise = noise
        )

        val emissionValues = EmissionValuesDto(
            HC = 0.0,
            NOx = 0.0,
            PM = 0.0
        )

        val certification = CertificationDto(
            certificationUnitId = 0,
            certificationValueHC = 0.0,
            certificationValueNOx = 0.0,
            certificationValuePM = 0.0
        )

        val emissionsAndEnvironment = EmissionsAndEnvironmentDto(
            environmentalClass = "",
            euroClass = "",
            emissionUnitId = 0,
            emissionValues = emissionValues,
            certification = certification,
            energyUse = null,
            consumptionMixedDriving = null,
            eevVehicle = false
        )

        val technicalDto = TechnicalDto(
            technicalApproval = technicalApproval,
            dimensions = dimensions,
            weights = weights,
            axles = axles,
            wheelsAndBrakes = wheelsAndBrakes,
            engineAndDrivetrain = engineAndDrivetrain,
            emissionsAndEnvironment = emissionsAndEnvironment,
            doors = ""
        )

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = technicalDto,
            quality = VehicleFieldQuality(
                value = technicalDto,
                type = QualityType.VALID
            )
        ).right()
    }
}