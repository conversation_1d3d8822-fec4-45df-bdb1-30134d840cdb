package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.DependsOn
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
@DependsOn(
    ChassisNumberProcessor::class,
    NumberOfPassengersSeatedProcessor::class,
    StandingPlacesProcessor::class,
)
class TotalNumberPassengersProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val vehicleRef = processorResults[ChassisNumberProcessor::class]?.value as? String
        val seats = processorResults[NumberOfPassengersSeatedProcessor::class]?.value as? Int
        val standingPlaces = processorResults[StandingPlacesProcessor::class]?.value as? Int

        val isTram = listOf("SL79","SL95","SL18").any { vehicleRef?.startsWith(it) == true }
        if (isTram) {
            val totalNumberPassengers = aggregatedEventData["totalNumberOfPassengers"]?.fieldValue?.let {
                Integer.parseInt(it)
            }
            return ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = totalNumberPassengers,
                quality = VehicleFieldQuality(
                    value = totalNumberPassengers,
                    type = QualityType.VALID,
                    message = "taken from Frida's 'seats'"
                )
            ).right()
        }

        if (seats != null || standingPlaces != null) {
            val total = (seats ?: 0) + (standingPlaces ?: 0)
            val message = when {
                seats != null && standingPlaces != null -> "Calculated from autosys seats + standing places"
                seats != null -> "Calculated from autosys seats only, standing places missing"
                else -> "Calculated from autosys standing places only, seats missing"
            }
            val qualityType = if (seats != null && standingPlaces != null) QualityType.ENRICHED_VALUE else QualityType.MISSING_VALUE
            return ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = total,
                quality = VehicleFieldQuality(
                    value = total,
                    type = qualityType,
                    message = message
                )
            ).right()
        }

        val snapshotTotal = lastSnapshot?.capacity?.seated
        if (snapshotTotal != null) {
            return ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = snapshotTotal,
                quality = VehicleFieldQuality(
                    value = snapshotTotal,
                    type = QualityType.STALE_VALUE,
                    message = "Using last snapshot value for total number of passengers"
                )
            ).right()
        }

        return ProcessedField(
            originProcessor = this.javaClass.kotlin,
            value = 0,
            quality = VehicleFieldQuality(
                value = 0,
                type = QualityType.MISSING_VALUE,
                message = "Missing seats and standing places data, defaulting to 0",
            )
        ).right()
    }
}