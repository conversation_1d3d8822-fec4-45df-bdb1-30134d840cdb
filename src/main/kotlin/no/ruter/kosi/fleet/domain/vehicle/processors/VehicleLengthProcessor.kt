package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.flatMap
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.*
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class VehicleLengthProcessor : BaseFieldProcessor() {
    private fun getFallbackFromSnapshot(lastSnapshot: VehicleEntity?, errorMessage: String): Either<ProcessingError, ProcessedField>? {
        return if (lastSnapshot?.technical?.dimensions?.lengthInMeters != null) {
            ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = lastSnapshot.technical.dimensions.lengthInMeters,
                quality =
                    VehicleFieldQuality(
                        value = lastSnapshot.technical.dimensions.lengthInMeters,
                        type = QualityType.STALE_VALUE,
                        message = "Using fallback value due to $errorMessage",
                    ),
            ).right()
        } else {
            null
        }
    }
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldNames =
            listOf(
                "godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde",
                "vehicleLength",
                "customAttr_Fordonslängd",
                "customAttr_Fartøy_lengde"
            )

        val eventData = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
        if (eventData == null) {
            return getFallbackFromSnapshot(lastSnapshot, "No length data found") ?: Either.Left(
                ProcessingError.MissingRequiredData(
                    processor = this.javaClass.kotlin,
                    message = "No length data found",
                ),
            )
        }

        return validateDoubleField(eventData.fieldValue, this.javaClass.kotlin)
            .flatMap { rawQuality ->
                val rawValue = rawQuality.value as Double
                val isAutosys = DataSource.valueOf(eventData.event.source) == DataSource.AUTOSYS
                // only from autosys we get millimeters
                val normalizedValue = if (isAutosys) {
                    rawValue / 1000.0
                } else {
                    rawValue
                }

                if (normalizedValue < 4.0 || normalizedValue > 40.0) {
                    return Either.Left(
                        ProcessingError.InvalidFormat(
                            processor = this.javaClass.kotlin,
                            value = normalizedValue,
                            message = "Imported length $normalizedValue out of valid range (4.0 - 40.0 meters)"
                        )
                    )
                }

                val finalQuality = if (isAutosys) {
                    rawQuality.copy(
                        value = normalizedValue,
                        type = QualityType.ENRICHED_VALUE,
                        message = "Converted from mm to meters"
                    )
                } else {
                    rawQuality
                }

                finalQuality.right()
            }.fold(
                { error ->
                    getFallbackFromSnapshot(lastSnapshot, "error: ${error.getErrorMessage()}") ?: error.left()
                },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = quality.value,
                        quality = quality,
                    ).right()
                },
            )
    }
}
