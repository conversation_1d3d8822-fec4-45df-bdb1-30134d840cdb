package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class VehicleSisIdProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val value = extractFromEventData(aggregatedEventData, "customAttr_Internnummer_SIS")
        val strValue = value?.toString()

        // TODO make sure this is ok for SIS validation
        if (!strValue.isNullOrBlank() && strValue.all { it.isDigit() }) {
            return ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = strValue,
                quality =
                    VehicleFieldQuality(
                        value = strValue,
                        type = QualityType.VALID,
                    ),
            ).right()
        }

        val error =
            if (value == null) {
                ProcessingError.MissingRequiredData(
                    processor = this.javaClass.kotlin,
                    message = "No SIS ID found",
                )
            } else {
                ProcessingError.InvalidFormat(
                    processor = this.javaClass.kotlin,
                    value = value,
                    message = "SIS ID must be a numeric value",
                )
            }

        // Try fallback
        return if (!lastSnapshot?.identification?.vehicleSISId.isNullOrBlank()) {
            ProcessedField(
                originProcessor = this.javaClass.kotlin,
                value = lastSnapshot.identification.vehicleSISId,
                quality =
                    VehicleFieldQuality(
                        value = lastSnapshot.identification.vehicleSISId,
                        type = QualityType.STALE_VALUE,
                        message = "Using fallback due to ${error.getErrorMessage()}",
                    ),
            ).right()
        } else {
            error.left()
        }
    }
}
