package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.BaseFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.reflect.KClass

@Component
class WheelchairPlacesProcessor : BaseFieldProcessor() {
    override fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> {
        val fieldNames =
            listOf(
                "godkjenning.tekniskGodkjenning.tekniskeData.persontall.rullestolplasser",
                "wheelChairPlaces",
            )

        val value = getFirstNotNullValueEventData(aggregatedEventData, fieldNames)
            ?: return if (lastSnapshot?.features?.accessibility?.wheelchairPlaces != null) {
                ProcessedField(
                    originProcessor = this.javaClass.kotlin,
                    value = lastSnapshot.features.accessibility.wheelchairPlaces,
                    quality =
                        VehicleFieldQuality(
                            value = lastSnapshot.features.accessibility.wheelchairPlaces,
                            type = QualityType.STALE_VALUE,
                            message = "Using fallback value because no data found in sources",
                        ),
                ).right()
            } else {
                ProcessedField(
                    originProcessor = this.javaClass.kotlin,
                    value = null,
                    quality =
                        VehicleFieldQuality(
                            value = null,
                            type = QualityType.MISSING_VALUE,
                            message = "No wheelchair places data found",
                        ),
                ).right()
            }

        return validateIntField(value.fieldValue, this.javaClass.kotlin)
            .fold(
                { error ->
                    if (lastSnapshot?.features?.accessibility?.wheelchairPlaces != null) {
                        ProcessedField(
                            originProcessor = this.javaClass.kotlin,
                            value = lastSnapshot.features.accessibility.wheelchairPlaces,
                            quality =
                                VehicleFieldQuality(
                                    value = lastSnapshot.features.accessibility.wheelchairPlaces,
                                    type = QualityType.STALE_VALUE,
                                    message = "Using fallback value due to error: ${error.getErrorMessage()}",
                                ),
                        ).right()
                    } else {
                        error.left()
                    }
                },
                { quality ->
                    ProcessedField(
                        originProcessor = this.javaClass.kotlin,
                        value = quality.value,
                        quality = quality,
                    ).right()
                },
            )
    }
}
