package no.ruter.kosi.fleet.domain.vehicle.processors.common

import arrow.core.Either
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import org.slf4j.LoggerFactory
import java.time.Instant
import kotlin.reflect.KClass

abstract class BaseFieldProcessor(
    override val jsonPath: String? = null,
) : VehicleFieldProcessor {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun process(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField> =
        try {
            processInternal(aggregatedEventData, processorResults, lastSnapshot, timestamp)
        } catch (e: Exception) {
            logger.error("Unexpected error running processor ${this.javaClass.simpleName}: ${e.message}", e)
            Either.Left(ProcessingError.UnexpectedError(this.javaClass.kotlin,e))
        }

    protected abstract fun processInternal(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField>

    // TODO pretty sure this can just return a string
    protected fun extractFromEventData(
        aggregatedEventData: Map<String, EventData?>,
        sourceFieldName: String,
    ): String? = aggregatedEventData[sourceFieldName]?.fieldValue

    protected fun getFirstNotNullValueEventData(
        aggregatedEventData: Map<String, EventData?>,
        fieldNames: List<String>,
    ): EventData? {
        for (fieldName in fieldNames) {
            val value = extractFromEventData(aggregatedEventData, fieldName)
            if (value != null) return aggregatedEventData[fieldName]
        }
        return null
    }

    protected fun validateIntField(
        newValue: Any?,
        processor: KClass<out VehicleFieldProcessor>,
    ): Either<ProcessingError, VehicleFieldQuality> =
        when (newValue) {
            is Int -> Either.Right(VehicleFieldQuality(value = newValue, type = QualityType.VALID))
            is Double ->
                if (newValue % 1.0 == 0.0) {
                    Either.Right(VehicleFieldQuality(value = newValue.toInt(), type = QualityType.VALID))
                } else {
                    Either.Left(
                        ProcessingError.InvalidFormat(
                            processor = this.javaClass.kotlin,
                            value = newValue,
                            message = "Cannot convert Double with remainder to Int: $newValue",
                        ),
                    )
                }
            is String ->
                newValue.toIntOrNull()?.let {
                    Either.Right(VehicleFieldQuality(value = it, type = QualityType.VALID))
                } ?: Either.Left(
                    ProcessingError.InvalidFormat(
                        processor = this.javaClass.kotlin,
                        value = newValue,
                        message = "Cannot parse String to Int: $newValue",
                    ),
                )
            null ->
                Either.Left(
                    ProcessingError.MissingRequiredData(
                        processor = this.javaClass.kotlin,
                        message = "No value provided for required integer field",
                    ),
                )
            else ->
                Either.Left(
                    ProcessingError.InvalidFormat(
                        processor = this.javaClass.kotlin,
                        value = newValue,
                        message = "Cannot convert ${newValue::class} to Int",
                    ),
                )
        }

    protected fun validateDoubleField(
        newValue: Any?,
        processor: KClass<out VehicleFieldProcessor>,
    ): Either<ProcessingError, VehicleFieldQuality> =
        when (newValue) {
            is Double -> Either.Right(VehicleFieldQuality(value = newValue, type = QualityType.VALID))
            is Int -> Either.Right(VehicleFieldQuality(value = newValue.toDouble(), type = QualityType.VALID))
            is String -> {
                val normalized = newValue.replace(",", ".")
                normalized.toDoubleOrNull()?.let {
                    Either.Right(VehicleFieldQuality(value = it, type = QualityType.VALID))
                } ?: Either.Left(
                    ProcessingError.InvalidFormat(
                        processor = this.javaClass.kotlin,
                        value = normalized,
                        message = "Cannot parse String \"$newValue\" to Double",
                    ),
                )
            }
            null ->
                Either.Left(
                    ProcessingError.MissingRequiredData(
                        processor = this.javaClass.kotlin,
                        message = "No value provided for required double field",
                    ),
                )
            else ->
                Either.Left(
                    ProcessingError.InvalidFormat(
                        processor = this.javaClass.kotlin,
                        value = newValue,
                        message = "Cannot convert ${newValue::class} to Double",
                    ),
                )
        }

    protected fun validateStringField(
        newValue: Any?,
        processor: KClass<out VehicleFieldProcessor>,
    ): Either<ProcessingError, VehicleFieldQuality> =
        when {
            newValue == null ->
                Either.Left(
                    ProcessingError.MissingRequiredData(
                        processor = this.javaClass.kotlin,
                        message = "No value provided for required string field",
                    ),
                )
            newValue.toString().isBlank() ->
                Either.Left(
                    ProcessingError.InvalidFormat(
                        processor = this.javaClass.kotlin,
                        value = newValue,
                        message = "Empty string value",
                    ),
                )
            else ->
                Either.Right(
                    VehicleFieldQuality(
                        value = newValue.toString(),
                        type = QualityType.VALID,
                    ),
                )
        }
}
