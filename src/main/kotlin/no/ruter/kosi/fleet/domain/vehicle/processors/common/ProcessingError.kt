package no.ruter.kosi.fleet.domain.vehicle.processors.common

import kotlin.reflect.KClass

sealed class ProcessingError {
    data class MissingRequiredData(
        val processor: KClass<out VehicleFieldProcessor>,
        val message: String,
    ) : ProcessingError()

    data class InvalidFormat(
        val processor: KClass<out VehicleFieldProcessor>,
        val value: Any?,
        val message: String,
    ) : ProcessingError()

    data class ExternalServiceFailure(
        val processor: KClass<out VehicleFieldProcessor>,
        val service: String,
        val message: String,
    ) : ProcessingError()

    data class UnexpectedError(
        val processor: KClass<out VehicleFieldProcessor>,
        val exception: Throwable,
    ) : ProcessingError()
}

fun formatProcessingError(error: ProcessingError): String =
    when (error) {
        // TODO be more specific
        is ProcessingError.MissingRequiredData ->
            "Missing data for processor '${error.processor.simpleName}': ${error.message}"
        is ProcessingError.InvalidFormat ->
            "Invalid format in field '${error.processor.simpleName}', value '${error.value}': ${error.message}"
        is ProcessingError.ExternalServiceFailure ->
            "External service '${error.service}' error: ${error.message}"
        is ProcessingError.UnexpectedError ->
            "Unexpected error: '${error.exception.stackTraceToString()}"
    }

fun ProcessingError.getErrorMessage(): String = formatProcessingError(this)
