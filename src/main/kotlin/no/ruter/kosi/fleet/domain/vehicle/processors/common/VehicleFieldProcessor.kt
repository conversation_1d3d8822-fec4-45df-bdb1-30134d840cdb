package no.ruter.kosi.fleet.domain.vehicle.processors.common

import arrow.core.Either
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import java.time.Instant
import kotlin.reflect.KClass

interface VehicleFieldProcessor {
    val jsonPath: String?

    fun process(
        aggregatedEventData: Map<String, EventData?>,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField>,
        lastSnapshot: VehicleEntity?,
        timestamp: Instant?,
    ): Either<ProcessingError, ProcessedField>
}
