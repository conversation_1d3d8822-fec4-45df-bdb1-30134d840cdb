package no.ruter.kosi.fleet.domain.vehicle.processors.common

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

@Component
class VehicleFieldProcessorRegistry(
    fieldProcessors: List<VehicleFieldProcessor>,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    private val allProcessors: List<VehicleFieldProcessor> = fieldProcessors
    private val byClass: Map<KClass<out VehicleFieldProcessor>, VehicleFieldProcessor> =
        allProcessors.associateBy { it::class }

    private val dependencyMap: Map<KClass<out VehicleFieldProcessor>, List<KClass<out VehicleFieldProcessor>>> =
        allProcessors.associate { proc ->
            val procClass = proc::class
            val ann = procClass.java.getAnnotation(DependsOn::class.java)
            val deps: List<KClass<out VehicleFieldProcessor>> = ann?.processorClasses?.toList() ?: emptyList()
            procClass to deps
        }

    // adjacency is basically producers -> list of consumers
    private val adjacency: Map<VehicleFieldProcessor, List<VehicleFieldProcessor>>

    // the processors in the order we should execute them in to satisfy dependenices
    private val sortedProcessors: List<VehicleFieldProcessor>

    init {
        // the edges in this DAG, processor -> mutable list of its children
        val edges = allProcessors.associateWith { mutableListOf<VehicleFieldProcessor>() }.toMutableMap()

        allProcessors.forEach { consumer ->
            val consumerClass = consumer::class
            val requiredClasses = dependencyMap[consumerClass] ?: emptyList()
            requiredClasses.forEach { depClass ->
                val producerInstance = byClass[depClass]
                    ?: throw IllegalStateException(
                        "Bean for ${consumerClass.simpleName} depends on $depClass, but no instance was found."
                    )
                edges[producerInstance]!!.add(consumer)
            }
        }

        adjacency = edges.mapValues { it.value.toList() }

        // detect cyclic dependencies with kahn's
        val inDegree: MutableMap<VehicleFieldProcessor, Int> =
            allProcessors.associateWith { 0 }.toMutableMap()

        adjacency.forEach { (producer, consumers) ->
            consumers.forEach { consumer ->
                inDegree[consumer] = inDegree.getValue(consumer) + 1
            }
        }

        val queue = ArrayDeque(allProcessors.filter { inDegree[it] == 0 })
        val result = mutableListOf<VehicleFieldProcessor>()

        while (queue.isNotEmpty()) {
            val processor = queue.removeFirst()
            result.add(processor)
            adjacency[processor]?.forEach { adjacentProcessor ->
                inDegree[adjacentProcessor] = inDegree.getValue(adjacentProcessor) - 1
                if (inDegree[adjacentProcessor] == 0) {
                    queue.addLast(adjacentProcessor)
                }
            }
        }

        if (result.size != allProcessors.size) {
            val infiniteCycleSuspects = allProcessors.filter { it !in result }.map { it::class.simpleName }
            throw IllegalStateException("Cycle detected among processors: $infiniteCycleSuspects")
        }

        sortedProcessors = result.toList()

        // debug logging
        logger.info("=================================================")
        logger.info("=== Processor dependency topology ===")
        dependencyMap.forEach { (processor, dependencies) ->
            val dependencyNames = dependencies.joinToString(", ") { it.simpleName ?: "<anonymous>" }
            logger.info("  ${processor.simpleName} depends on → [$dependencyNames]")
        }
        logger.info("=== Actual processing order ===")
        sortedProcessors.forEachIndexed { i, processor ->
            logger.info("  ${i + 1}. ${processor::class.simpleName}")
        }
        logger.info("=================================================")
    }

    fun getProcessors(): List<VehicleFieldProcessor> = sortedProcessors

    fun getDeclaredDependencies(): Map<KClass<out VehicleFieldProcessor>, List<KClass<out VehicleFieldProcessor>>> = dependencyMap
}