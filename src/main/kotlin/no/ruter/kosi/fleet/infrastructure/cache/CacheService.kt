package no.ruter.kosi.fleet.infrastructure.cache

import com.github.benmanes.caffeine.cache.Cache
import org.slf4j.LoggerFactory
import org.springframework.cache.CacheManager
import org.springframework.stereotype.Service

@Service
class CacheService(private val cacheManager: CacheManager) {
    private val logger = LoggerFactory.getLogger(CacheService::class.java)

    fun evictCache(cacheName: String) {
        logger.info("Evicting all entries from cache: {}", cacheName)
        cacheManager.getCache(cacheName)?.clear()
    }

    fun evictCacheEntry(cacheName: String, key: Any) {
        logger.info("Evicting entry with key '{}' from cache: {}", key, cacheName)
        cacheManager.getCache(cacheName)?.evict(key)
    }

    fun evictAllCaches() {
        logger.info("Evicting all caches")
        cacheManager.cacheNames.forEach { cacheName ->
            cacheManager.getCache(cacheName)?.clear()
        }
    }

    fun getCachesInfo(): Map<String, Any> {
        val cacheInfo = mutableMapOf<String, Any>()

        cacheManager.cacheNames.forEach { cacheName ->
            val cache = cacheManager.getCache(cacheName)
            val cacheStats = when (cache?.nativeCache) {
                is Cache<*, *> -> {
                    val caffeineCache = cache.nativeCache as Cache<*, *>
                    mapOf(
                        "size" to caffeineCache.estimatedSize(),
                        "stats" to caffeineCache.stats().toString()
                    )
                }
                else -> mapOf("info" to "Cache statistics not available")
            }
            cacheInfo[cacheName] = cacheStats
        }

        return cacheInfo
    }
}