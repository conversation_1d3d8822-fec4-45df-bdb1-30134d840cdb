package no.ruter.kosi.fleet.infrastructure.cache

import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.TimeUnit
import org.springframework.cache.caffeine.CaffeineCacheManager
import com.github.benmanes.caffeine.cache.Caffeine

@Configuration
@EnableCaching
class CachingConfig {
    @Bean
    fun cacheManager(): CacheManager {
        val cacheManager =
            CaffeineCacheManager("vehiclesList", "vehicleDetails", "vehicleReconstruction", "eventDataList")
        cacheManager.setCaffeine(
            Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .maximumSize(100)
                .recordStats()
        )
        return cacheManager
    }
}