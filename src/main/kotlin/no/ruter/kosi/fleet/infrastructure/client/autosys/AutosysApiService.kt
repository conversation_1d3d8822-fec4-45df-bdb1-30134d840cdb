package no.ruter.kosi.fleet.infrastructure.client.autosys

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.autosysclient.client.ApiClientException
import no.ruter.kosi.autosysclient.client.EnkeltoppslagKjoretoydataClient
import no.ruter.kosi.autosysclient.models.EnkeltOppslagKjoretoydata
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

sealed class AutosysApiError {
    data class NotFound(
        val vehicleId: Int,
        val vehicleRef: String?,
    ) : AutosysApiError()

    data class ClientError(
        val statusCode: Int,
        val message: String,
    ) : AutosysApiError()

    data class NetworkError(
        val cause: Throwable,
    ) : AutosysApiError()

    data class UnexpectedError(
        val cause: Throwable,
    ) : AutosysApiError()
}

@Service
class AutosysApiService(
    private val kjoretoydataClient: EnkeltoppslagKjoretoydataClient,
) {
    private val logger = LoggerFactory.getLogger(AutosysApiService::class.java)

    fun getVehicleData(vehicle: InternalVehicle): Either<AutosysApiError, EnkeltOppslagKjoretoydata> {
        return try {
            if (vehicle.vehicleRef != null) {
                logger.debug("Fetching vehicle data by chassis number: {}", vehicle.vehicleRef)
                fetchByChassisNumber(vehicle.vehicleRef!!, vehicle.vehicleId)
            } else if (vehicle.publicIdString != null) {
                logger.debug("Fetching vehicle data by registration number: {}", vehicle.publicIdString)
                fetchByRegistrationId(vehicle.publicIdString!!, vehicle.vehicleId)
            } else {
                logger.warn("Cannot fetch vehicle data: no chassis number or registration number available")
                AutosysApiError.NotFound(vehicle.vehicleId, vehicle.vehicleRef).left()
            }
        } catch (e: Exception) {
            val error =
                when (e) {
                    is ApiClientException -> {
                        logger.error(
                            "AUTOSYS API client error for vehicle {}: {} (Status code: {})",
                            vehicle.vehicleId,
                            e.message,
                            e.statusCode,
                        )
                        AutosysApiError.ClientError(e.statusCode, e.message)
                    }

                    else -> {
                        logger.error(
                            "Unexpected error fetching vehicle data for vehicle {}: {}",
                            vehicle.vehicleId,
                            e.message,
                            e,
                        )
                        AutosysApiError.UnexpectedError(e)
                    }
                }
            error.left()
        }
    }

    private fun fetchByChassisNumber(
        chassisNumber: String,
        vehicleId: Int,
    ): Either<AutosysApiError, EnkeltOppslagKjoretoydata> =
        try {
            val response = kjoretoydataClient.hentKjoretoydata(understellsnummer = chassisNumber)
            val dataList = response.data?.kjoretoydataListe

            if (dataList.isNullOrEmpty()) {
                logger.warn("No vehicle data found for chassis number: {}", chassisNumber)
                AutosysApiError.NotFound(vehicleId, chassisNumber).left()
            } else {
                logger.debug("Successfully retrieved vehicle data for chassis number: {}", chassisNumber)
                dataList.first().right()
            }
        } catch (e: ApiClientException) {
            if (e.statusCode == 404) {
                AutosysApiError.NotFound(vehicleId, chassisNumber).left()
            } else {
                AutosysApiError.ClientError(e.statusCode, e.message).left()
            }
        } catch (e: Exception) {
            AutosysApiError.NetworkError(e).left()
        }

    private fun fetchByRegistrationId(
        registrationNumber: String,
        vehicleId: Int,
    ): Either<AutosysApiError, EnkeltOppslagKjoretoydata> =
        try {
            val response = kjoretoydataClient.hentKjoretoydata(kjennemerke = registrationNumber)
            val dataList = response.data?.kjoretoydataListe

            if (dataList.isNullOrEmpty()) {
                logger.warn("No vehicle data found for registration number: {}", registrationNumber)
                AutosysApiError.NotFound(vehicleId, null).left()
            } else {
                logger.debug("Successfully retrieved vehicle data for registration number: {}", registrationNumber)
                dataList.first().right()
            }
        } catch (e: ApiClientException) {
            if (e.statusCode == 404) {
                AutosysApiError.NotFound(vehicleId, null).left()
            } else {
                AutosysApiError.ClientError(e.statusCode, e.message).left()
            }
        } catch (e: Exception) {
            AutosysApiError.NetworkError(e).left()
        }
}
