package no.ruter.kosi.fleet.infrastructure.client.autosys

import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.autosysclient.client.EnkeltoppslagKjoretoydataClient
import no.ruter.kosi.fleet.infrastructure.client.autosys.configuration.AutosysApiProperties
import okhttp3.OkHttpClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class AutosysClientFactory(
    private val apiProperties: AutosysApiProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean(name = ["autosysApiClient"])
    fun apiClient(): OkHttpClient =
        OkHttpClient
            .Builder()
            .addInterceptor { chain ->
                val request =
                    chain
                        .request()
                        .newBuilder()
                        .header("SVV-Authorization", "Apikey ${apiProperties.token}")
                        .build()
                chain.proceed(request)
            }.build()

    @Bean
    fun kjoretoydataClient(
        @Qualifier("autosysApiClient") apiClient: OkHttpClient,
    ): EnkeltoppslagKjoretoydataClient =
        EnkeltoppslagKjoretoydataClient(
            objectMapper = objectMapper,
            baseUrl = apiProperties.baseUrl,
            client = apiClient,
        )
}
