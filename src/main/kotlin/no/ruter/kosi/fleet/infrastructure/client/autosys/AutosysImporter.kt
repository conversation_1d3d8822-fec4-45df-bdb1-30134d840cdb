package no.ruter.kosi.fleet.infrastructure.client.autosys

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.autosysclient.models.EnkeltOppslagKjoretoydata
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import kotlin.reflect.full.memberProperties

sealed class AutosysImporterError {
    data class ProcessingFailure(
        val vehicleId: Int,
        val vehicleRef: String?,
        val cause: Throwable,
    ) : AutosysImporterError()

    data class MissingData(
        val vehicleId: Int,
        val message: String,
    ) : AutosysImporterError()
}

@Service
class AutosysImporter(
    private val eventService: EventService,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(AutosysImporter::class.java)

    fun processVehicle(
        externalVehicle: EnkeltOppslagKjoretoydata,
        internalVehicle: InternalVehicle,
        job: FleetJob,
    ): Either<AutosysImporterError, InternalVehicle> {
            return try {
                logger.debug("Extracting fields from AUTOSYS data")
                val extractedFields = flattenNestedObjectWithPaths("", externalVehicle)

                if (extractedFields.isEmpty()) {
                    logger.warn("No fields extracted from AUTOSYS data")
                    return AutosysImporterError
                        .MissingData(
                            internalVehicle.vehicleId,
                            "No fields extracted from AUTOSYS data",
                        ).left()
                }

                logger.debug(
                    "Extracted {} fields from AUTOSYS data",
                    extractedFields.size,
                )

                val updatedVehicle =
                    eventService.storeChangedFieldsFromVehicle(
                        internalVehicle,
                        extractedFields,
                        DataSource.AUTOSYS,
                        job,
                    )

                updatedVehicle.right()
            } catch (e: Exception) {
                logger.error("Error importing from AUTOSYS", e)

                if (logger.isTraceEnabled) {
                    try {
                        logger.trace(
                            "AUTOSYS data:\n--------------------\n{}\n--------------------------------",
                            objectMapper.writeValueAsString(externalVehicle),
                        )
                    } catch (ex: Exception) {
                        logger.trace("Could not serialize AUTOSYS data: {}", ex.message)
                    }
                }

                AutosysImporterError
                    .ProcessingFailure(
                        internalVehicle.vehicleId,
                        internalVehicle.vehicleRef,
                        e,
                    ).left()
            }
    }

    private fun flattenNestedObjectWithPaths(
        prefix: String = "",
        obj: Any?,
        result: MutableMap<String, Any?> = mutableMapOf(),
    ): Map<String, Any?> {
        if (obj == null) return result

        // treat java package objects as a primitive and use toString()
        if (obj.javaClass.`package`
                ?.name
                ?.startsWith("java") == true
        ) {
            result[prefix] = obj.toString()
            return result
        }

        when (obj) {
            is List<*> -> {
                obj.forEachIndexed { index, element ->
                    // when the object is a list
                    val newPrefix = if (prefix.isEmpty()) "[$index]" else "$prefix[$index]"
                    flattenNestedObjectWithPaths(newPrefix, element, result)
                }
            }
            is Map<*, *> -> {
                obj.forEach { (key, value) ->
                    val newPrefix = if (prefix.isEmpty()) key.toString() else "$prefix.$key"
                    flattenNestedObjectWithPaths(newPrefix, value, result)
                }
            }
            else -> {
                // when the object is a kotlin class (nested objects)
                try {
                    val kClass = obj::class
                    if (kClass.memberProperties.isNotEmpty()) {
                        kClass.memberProperties.forEach { property ->
                            val newPrefix = if (prefix.isEmpty()) property.name else "$prefix.${property.name}"
                            try {
                                val value = property.getter.call(obj)
                                flattenNestedObjectWithPaths(newPrefix, value, result)
                            } catch (e: Exception) {
                                // log and skip inaccessible fields
                                logger.trace("Error extracting field '{}': {}", property.name, e.message)
                            }
                        }
                    } else {
                        result[prefix] = obj.toString()
                    }
                } catch (e: Exception) {
                    // primitive or simple values
                    result[prefix] = obj.toString()
                }
            }
        }
        return result
    }

}
