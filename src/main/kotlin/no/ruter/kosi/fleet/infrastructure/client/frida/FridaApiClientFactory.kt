package no.ruter.kosi.fleet.infrastructure.client.frida

import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.FridaApiProperties
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import no.ruter.kosi.fridaclient.client.ApiContractContractsClient
import no.ruter.kosi.fridaclient.client.ApiVehicleVehiclesClient
import okhttp3.OkHttpClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class FridaApiClientFactory(
    private val tokenProvider: TokenProvider,
    private val apiProperties: FridaApiProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean(name = ["fridaApiClient"])
    fun apiClient(): OkHttpClient =
        OkHttpClient
            .Builder()
            .addInterceptor { chain ->
                val token = tokenProvider.getAccessToken()
                val request =
                    chain
                        .request()
                        .newBuilder()
                        .header("Authorization", "Bearer $token")
                        .build()
                chain.proceed(request)
            }.build()

    @Bean
    fun vehiclesClient(
        @Qualifier("fridaApiClient") apiClient: OkHttpClient,
    ): ApiVehicleVehiclesClient =
        ApiVehicleVehiclesClient(
            objectMapper = objectMapper,
            baseUrl = apiProperties.baseUrl,
            client = apiClient,
        )

    @Bean
    fun contractorsClient(
        @Qualifier("fridaApiClient") apiClient: OkHttpClient,
    ): ApiContractContractsClient =
        ApiContractContractsClient(
            objectMapper = objectMapper,
            baseUrl = apiProperties.baseUrl,
            client = apiClient,
        )
}
