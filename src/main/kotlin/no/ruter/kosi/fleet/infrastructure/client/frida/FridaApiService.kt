package no.ruter.kosi.fleet.infrastructure.client.frida

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fridaclient.client.ApiContractContractsClient
import no.ruter.kosi.fridaclient.client.ApiResponse
import no.ruter.kosi.fridaclient.client.ApiVehicleVehiclesClient
import no.ruter.kosi.fridaclient.models.ApiContractV3Model
import no.ruter.kosi.fridaclient.models.ApiListResponseModelOfApiContractV3Model
import no.ruter.kosi.fridaclient.models.ApiListResponseModelOfApiVehicleV2Model
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.OffsetDateTime

sealed class FridaApiError {
    data class ClientError(
        val statusCode: Int,
        val message: String,
    ) : FridaApiError()

    data class NetworkError(
        val cause: Throwable,
    ) : FridaApiError()

    data class ParseError(
        val cause: Throwable,
    ) : FridaApiError()

    data class EmptyResponse(
        val message: String,
    ) : FridaApiError()

    data class UnexpectedError(
        val cause: Throwable,
    ) : FridaApiError()

    object Cancelled : FridaApiError()
}

@Service
class FridaApiService(
    private val vehiclesClient: ApiVehicleVehiclesClient,
    private val contractorsClient: ApiContractContractsClient,
) {
    private val logger = LoggerFactory.getLogger(FridaApiService::class.java)
    private val pageSize = 200 // random start amount

    fun fetchVehicles(
        fromDate: OffsetDateTime? = null,
        isCancelled: (() -> Boolean)? = null
    ): Either<FridaApiError, List<ApiVehicleV2Model>> {
        return try {
            logger.info("Fetching vehicles from FRIDA API".plus(fromDate?.let { " (from $it)" } ?: ""))
            val vehicles = mutableListOf<ApiVehicleV2Model>()

            if (isCancelled?.invoke() == true) {
                logger.info("Fetch vehicles operation cancelled before starting")
                return FridaApiError.Cancelled.left()
            }

            // handle pagination
            val firstPageResponse = fetchVehiclesPage(0, fromDate = fromDate)

            firstPageResponse.fold(
                { error -> error.left() },
                { firstPage ->
                    vehicles.addAll(firstPage.first)

                    val totalCount = firstPage.second
                    logger.info(
                        "Retrieved first page with {} vehicles, total count: {}",
                        firstPage.first.size,
                        totalCount,
                    )

                    val totalPages = (totalCount / pageSize)

                    // remaining pages
                    for (page in 1..totalPages) {
                        // check if cancelled before fetching next page
                        if (isCancelled?.invoke() == true) {
                            logger.info("Fetch vehicles operation cancelled during pagination (before page {})", page)
                            return FridaApiError.Cancelled.left()
                        }

                        logger.debug("Fetching page {} of {}", page, totalPages)
                        val nextPageResponse = fetchVehiclesPage(page, fromDate = fromDate)

                        nextPageResponse.fold(
                            { error ->
                                logger.error("Error fetching page {}: {}", page, error)
                                error.left()
                            },
                            { pageData ->
                                vehicles.addAll(pageData.first)
                                logger.debug("Added {} vehicles from page {}", pageData.first.size, page)
                            },
                        )
                    }

                    logger.info("Successfully fetched all {} vehicles from FRIDA", vehicles.size)
                    vehicles.right()
                },
            )
        } catch (e: Exception) {
            logger.error("Unexpected error fetching vehicles from FRIDA: {}", e.message, e)
            FridaApiError.UnexpectedError(e).left()
        }
    }

    fun fetchContracts(): Either<FridaApiError, List<ApiContractV3Model>> {
        return try {
            logger.info("Fetching vehicles from FRIDA API")
            val contracts = mutableListOf<ApiContractV3Model>()

            // handle pagination
            val firstPageResponse = fetchContractsPage(0)
            firstPageResponse.fold(
                { error -> error.left() },
                { firstPage ->
                    // TODO implement contracts
                    //vehicles.addAll(firstPage.first)

                    val totalCount = firstPage.second
                    logger.info(
                        "Retrieved first page with {} contracts, total count: {}",
                        firstPage.first.size,
                        totalCount,
                    )

                    val totalPages = (totalCount / pageSize)

                    // remaining pages
                    for (page in 1..totalPages) {
                        logger.debug("Fetching page {} of {}", page, totalPages)
                        val nextPageResponse = fetchContractsPage(page)

                        nextPageResponse.fold(
                            { error ->
                                logger.error("Error fetching page {}: {}", page, error)
                                error.left()
                            },
                            { pageData ->
                                //vehicles.addAll(pageData.first)
                                logger.debug("Got {} contracts on page {}", pageData.first.size, page)
                            },
                        )
                    }

                    logger.info("Successfully fetched all {} contracts from FRIDA", contracts.size)
                    contracts.right()
                },
            )
        } catch (e: Exception) {
            logger.error("Unexpected error fetching contracts from FRIDA: {}", e.message, e)
            FridaApiError.UnexpectedError(e).left()
        }
    }

    private fun fetchContractsPage(page: Int): Either<FridaApiError, Pair<List<ApiContractV3Model>, Int>> =
        try {
            val response =
                if (page == 0) {
                    contractorsClient.contractsVGetAllContracts()
                } else {
                    contractorsClient.contractsVGetAllContracts(page, pageSize)
                }

            validateContractsResponse(response, page)
        } catch (e: Exception) {
            logger.error("Error fetching page {}: {}", page, e.message, e)
            FridaApiError.NetworkError(e).left()
        }

    private fun fetchVehiclesPage(page: Int, fromDate: OffsetDateTime?): Either<FridaApiError, Pair<List<ApiVehicleV2Model>, Int>> =
        try {
            val response =
                if (page == 0) {
                    vehiclesClient.vehiclesVGetAllVehicles(date = fromDate)
                } else {
                    vehiclesClient.vehiclesVGetAllVehicles(page, pageSize, date = fromDate)
                }

            validateVehiclesResponse(response, page)
        } catch (e: Exception) {
            logger.error("Error fetching page {}: {}", page, e.message, e)
            FridaApiError.NetworkError(e).left()
        }

    private inline fun <T, R> validatePagedResponse(
        response: ApiResponse<T>,
        page: Int,
        crossinline itemsExtractor: (T) -> Pair<List<R>?, Int?>
    ): Either<FridaApiError, Pair<List<R>, Int>> {
        if (!response.isSuccessful() || response.statusCode !in 200..299) {
            return FridaApiError.ClientError(
                response.statusCode,
                "Error fetching page $page: HTTP ${response.statusCode}"
            ).left()
        }
        val data = response.data ?: return FridaApiError.EmptyResponse("Empty response for page $page").left()
        val (items, totalCount) = itemsExtractor(data)
        if (items == null) return FridaApiError.EmptyResponse("No items in response for page $page").left()
        return Pair(items, totalCount ?: 0).right()
    }

    private fun validateVehiclesResponse(
        response: ApiResponse<ApiListResponseModelOfApiVehicleV2Model>,
        page: Int,
    ) = validatePagedResponse(response, page) { data -> Pair(data.items, data.totalCount) }

    private fun validateContractsResponse(
        response: ApiResponse<ApiListResponseModelOfApiContractV3Model>,
        page: Int,
    ) = validatePagedResponse(response, page) { data -> Pair(data.items, data.totalCount) }

    private fun ApiResponse<*>.isSuccessful(): Boolean = statusCode in 200..299
}
