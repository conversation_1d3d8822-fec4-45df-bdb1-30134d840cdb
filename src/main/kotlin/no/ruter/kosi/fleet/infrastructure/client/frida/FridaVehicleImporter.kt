package no.ruter.kosi.fleet.infrastructure.client.frida

import com.fasterxml.jackson.databind.ObjectMapper
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.FleetVehicleRefGenerator
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import kotlin.reflect.full.memberProperties

@Service
class FridaVehicleImporter
@Autowired
constructor(
    private val eventService: EventService,
    private val objectMapper: ObjectMapper,
    private val vehicleRepository: AuroraInternalVehicleRepository,
    private val eventDataRepository: AuroraEventDataRepository,
) {
    private val logger = LoggerFactory.getLogger(FridaVehicleImporter::class.java)

    fun processVehicles(
        fridaVehicles: List<ApiVehicleV2Model>,
        job: FleetJob,
        onVehicleProcessed: ((count: Int) -> Unit)? = null,
        isCancelled: (() -> Boolean)? = null,
        historicTimestamp: Instant? = null,
        batchSize: Int = 50,
    ) {
        MdcUtils.setJobId(job.jobId)

        logger.info("Processing {} vehicles from FRIDA in batches of {}", fridaVehicles.size, batchSize)

        var processedCount = 0

        fridaVehicles.chunked(batchSize).forEachIndexed { batchIndex, batch ->
            if (isCancelled?.invoke() == true) {
                logger.info("Job cancelled, stopping vehicle processing after {} of {} vehicles",
                    processedCount, fridaVehicles.size)
                return
            }

            try {
                val batchResult = processBatch(batch, job, historicTimestamp)
                processedCount += batchResult

                // Update progress for the entire batch
                val totalProcessed = (batchIndex + 1) * batchSize.coerceAtMost(fridaVehicles.size - batchIndex * batchSize)
                onVehicleProcessed?.invoke(totalProcessed.coerceAtMost(fridaVehicles.size))

                logger.info("Completed batch {} of {}, processed {} vehicles so far",
                    batchIndex + 1,
                    (fridaVehicles.size + batchSize - 1) / batchSize,
                    processedCount)

            } catch (e: Exception) {
                logger.error("Error processing batch {}: {}", batchIndex + 1, e.message, e)

                // Fall back to individual processing for this batch
                logger.info("Falling back to individual processing for batch {}", batchIndex + 1)
                batch.forEachIndexed { vehicleIndex, externalVehicle ->
                    if (isCancelled?.invoke() == true) {
                        logger.info("Job cancelled during fallback processing")
                        return
                    }

                    MdcUtils.setVehicleId(externalVehicle.id)
                    MdcUtils.setVehicleRef(externalVehicle.chassiNumber)
                    try {
                        processVehicle(externalVehicle, job, historicTimestamp)
                        processedCount++
                    } catch (vehicleError: Exception) {
                        logger.error(
                            "Error importing vehicle from FRIDA with ID {}: {}",
                            externalVehicle.id,
                            vehicleError.message,
                            vehicleError,
                        )
                    }

                    val globalIndex = batchIndex * batchSize + vehicleIndex + 1
                    onVehicleProcessed?.invoke(globalIndex)
                }
            }
        }

        logger.info("Completed processing of {} FRIDA vehicles", processedCount)
    }

    @Transactional
    fun processBatch(
        batch: List<ApiVehicleV2Model>,
        job: FleetJob,
        historicTimestamp: Instant? = null,
    ): Int {
        logger.debug("Processing batch of {} vehicles", batch.size)

        val vehiclesWithFields = mutableListOf<Pair<InternalVehicle, Map<String, Any?>>>()
        var processedCount = 0

        batch.forEach { externalVehicle ->
            try {
                MdcUtils.setVehicleId(externalVehicle.id)
                MdcUtils.setVehicleRef(externalVehicle.chassiNumber)

                val vehicle = getOrCreateVehicle(externalVehicle)
                val extractedFields = extractFields(externalVehicle)
                val changedFields = getChangedFields(vehicle, extractedFields)

                if (changedFields.isNotEmpty()) {
                    vehiclesWithFields.add(vehicle to changedFields)
                    logger.debug("Found {} changed fields for vehicle {}", changedFields.size, vehicle.vehicleRef)
                }

                processedCount++
            } catch (e: Exception) {
                logger.error("Error preparing vehicle {} for batch processing: {}", externalVehicle.id, e.message, e)
                throw e // Re-throw to trigger fallback processing
            }
        }

        // Process all vehicles in a single batch operation
        if (vehiclesWithFields.isNotEmpty()) {
            eventService.batchStoreChangedFields(
                vehiclesWithFields = vehiclesWithFields,
                dataSource = DataSource.FRIDA,
                job = job,
                historicTimestamp = historicTimestamp,
            )
            logger.info("Batch processed {} vehicles with {} total field changes",
                vehiclesWithFields.size,
                vehiclesWithFields.sumOf { it.second.size })
        }

        return processedCount
    }

    @Transactional
    fun processVehicle(
        externalVehicle: ApiVehicleV2Model,
        job: FleetJob,
        historicTimestamp: Instant? = null,
    ) {
        val vehicle = getOrCreateVehicle(externalVehicle)
        val extractedFields = extractFields(externalVehicle)

        MdcUtils.setVehicleId(vehicle.vehicleId)
        MdcUtils.setVehicleRef(vehicle.vehicleRef)

        // Check for missing data
        if (vehicle.vehicleRef == null && vehicle.publicIdString == null) {
            logger.warn("Vehicle {} is missing both vehicleRef and publicIdString", vehicle.vehicleId)
        } else if (vehicle.vehicleRef == null) {
            logger.warn("Vehicle {} is missing vehicleRef", vehicle.vehicleId)
        }

        eventService.storeChangedFieldsFromVehicle(
            vehicle,
            extractedFields,
            DataSource.FRIDA,
            job,
            historicTimestamp,
        )
    }

    private fun extractFields(externalVehicle: ApiVehicleV2Model): Map<String, Any?> {
        val fieldsMap = mutableMapOf<String, Any?>()
        val vehicleClass = externalVehicle::class
        for (property in vehicleClass.memberProperties) {
            val fieldName = property.name
            if (fieldName == "customAttributeValues") {
                // handled later
                continue
            }
            try {
                val fieldValue = property.getter.call(externalVehicle)
                fieldsMap[fieldName] = fieldValue
            } catch (e: Exception) {
                logger.error("Error extracting field '${property.name}': ${e.message}", e)
            }
        }

        // process custom attribute values separately
        val customAttributes = externalVehicle.customAttributeValues ?: emptyList()
        for (customAttr in customAttributes) {
            try {
                val attributeName =
                    customAttr.vehicleAttributeName
                        ?: throw IllegalStateException("Can't store attribute without an attribute name")
                val attributeValue = customAttr.value
                val safeAttributeName = attributeName.replace("\\s+".toRegex(), "_")
                fieldsMap["customAttr_$safeAttributeName"] = attributeValue
            } catch (e: Exception) {
                logger.error("Error extracting custom attribute: ${e.message}", e)
            }
        }
        return fieldsMap
    }

    private fun getOrCreateVehicle(externalVehicle: ApiVehicleV2Model): InternalVehicle {
        val (vehicleRef, quality) = FleetVehicleRefGenerator.generateFleetVehicleRef(
            fridaId = externalVehicle.id
        )

        if (quality.type == QualityType.ENRICHED_VALUE) {
            logger.info("Generated vehicle reference for vehicle ${externalVehicle.id}: $vehicleRef")
        }

        val extractedFields = extractFields(externalVehicle)

        // we need to remove whitespaces, because frida likes to add them
        var publicIdString: String? = extractedFields["publicIDString"].toString().replace(" ", "")
        if (publicIdString == "null") {
            // just to be sure
            publicIdString = null
        }
        // fall back to temporaryPublicIDString
        val temporaryPublicIdString = extractedFields["temporaryPublicIDString"].toString()
        if (publicIdString == null && temporaryPublicIdString.isNotEmpty()) {
            publicIdString = temporaryPublicIdString
        }

        return vehicleRepository.findBySourceId(externalVehicle.id) ?: run {
            logger.info(
                "Creating new vehicle for FRIDA ID: {}, vehicleRef: {}",
                externalVehicle.id,
                vehicleRef,
            )

            val newVehicle =
                InternalVehicle(
                    dataSource = DataSource.FRIDA,
                    sourceId = externalVehicle.id,
                    publicIdString = publicIdString,
                    vehicleRef = vehicleRef,
                )
            vehicleRepository.save(newVehicle)
        }
    }

    private fun getChangedFields(vehicle: InternalVehicle, extractedFields: Map<String, Any?>): Map<String, Any?> {
        val existingFieldsMap = eventDataRepository.findCurrentFieldValuesByVehicleId(vehicle.vehicleId)

        return extractedFields.filter { (fieldName, newValue) ->
            val existingValue = existingFieldsMap[fieldName]
            val newValueString = newValue?.toString()
            existingValue != newValueString
        }
    }
}
