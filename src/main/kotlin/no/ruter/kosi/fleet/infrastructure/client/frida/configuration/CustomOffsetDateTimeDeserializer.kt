package no.ruter.kosi.fleet.infrastructure.client.frida.configuration

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class CustomOffsetDateTimeDeserializer : JsonDeserializer<OffsetDateTime>() {
    private val logger = LoggerFactory.getLogger(CustomOffsetDateTimeDeserializer::class.java)

    // we need this because Frida has inconsistent timestamps
    override fun deserialize(
        p: JsonParser,
        ctxt: DeserializationContext,
    ): OffsetDateTime? {
        val value = p.text
        return try {
            when {
                // handle ISO-8601 formatted string
                value.contains("T") -> parseIsoDate(value)

                // handle epoch time as a string
                value.contains(".") -> parseEpoch(value.split(".")[0])

                // handle epoch time as a number
                value.toLongOrNull() != null -> parseEpoch(value)

                else -> throw IllegalArgumentException("Unsupported date format: $value")
            }
        } catch (e: Exception) {
            logger.error("Failed to deserialize OffsetDateTime: $value", e)
            throw RuntimeException("Failed to parse OffsetDateTime: $value", e)
        }
    }

    private fun parseIsoDate(value: String): OffsetDateTime {
        val dateTimeText =
            if (value.contains("T") && !value.contains("Z") && !value.contains("+")) {
                "$value+00:00" // Append UTC offset if missing
            } else {
                value
            }
        return OffsetDateTime.parse(dateTimeText, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    private fun parseEpoch(value: String): OffsetDateTime =
        OffsetDateTime.ofInstant(Instant.ofEpochSecond(value.toLong()), ZoneId.of("UTC"))
}
