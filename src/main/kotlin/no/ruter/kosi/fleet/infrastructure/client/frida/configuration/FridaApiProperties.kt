package no.ruter.kosi.fleet.infrastructure.client.frida.configuration

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "frida.api")
data class FridaApiProperties(
    val clientId: String,
    val clientSecret: String,
    val username: String,
    val password: String,
    val scope: String,
    val tokenUrl: String,
    val baseUrl: String,
    val retryCount: Int = 3,
    val tokenExpiryDuration: Long = 300L, // 5 minutes in seconds
)
