package no.ruter.kosi.fleet.infrastructure.client.frida.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.OffsetDateTime

@Configuration
class JacksonConfig {
    @Bean
    fun objectMapper(): ObjectMapper {
        val customModule =
            SimpleModule().apply {
                addDeserializer(OffsetDateTime::class.java, CustomOffsetDateTimeDeserializer())
            }
        return ObjectMapper().apply {
            registerKotlinModule()
            registerModule(JavaTimeModule())
            registerModule(customModule)
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        }
    }
}
