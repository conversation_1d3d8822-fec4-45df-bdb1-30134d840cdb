package no.ruter.kosi.fleet.infrastructure.client.frida.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * manages tokens for talking to frida
 */
@Component
class TokenProvider(
    private val apiProperties: FridaApiProperties,
    private val objectMapper: ObjectMapper,
) {
    var tokenHttpClient: OkHttpClient = OkHttpClient()
    private val logger = LoggerFactory.getLogger(TokenProvider::class.java)

    // TODO abstract this away, it's only internal access for testing for now
    internal val tokenCache: Cache<String, String> =
        Caffeine
            .newBuilder()
            .expireAfterWrite(apiProperties.tokenExpiryDuration, TimeUnit.SECONDS)
            .maximumSize(1)
            .build()

    private val lock = ReentrantLock()

    /**
     * retrieves an access token
     *
     * If a valid token exists in the cache, it is returned
     * otherwise, fetches a new token from the API
     *
     * @return A valid access token as a {@link String}.
     * @throws IllegalStateException if the token cannot be retrieved.
     */
    fun getAccessToken(): String {
        val cacheKey = "accessToken-${apiProperties.clientId}-${apiProperties.scope}"
        return tokenCache.get(cacheKey) {
            lock.withLock {
                try {
                    fetchTokenFromApi()
                } catch (e: Exception) {
                    logger.error("Error fetching token: ${e.message}", e)
                    throw e
                }
            }
        } ?: throw IllegalStateException("Failed to retrieve token from cache")
    }

    private fun fetchTokenFromApi(): String {
        logger.info("Fetching new access token from the API")
        val requestBody =
            FormBody
                .Builder()
                .add("grant_type", "client_credentials")
                .add("client_id", apiProperties.clientId)
                .add("client_secret", apiProperties.clientSecret)
                .add("scope", apiProperties.scope)
                .build()

        val request =
            Request
                .Builder()
                .url(apiProperties.tokenUrl)
                .post(requestBody)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build()

        return retry(apiProperties.retryCount) {
            tokenHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    logger.error("Failed token request: ${response.code} ${response.message}")
                    if (response.code == 401) throw RuntimeException("Unauthorized")
                    throw RuntimeException("Failed to fetch token: ${response.code} ${response.message}")
                }

                val responseBody =
                    response.body?.string()
                        ?: throw IllegalStateException("Empty response body from token endpoint")

                val json =
                    try {
                        objectMapper.readTree(responseBody)
                    } catch (e: Exception) {
                        logger.error("Error parsing token response body: $responseBody", e)
                        throw IllegalStateException("Failed to parse token response", e)
                    }

                // validate presence of required fields
                val tokenNode = json.get("access_token")
                if (tokenNode == null || !tokenNode.isTextual) {
                    throw IllegalStateException("Missing or invalid required field: access_token")
                }
                val token = tokenNode.asText()

                val expiresInNode = json.get("expires_in")
                if (expiresInNode == null || !expiresInNode.isNumber) {
                    throw IllegalStateException("Missing or invalid required field: expires_in")
                }
                val expiresIn = expiresInNode.asLong()

                logger.info("Successfully fetched access token, expires in $expiresIn seconds")
                token
            }
        }
    }

    private fun <T> retry(
        times: Int,
        operation: () -> T,
    ): T {
        repeat(times - 1) {
            try {
                return operation()
            } catch (e: Exception) {
                logger.warn("Retrying operation after failure: ${e.message}", e)
            }
        }
        return operation()
    }
}
