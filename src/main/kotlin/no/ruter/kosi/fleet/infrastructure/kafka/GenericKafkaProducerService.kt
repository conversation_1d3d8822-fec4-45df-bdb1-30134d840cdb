package no.ruter.kosi.fleet.infrastructure.kafka

import arrow.core.Either
import org.slf4j.LoggerFactory
import org.springframework.kafka.core.KafkaTemplate
import java.util.concurrent.CompletableFuture
import org.springframework.kafka.support.SendResult

sealed class KafkaError {
    abstract val key: Any
    abstract val cause: Throwable?
    
    data class PublishError(
        override val key: Any,
        override val cause: Throwable,
        val topic: String
    ) : KafkaError()
    
    data class DisabledError(
        override val key: Any,
        override val cause: Throwable? = null
    ) : KafkaError()
}

open class GenericKafkaProducerService<K : Any, V>(
    private val kafkaTemplate: KafkaTemplate<K, V>,
    private val topic: String,
    private val kafkaPublishingEnabled: Boolean,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun sendMessage(key: K, message: V): Either<KafkaError, Unit> {
        if (!kafkaPublishingEnabled) {
            logger.info("Kafka publishing disabled. Skipping sending message for key: $key")
            return Either.Left(
                KafkaError.DisabledError(key = key)
            )
        }
        
        return try {
            val future = kafkaTemplate.send(topic, key, message)

            future.get()
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(
                KafkaError.PublishError(
                    key = key,
                    cause = e,
                    topic = topic
                )
            )
        }
    }
    
    fun sendMessageAsync(key: K, message: V): CompletableFuture<Either<KafkaError, SendResult<K, V>>> {
        if (!kafkaPublishingEnabled) {
            logger.info("Kafka publishing disabled. Skipping sending message for key: $key")
            val result = CompletableFuture<Either<KafkaError, SendResult<K, V>>>()
            result.complete(Either.Left(KafkaError.DisabledError(key = key)))
            return result
        }
        
        val resultFuture = CompletableFuture<Either<KafkaError, SendResult<K, V>>>()
        
        kafkaTemplate.send(topic, key, message)
            .whenComplete { result, exception ->
                if (exception != null) {
                    logger.debug("Failed to send message with key $key: ${exception.message}")
                    resultFuture.complete(
                        Either.Left(
                            KafkaError.PublishError(
                                key = key,
                                cause = exception,
                                topic = topic
                            )
                        )
                    )
                } else {
                    resultFuture.complete(Either.Right(result))
                }
            }
            
        return resultFuture
    }
}