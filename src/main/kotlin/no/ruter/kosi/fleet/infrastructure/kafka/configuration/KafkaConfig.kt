package no.ruter.kosi.fleet.infrastructure.kafka.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import io.confluent.kafka.serializers.KafkaAvroSerializer
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.EntityVehicleCapacityNpraDetails
import org.apache.kafka.common.serialization.StringSerializer
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.ssl.SslBundles
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.serializer.JsonSerde

@Configuration
@EnableConfigurationProperties(KafkaProperties::class)
class KafkaConfig(
    private val kafkaProperties: KafkaProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean
    fun vehicleProducerFactory(sslBundles: SslBundles): ProducerFactory<String, VehicleV3> {
        val props = kafkaProperties.buildProducerProperties(sslBundles)
        props["key.serializer"] = StringSerializer::class.java.name
        props["value.serializer"] = KafkaAvroSerializer::class.java.name
        return DefaultKafkaProducerFactory(props)
    }

    @Bean
    fun kafkaTemplate(producerFactory: ProducerFactory<String, VehicleV3>): KafkaTemplate<String, VehicleV3> =
        KafkaTemplate(producerFactory)

    @Bean
    fun vehicleCapacityProducerFactory(sslBundles: SslBundles): DefaultKafkaProducerFactory<String, EntityVehicleCapacityNpraDetails> {
        val props = kafkaProperties.buildProducerProperties(sslBundles)
        val serde = JsonSerde(EntityVehicleCapacityNpraDetails::class.java, objectMapper)
        return DefaultKafkaProducerFactory(props, StringSerializer(), serde.serializer())
    }

    @Bean
    fun vehicleCapacityKafkaTemplate(
        producerFactory: DefaultKafkaProducerFactory<String, EntityVehicleCapacityNpraDetails>,
    ): KafkaTemplate<String, EntityVehicleCapacityNpraDetails> = KafkaTemplate(producerFactory)
}
