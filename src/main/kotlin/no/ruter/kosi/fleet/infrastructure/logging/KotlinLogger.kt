package no.ruter.kosi.fleet.infrastructure.logging

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC

inline val <reified T : Any> T.logger: KotlinLogger
    get() = <PERSON><PERSON><PERSON>Logger(LoggerFactory.getLogger(T::class.java))

class KotlinLogger(val logger: Logger) {
    inline fun debug(codeAndMessage: Pair<String, String>, crossinline messageSupplier: () -> String) {
        if (logger.isDebugEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.debug("${codeAndMessage.second}: ${messageSupplier()}")
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun info(codeAndMessage: Pair<String, String>, crossinline messageSupplier: () -> String) {
        if (logger.isInfoEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.info("${codeAndMessage.second}: ${messageSupplier()}")
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun warn(codeAndMessage: Pair<String, String>, crossinline messageSupplier: () -> String) {
        if (logger.isWarnEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.warn("${codeAndMessage.second}: ${messageSupplier()}")
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun error(codeAndMessage: Pair<String, String>, crossinline messageSupplier: () -> String) {
        if (logger.isErrorEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.error("${codeAndMessage.second}: ${messageSupplier()}")
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }

    inline fun debug(crossinline messageSupplier: () -> String) {
        if (logger.isDebugEnabled) {
            logger.debug(messageSupplier())
        }
    }
    
    inline fun info(crossinline messageSupplier: () -> String) {
        if (logger.isInfoEnabled) {
            logger.info(messageSupplier())
        }
    }
    
    inline fun warn(crossinline messageSupplier: () -> String) {
        if (logger.isWarnEnabled) {
            logger.warn(messageSupplier())
        }
    }
    
    inline fun error(crossinline messageSupplier: () -> String) {
        if (logger.isErrorEnabled) {
            logger.error(messageSupplier())
        }
    }

    inline fun debug(throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isDebugEnabled) {
            logger.debug(messageSupplier(), throwable)
        }
    }
    
    inline fun info(throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isInfoEnabled) {
            logger.info(messageSupplier(), throwable)
        }
    }
    
    inline fun warn(throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isWarnEnabled) {
            logger.warn(messageSupplier(), throwable)
        }
    }
    
    inline fun error(throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isErrorEnabled) {
            logger.error(messageSupplier(), throwable)
        }
    }

    inline fun debug(codeAndMessage: Pair<String, String>, throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isDebugEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.debug("${codeAndMessage.second}: ${messageSupplier()}", throwable)
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun info(codeAndMessage: Pair<String, String>, throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isInfoEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.info("${codeAndMessage.second}: ${messageSupplier()}", throwable)
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun warn(codeAndMessage: Pair<String, String>, throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isWarnEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.warn("${codeAndMessage.second}: ${messageSupplier()}", throwable)
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
    
    inline fun error(codeAndMessage: Pair<String, String>, throwable: Throwable, crossinline messageSupplier: () -> String) {
        if (logger.isErrorEnabled) {
            val previousErrorCode = MDC.get("errorCode")
            try {
                MDC.put("errorCode", codeAndMessage.first)
                logger.error("${codeAndMessage.second}: ${messageSupplier()}", throwable)
            } finally {
                if (previousErrorCode != null) MDC.put("errorCode", previousErrorCode) else MDC.remove("errorCode")
            }
        }
    }
}
