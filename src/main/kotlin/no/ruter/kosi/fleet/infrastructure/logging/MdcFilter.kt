package no.ruter.kosi.fleet.infrastructure.logging

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.util.UUID

@Component
class MdcFilter : OncePerRequestFilter() {
    companion object {
        private const val CORRELATION_ID_HEADER = "X-Correlation-ID"
    }

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) {
        try {
            val correlationId = request.getHeader(CORRELATION_ID_HEADER) ?: UUID.randomUUID().toString()

            MdcUtils.setCorrelationId(correlationId)

            response.addHeader(CORRELATION_ID_HEADER, correlationId)

            filterChain.doFilter(request, response)
        } finally {
            MdcUtils.clearContext()
        }
    }
}
