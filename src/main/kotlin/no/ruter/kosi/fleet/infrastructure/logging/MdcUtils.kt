package no.ruter.kosi.fleet.infrastructure.logging

import org.slf4j.MDC
import java.util.UUID

object MdcUtils {
    private const val CORRELATION_ID = "correlationId"
    private const val IMPORT_ID = "importId"
    private const val JOB_ID = "jobId"
    private const val JOB_NAME = "jobName"
    private const val LAST_PROCESSED_VEHICLE_ID = "lastProcessedVehicleId"
    private const val LAST_PROCESSED_VEHICLE_REF = "lastProcessedVehicleRef"

    fun setCorrelationId(correlationId: String = UUID.randomUUID().toString()) {
        MDC.put(CORRELATION_ID, correlationId)
    }

    fun setJobId(jobId: Int?) {
        jobId?.let { MDC.put(JOB_ID, it.toString()) } ?: MDC.remove(JOB_ID)
    }

    fun setJobName(jobName: String?) {
        jobName?.let { MDC.put(JOB_NAME, it) } ?: MDC.remove(JOB_NAME)
    }

    fun setVehicleId(vehicleId: Int?) {
        vehicleId?.let { MDC.put(LAST_PROCESSED_VEHICLE_ID, it.toString()) } ?: MDC.remove(LAST_PROCESSED_VEHICLE_ID)
    }

    fun setVehicleRef(vehicleRef: String?) {
        vehicleRef?.let { MDC.put(LAST_PROCESSED_VEHICLE_REF, it) } ?: MDC.remove(LAST_PROCESSED_VEHICLE_REF)
    }

    fun clearContext() {
        MDC.remove(CORRELATION_ID)
        MDC.remove(IMPORT_ID)
        MDC.remove(JOB_ID)
        MDC.remove(JOB_NAME)
        MDC.remove(LAST_PROCESSED_VEHICLE_ID)
        MDC.remove(LAST_PROCESSED_VEHICLE_REF)
    }

}
