package no.ruter.kosi.fleet.infrastructure.persistence

import jakarta.annotation.PostConstruct
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.SnowflakeConfiguration
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component
import javax.sql.DataSource

@Configuration
@EnableAutoConfiguration
class FleetDataSourceConfiguration {

    @Bean
    @ConfigurationProperties("spring.datasource.aurora")
    fun auroraDataSourceProperties(): DataSourceProperties = DataSourceProperties()

    @Bean
    @Primary
    @QuartzDataSource
    fun auroraDataSource(): DataSource = auroraDataSourceProperties().initializeDataSourceBuilder().build()

    @Bean
    @ConfigurationProperties("spring.datasource.snowflake")
    fun snowflakeDataSourceProperties() = DataSourceProperties()

    @Bean(name = ["snowflakeDataSource"])
    fun snowflakeDataSource(): DataSource = snowflakeDataSourceProperties().initializeDataSourceBuilder().build()
}

@Component
class DataSourceLogger {
    private val logger = LoggerFactory.getLogger(DataSourceLogger::class.java)

    @Autowired
    private lateinit var context: ApplicationContext

    @PostConstruct
    fun logDataSources() {
        val dataSources = context.getBeansOfType(DataSource::class.java)
        logger.info("Available DataSources: ${dataSources.keys.joinToString(", ")}")

        if (SnowflakeConfiguration.isSnowflakeAvailable()) {
            logger.info("Snowflake connection is AVAILABLE")
        } else if (dataSources.keys.any { it.contains("snowflake", ignoreCase = true) }) {
            logger.info("Snowflake connection is UNAVAILABLE - using fallback")
        } else {
            logger.info("Snowflake is DISABLED")
        }
    }
}
