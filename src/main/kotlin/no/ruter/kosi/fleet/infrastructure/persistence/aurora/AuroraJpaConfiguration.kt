package no.ruter.kosi.fleet.infrastructure.persistence.aurora

import org.flywaydb.core.Flyway
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.sql.DataSource

@Configuration
@Primary
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = [
        "no.ruter.kosi.fleet.infrastructure.persistence.aurora",
    ],
    entityManagerFactoryRef = "auroraEntityManagerFactory",
    transactionManagerRef = "auroraTransactionManager",
)
class AuroraJpaConfiguration {
    @Bean
    @Primary
    fun auroraEntityManagerFactory(
        @Qualifier("auroraDataSource") dataSource: DataSource?,
        @Qualifier("auroraEntityManagerFactoryBuilder") builder: EntityManagerFactoryBuilder,
    ): LocalContainerEntityManagerFactoryBean {
        Flyway
            .configure()
            .dataSource(dataSource)
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .locations("db/migration/aurora")
            .load()
            .migrate()
        return builder
            .dataSource(dataSource)
            .packages(
                "no.ruter.kosi.fleet.domain",
            ).build()
    }

    @Bean
    @Primary
    fun auroraTransactionManager(
        @Qualifier("auroraEntityManagerFactory") auroraEntityManagerFactory: LocalContainerEntityManagerFactoryBean,
    ): PlatformTransactionManager = JpaTransactionManager(auroraEntityManagerFactory.`object`!!)

    @Bean
    @Primary
    @Qualifier("auroraEntityManagerFactoryBuilder")
    fun auroraEntityManagerFactoryBuilder(jpaProperties: JpaProperties): EntityManagerFactoryBuilder =
        EntityManagerFactoryBuilder(HibernateJpaVendorAdapter(), jpaProperties.properties, null)
}
