package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.vehicle.contract.ContractorEvent
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataContractorEventRepository
import org.springframework.stereotype.Repository

@Repository
class AuroraContractEventRepository(
    private val jpaRepository: SpringDataContractorEventRepository,
) {
    fun save(contractorEvent: ContractorEvent): ContractorEvent = jpaRepository.save(contractorEvent)

    fun findByVehicleId(vehicleId: Int): List<ContractorEvent> = jpaRepository.findByVehicleVehicleId(vehicleId)

    fun findAll(): List<ContractorEvent> = jpaRepository.findAll()

    fun deleteAll() {
        jpaRepository.deleteAll()
    }
}
