package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataContractRepository
import org.springframework.stereotype.Repository

@Repository
class AuroraContractRepository(
    private val jpaRepository: SpringDataContractRepository,
) {
    fun save(contract: Contract): Contract = jpaRepository.save(contract)

    fun findById(id: Int): Contract? = jpaRepository.findByContractId(id)

    fun findByFridaId(id: Int): Contract? = jpaRepository.findByContractIdInFrida(id)

    fun findAll(): List<Contract> = jpaRepository.findAll()

    fun deleteAll() {
        jpaRepository.deleteAll()
    }
}
