package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.infrastructure.logging.KotlinLogger
import no.ruter.kosi.fleet.infrastructure.logging.LogCodes
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
class AuroraEventDataRepository(
    private val jpaRepository: SpringDataEventDataRepository,
    private val scd2Service: ISCD2Service<EventData, Int, String>
) {
    private val logger = KotlinLogger(LoggerFactory.getLogger(AuroraEventDataRepository::class.java))

    fun save(event: EventData): EventData {
        if (!event.effectiveFrom.isInitialized()) {
            event.effectiveFrom = Instant.now()
        }
        event.isCurrent = true

        logger.debug(LogCodes.DB_TRANSACTION_ERROR to "Saving event data") {
            "Saving event data"
        }
        
        return scd2Service.saveNewVersion(event)
    }

    fun saveAll(events: List<EventData>): List<EventData> {
        logger.debug(LogCodes.DB_TRANSACTION_ERROR to "Saving multiple event data") {
            "Saving ${events.size} event data records"
        }
        return jpaRepository.saveAll(events)
    }

    fun findByVehicleId(vehicleId: Int): List<EventData> = jpaRepository.findCurrentByVehicleId(vehicleId)

    /**
     * Optimised method for change detection' only returns field names and values
     * instead of full EventData objects with joins. Should be umch faster for large datasets.
     */
    fun findCurrentFieldValuesByVehicleId(vehicleId: Int): Map<String, String?> {
        return jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId)
            .associate { row ->
                val fieldName = row[0] as String
                val fieldValue = row[1] as String?
                fieldName to fieldValue
            }
    }

    fun deleteAll() {
        jpaRepository.deleteAll()
    }

    fun findAll(): List<EventData> = jpaRepository.findAll()

    fun findByEventDataId(id: Int): EventData? = jpaRepository.findById(id).orElse(null)

    fun findByVehicleRef(vehicleRef: String): List<EventData> = jpaRepository.findCurrentByVehicleRef(vehicleRef)

    fun findLatestByVehicleRef(vehicleRef: String): List<EventData> = findByVehicleRef(vehicleRef)

    fun findWithFilters(
        vehicleId: Int?,
        vehicleRef: String?,
        fieldName: String?,
        fieldValue: String?,
        startDate: String?,
        endDate: String?,
        isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData> {
        if (startDate == null && endDate == null) {
            return jpaRepository.findWithFiltersNoDates(
                vehicleId,
                vehicleRef,
                fieldName,
                fieldValue,
                isCurrent,
                pageable
            )
        }
        
        val startInstant = startDate?.let { parseIsoDate(it) }
        val endInstant = endDate?.let { parseIsoDate(it) }
        
        // we have start date but no end date
        if (startInstant != null && endInstant == null) {
            return jpaRepository.findWithStartDate(
                vehicleId,
                vehicleRef,
                fieldName,
                fieldValue,
                startInstant,
                isCurrent,
                pageable
            )
        }
        
        if (startInstant == null && endInstant != null) {
            return jpaRepository.findWithEndDate(
                vehicleId,
                vehicleRef,
                fieldName,
                fieldValue,
                endInstant,
                isCurrent,
                pageable
            )
        }
        
        if (startInstant != null && endInstant != null) {
            return jpaRepository.findWithDateRange(
                vehicleId,
                vehicleRef,
                fieldName,
                fieldValue,
                startInstant,
                endInstant,
                isCurrent,
                pageable
            )
        }
        
        // fallback
        return jpaRepository.findWithFiltersNoDates(
            vehicleId,
            vehicleRef,
            fieldName,
            fieldValue,
            isCurrent,
            pageable
        )
    }
    
    private fun parseIsoDate(dateString: String): Instant? {
        return try {
            Instant.parse(dateString)
        } catch (e: Exception) {
            null
        }
    }

    // TODO probably don't need this anymore, but check
    private fun Instant.isInitialized(): Boolean = this != Instant.EPOCH && this.toEpochMilli() != 0L
}