package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventRepository
import org.springframework.stereotype.Repository

@Repository
class AuroraEventRepository(
    private val jpaRepository: SpringDataEventRepository,
) {
    fun save(event: Event): Event = jpaRepository.save(event)

    fun saveAll(events: List<Event>): List<Event> = jpaRepository.saveAll(events)

    fun findByVehicleId(vehicleId: Int): List<Event> = jpaRepository.findByVehicleVehicleIdOrderByTimestamp(vehicleId)

    fun findByVehicleRef(vehicleRef: String): List<Event> = jpaRepository.findByVehicleVehicleRefOrderByTimestamp(vehicleRef)

    fun findAll(): List<Event> = jpaRepository.findAll()

    fun deleteAll() {
        jpaRepository.deleteAll()
    }
}
