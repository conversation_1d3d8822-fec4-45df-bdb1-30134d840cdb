package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataInternalVehicleRepository
import org.springframework.stereotype.Repository

@Repository
class AuroraInternalVehicleRepository(
    private val jpaRepository: SpringDataInternalVehicleRepository,
) {
    fun save(vehicle: InternalVehicle): InternalVehicle = jpaRepository.save(vehicle)

    fun exists(vehicleRef: String): Boolean = jpaRepository.existsByVehicleRef(vehicleRef)

    fun findByVehicleRef(vehicleRef: String): InternalVehicle? = jpaRepository.findByVehicleRefWithEvents(vehicleRef)

    fun findBySourceId(sourceId: Int): InternalVehicle? = jpaRepository.findBySourceId(sourceId)

    fun findAll(): List<InternalVehicle> = jpaRepository.findAll()

    fun deleteAll() {
        jpaRepository.deleteAll()
    }

}
