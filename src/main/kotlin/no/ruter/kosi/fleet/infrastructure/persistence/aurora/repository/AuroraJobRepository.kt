package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataJobRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
class AuroraJobRepository(
    private val jpaRepository: SpringDataJobRepository,
) {
    private val logger = LoggerFactory.getLogger(AuroraJobRepository::class.java)

    fun save(fleetJob: FleetJob): FleetJob = jpaRepository.save(fleetJob)
    fun flush() = jpaRepository.flush()

    fun findById(id: Int): FleetJob? = jpaRepository.findById(id).orElse(null)

    fun findAll(): List<FleetJob> = jpaRepository.findAll()

    fun findByStatusIn(statuses: List<FleetJob.JobStatus>): List<FleetJob> = jpaRepository.findByStatusIn(statuses)

    fun findLatestJobByType(type: FleetJob.JobType): FleetJob? = jpaRepository.findLatestJobByType(type)

    fun findByParentJobId(id: Int): List<FleetJob> = jpaRepository.findByParentJobId(id)

    fun delete(fleetJob: FleetJob) {
        val childJobs = jpaRepository.findByParentJobId(fleetJob.jobId)
        if (childJobs.isNotEmpty()) {
            jpaRepository.deleteAll(childJobs)
        }

        jpaRepository.delete(fleetJob)
    }

    @Transactional
    fun deleteAll() {
        try {
            val allJobs = jpaRepository.findAll()

            val childJobs = allJobs.filter { it.parentJobId != null }
            val parentJobs = allJobs.filter { it.parentJobId == null }

            if (childJobs.isNotEmpty()) {
                jpaRepository.deleteAll(childJobs)
                jpaRepository.flush()
            }

            if (parentJobs.isNotEmpty()) {
                jpaRepository.deleteAll(parentJobs)
            }
        } catch (e: Exception) {
            logger.warn("Standard job deletion failed: ${e.message}. Trying direct SQL deletion.")
            try {
                jpaRepository.deleteAllWithNativeQuery()
            } catch (innerEx: Exception) {
                logger.error("Failed to delete jobs: ${innerEx.message}")
                throw innerEx
            }
        }
    }
}