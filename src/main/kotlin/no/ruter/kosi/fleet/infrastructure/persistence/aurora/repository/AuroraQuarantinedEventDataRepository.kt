package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.quarantine.QuarantinedEventData
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataQuarantinedEventDataRepository
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
class AuroraQuarantinedEventDataRepository(
    private val jpaRepository: SpringDataQuarantinedEventDataRepository,
) {
    fun save(event: QuarantinedEventData): QuarantinedEventData = jpaRepository.save(event)

    fun saveAll(events: List<QuarantinedEventData>): List<QuarantinedEventData> = jpaRepository.saveAll(events)

    fun findByVehicleId(vehicleId: Int): List<QuarantinedEventData> = jpaRepository.findByEventVehicleVehicleId(vehicleId)

    fun findAll(): List<QuarantinedEventData> = jpaRepository.findAll()

    @Transactional
    fun deleteByVehicleId(vehicleId: Int) {
        jpaRepository.deleteByEventVehicleVehicleId(vehicleId)
    }

    fun deleteAll() {
        jpaRepository.deleteAll()
    }
}
