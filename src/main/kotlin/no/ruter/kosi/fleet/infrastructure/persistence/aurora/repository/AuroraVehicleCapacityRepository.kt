package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.VehiclePassengerCapacityEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleCapacityRepository
import org.springframework.stereotype.Repository
import org.springframework.context.annotation.Primary

@Repository
@Primary
class AuroraVehicleCapacityRepository(
    private val jpaRepository: SpringDataVehicleCapacityRepository,
) {
    fun save(capacity: VehiclePassengerCapacityEntity): VehiclePassengerCapacityEntity = jpaRepository.save(capacity)

    fun findById(id: Int): VehiclePassengerCapacityEntity? = jpaRepository.findById(id).orElse(null)

    fun findAll(): List<VehiclePassengerCapacityEntity> = jpaRepository.findAll().toList()

    fun deleteAll() = jpaRepository.deleteAll()
}