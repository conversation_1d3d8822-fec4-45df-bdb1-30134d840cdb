package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import arrow.core.Either
import arrow.core.right
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleFieldQualityRepository
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
class AuroraVehicleFieldQualityRepository(
    private val jpaRepository: SpringDataVehicleFieldQualityRepository
) {

    fun save(vehicleFieldQualityEntity: VehicleFieldQualityEntity): Either<Throwable, VehicleFieldQualityEntity> {
        return try {
            jpaRepository.save(vehicleFieldQualityEntity).right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun saveAll(entities: List<VehicleFieldQualityEntity>): Either<Throwable, List<VehicleFieldQualityEntity>> {
        return try {
            jpaRepository.saveAll(entities).toList().right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun findById(id: Int): Either<Throwable, VehicleFieldQualityEntity?> {
        return try {
            jpaRepository.findById(id).orElse(null).right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun findByVehicleQualityId(vehicleQualityId: Int): Either<Throwable, List<VehicleFieldQualityEntity>> {
        return try {
            jpaRepository.findByVehicleQualityId(vehicleQualityId).right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun deleteAll(): Either<Throwable, Unit> {
        return try {
            jpaRepository.deleteAll()
            Unit.right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun deleteByVehicleQualityId(vehicleQualityId: Int): Either<Throwable, Unit> {
        return try {
            jpaRepository.deleteByVehicleQualityId(vehicleQualityId)
            Unit.right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }
}