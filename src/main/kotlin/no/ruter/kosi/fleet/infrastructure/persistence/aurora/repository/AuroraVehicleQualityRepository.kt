package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleQualityRepository
import org.springframework.stereotype.Repository

@Repository
class AuroraVehicleQualityRepository(
    private val jpaRepository: SpringDataVehicleQualityRepository,
) {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    fun findCurrentByVehicleId(vehicleId: Int): VehicleQualityEntity? =
        jpaRepository.findCurrentByVehicleId(vehicleId)

    fun findByVehicleId(vehicleId: Int): VehicleQualityEntity? = findCurrentByVehicleId(vehicleId)

    fun findById(id: Int): VehicleQualityEntity? = jpaRepository.findById(id).orElse(null)

    fun delete(vehicleQuality: VehicleQualityEntity) {
        jpaRepository.delete(vehicleQuality)
    }

    fun deleteAll() = jpaRepository.deleteAll()

}
