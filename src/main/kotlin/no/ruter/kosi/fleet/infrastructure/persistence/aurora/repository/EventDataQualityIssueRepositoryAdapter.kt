package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssueRepositoryPort
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.infrastructure.logging.KotlinLogger
import no.ruter.kosi.fleet.infrastructure.logging.LogCodes
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataQualityIssueRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository("auroraEventDataQualityIssueRepository")
class EventDataQualityIssueRepositoryAdapter(
    private val jpaRepository: SpringDataEventDataQualityIssueRepository,
    @Qualifier("eventDataQualityIssueSCD2Service") private val scd2Service: ISCD2Service<EventDataQualityIssue, Int, String>
) : EventDataQualityIssueRepositoryPort {
    private val logger = KotlinLogger(LoggerFactory.getLogger(EventDataQualityIssueRepositoryAdapter::class.java))

    override fun save(issue: EventDataQualityIssue): EventDataQualityIssue {
        if (!issue.effectiveFrom.isInitialized()) {
            issue.effectiveFrom = Instant.now()
        }
        issue.isCurrent = true

        logger.debug(LogCodes.DB_TRANSACTION_ERROR to "Saving event data quality issue") {
            "Saving event data quality issue for eventDataId: ${issue.eventDataId}, field: ${issue.fieldName}, type: ${issue.type}"
        }
        
        return scd2Service.saveNewVersion(issue)
    }
    
    override fun findById(id: Int): EventDataQualityIssue? = jpaRepository.findById(id).orElse(null)
    
    override fun findByEventDataId(eventDataId: Int): List<EventDataQualityIssue> = 
        jpaRepository.findByEventDataId(eventDataId)
        
    override fun findCurrentByEventDataId(eventDataId: Int): List<EventDataQualityIssue> =
        jpaRepository.findCurrentByEventDataId(eventDataId)
        
    override fun findCurrentByEventDataIds(eventDataIds: List<Int>): List<EventDataQualityIssue> =
        jpaRepository.findCurrentByEventDataIds(eventDataIds)
        
    override fun findWithFilters(
        eventDataId: Int?,
        fieldName: String?,
        type: String?,
        isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventDataQualityIssue> {
        val qualityType = type?.let { QualityType.valueOf(it) }
        return jpaRepository.findWithFilters(eventDataId, fieldName, qualityType, isCurrent, pageable)
    }

    private fun Instant.isInitialized(): Boolean = this != Instant.EPOCH && this.toEpochMilli() != 0L
}
