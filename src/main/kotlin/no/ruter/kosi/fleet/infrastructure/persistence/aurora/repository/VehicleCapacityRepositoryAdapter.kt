package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository

import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.VehiclePassengerCapacityEntity
import org.springframework.stereotype.Repository

@Repository("auroraVehicleCapacityRepositoryPort")
class VehicleCapacityRepositoryAdapter(
    private val jpaRepository: no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleCapacityRepository,
) : AuroraVehicleCapacityRepository(jpaRepository) {
    override fun save(capacity: VehiclePassengerCapacityEntity): VehiclePassengerCapacityEntity = jpaRepository.save(capacity)

    override fun findById(id: Int): VehiclePassengerCapacityEntity? = jpaRepository.findById(id).orElse(null)

    override fun findAll(): List<VehiclePassengerCapacityEntity> = jpaRepository.findAll()

    override fun deleteAll() {
        jpaRepository.deleteAll()
    }
}
