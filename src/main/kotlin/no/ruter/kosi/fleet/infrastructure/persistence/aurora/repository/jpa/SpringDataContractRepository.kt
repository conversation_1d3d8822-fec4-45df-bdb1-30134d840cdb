package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SpringDataContractRepository : JpaRepository<Contract, Int> {

    fun findByContractId(id: Int): Contract?

    fun findByContractIdInFrida(id: Int): Contract?
}
