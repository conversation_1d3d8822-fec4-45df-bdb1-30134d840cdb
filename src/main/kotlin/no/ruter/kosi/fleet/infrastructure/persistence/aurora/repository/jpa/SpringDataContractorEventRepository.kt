package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.vehicle.contract.ContractorEvent
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SpringDataContractorEventRepository : JpaRepository<ContractorEvent, Int> {
    fun findByVehicleVehicleId(vehicleId: Int): List<ContractorEvent>

    fun findTopByVehicleVehicleIdOrderByTimestampDesc(vehicleId: Int): ContractorEvent

    fun findByVehicleVehicleRef(vehicleRef: String): List<ContractorEvent>

    fun findTopByVehicleVehicleRefOrderByTimestampDesc(vehicleRef: String): ContractorEvent?
}
