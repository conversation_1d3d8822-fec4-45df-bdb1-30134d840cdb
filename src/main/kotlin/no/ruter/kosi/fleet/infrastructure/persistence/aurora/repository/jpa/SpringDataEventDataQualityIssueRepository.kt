package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.scd2.SCD2RepositoryBase
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SpringDataEventDataQualityIssueRepository : SCD2RepositoryBase<EventDataQualityIssue, Int, String> {
    
    @Query(
        """
        SELECT e FROM EventDataQualityIssue e
        WHERE (:eventDataId IS NULL OR e.eventDataId = :eventDataId)
        AND (:fieldName IS NULL OR e.fieldName = :fieldName)
        AND (:type IS NULL OR e.type = :type)
        AND (:isCurrent IS NULL OR e.isCurrent = :isCurrent)
    """
    )
    fun findWithFilters(
        @Param("eventDataId") eventDataId: Int?,
        @Param("fieldName") fieldName: String?,
        @Param("type") type: no.ruter.kosi.fleet.domain.quality.QualityType?,
        @Param("isCurrent") isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventDataQualityIssue>
    
    fun findByEventDataId(eventDataId: Int): List<EventDataQualityIssue>
    
    @Query(
        """
        SELECT e FROM EventDataQualityIssue e 
        WHERE e.eventDataId = :eventDataId 
        AND e.isCurrent = true
    """
    )
    fun findCurrentByEventDataId(@Param("eventDataId") eventDataId: Int): List<EventDataQualityIssue>
    
    @Query(
        """
        SELECT e FROM EventDataQualityIssue e 
        WHERE e.eventDataId IN :eventDataIds 
        AND e.isCurrent = true
    """
    )
    fun findCurrentByEventDataIds(@Param("eventDataIds") eventDataIds: List<Int>): List<EventDataQualityIssue>
}
