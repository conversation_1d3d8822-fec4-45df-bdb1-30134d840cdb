package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.event.entities.Event
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SpringDataEventRepository : JpaRepository<Event, Int> {
    fun findByVehicleVehicleIdOrderByTimestamp(vehicleId: Int): List<Event>

    fun findByVehicleVehicleRefOrderByTimestamp(vehicleRef: String): List<Event>
}
