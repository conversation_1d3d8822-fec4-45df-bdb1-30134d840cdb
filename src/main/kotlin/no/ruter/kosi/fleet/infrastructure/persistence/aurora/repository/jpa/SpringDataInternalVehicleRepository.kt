package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SpringDataInternalVehicleRepository : JpaRepository<InternalVehicle, Int> {
    fun findBySourceId(sourceId: Int): InternalVehicle?

    @Query("SELECT iv FROM InternalVehicle iv LEFT JOIN FETCH iv.events WHERE iv.vehicleRef = :vehicleRef")
    fun findByVehicleRefWithEvents(@Param("vehicleRef") vehicleRef: String): InternalVehicle?

    fun existsByVehicleRef(vehicleRef: String): Boolean
}
