package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.job.FleetJob
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SpringDataJobRepository : JpaRepository<FleetJob, Int> {
    fun findByStatusIn(statuses: List<FleetJob.JobStatus>): List<FleetJob>

    @Query("SELECT j FROM FleetJob j WHERE j.parentJobId = :parentJobId")
    fun findByParentJobId(
        @Param("parentJobId") parentJobId: Int,
    ): List<FleetJob>

    @Query("SELECT j FROM FleetJob j WHERE j.type = :type ORDER BY j.createdAt DESC LIMIT 1")
    fun findLatestJobByType(
        @Param("type") type: FleetJob.JobType,
    ): FleetJob?

    @Modifying
    @Query(value = "DELETE FROM jobs", nativeQuery = true)
    fun deleteAllWithNativeQuery()
}
