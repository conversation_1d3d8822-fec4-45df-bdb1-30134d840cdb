package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.quarantine.QuarantinedEventData
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface SpringDataQuarantinedEventDataRepository : JpaRepository<QuarantinedEventData, Long> {
    fun findByEventVehicleVehicleId(vehicleId: Int): List<QuarantinedEventData>

    @Modifying
    @Transactional
    @Query("DELETE FROM QuarantinedEventData q WHERE q.event.vehicle.vehicleId = :vehicleId")
    fun deleteByEventVehicleVehicleId(
        @Param("vehicleId") vehicleId: Int,
    )
}
