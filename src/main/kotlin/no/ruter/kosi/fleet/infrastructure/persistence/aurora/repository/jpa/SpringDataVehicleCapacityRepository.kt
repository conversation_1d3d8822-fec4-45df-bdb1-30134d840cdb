package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.VehiclePassengerCapacityEntity
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
@Qualifier("auroraVehicleCapacityRepositoryPort")
interface SpringDataVehicleCapacityRepository : JpaRepository<VehiclePassengerCapacityEntity, Int>
