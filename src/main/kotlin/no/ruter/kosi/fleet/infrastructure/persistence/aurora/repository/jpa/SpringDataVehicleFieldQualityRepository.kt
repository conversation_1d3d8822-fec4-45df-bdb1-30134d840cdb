package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2Repository
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.Instant

@Repository
interface SpringDataVehicleFieldQualityRepository : JpaRepository<VehicleFieldQualityEntity, Int> {
    
    fun findByVehicleQualityId(vehicleQualityId: Int): List<VehicleFieldQualityEntity>

    fun deleteByVehicleQualityId(vehicleQualityId: Int)
}