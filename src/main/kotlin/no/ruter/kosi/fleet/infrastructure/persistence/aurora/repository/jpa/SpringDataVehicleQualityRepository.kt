package no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa

import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.scd2.SCD2Repository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface SpringDataVehicleQualityRepository : SCD2Repository<VehicleQualityEntity, Int, String> {
    @Query(
        """
        SELECT vq 
        FROM VehicleQualityEntity vq
        WHERE vq.vehicleId = :vehicleId
        AND vq.isCurrent = true
        """,
    )
    fun findCurrentByVehicleId(
        @Param("vehicleId") vehicleId: Int,
    ): VehicleQualityEntity?
}
