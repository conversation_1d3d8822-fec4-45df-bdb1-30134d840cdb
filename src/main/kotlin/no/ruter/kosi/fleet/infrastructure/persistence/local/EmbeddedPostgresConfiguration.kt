package no.ruter.kosi.fleet.infrastructure.persistence.local

import io.zonky.test.db.postgres.embedded.EmbeddedPostgres
import jakarta.annotation.PreDestroy
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import javax.sql.DataSource

@Configuration
@Profile("local & !test")
@ConditionalOnProperty(name = ["spring.profiles.active"], havingValue = "local")
class EmbeddedPostgresConfiguration {
    
    private val logger = LoggerFactory.getLogger(EmbeddedPostgresConfiguration::class.java)
    private var embeddedPostgres: EmbeddedPostgres? = null
    
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource.aurora")
    fun localAuroraDataSourceProperties(): DataSourceProperties = DataSourceProperties()
    
    @Bean
    @Primary
    fun localAuroraDataSource(): DataSource {
        logger.info("Starting embedded PostgreSQL for local development...")

        embeddedPostgres = try {
            EmbeddedPostgres.builder()
                .setPort(5432)
                .start()
        } catch (e: Exception) {
            logger.warn("Port 5432 is in use, using random available port")
            EmbeddedPostgres.builder()
                .setPort(0) // Use random available port
                .start()
        }

        val dataSource = embeddedPostgres!!.postgresDatabase
        logger.info("Embedded PostgreSQL started successfully on port ${embeddedPostgres!!.port}")

        return dataSource
    }
    
    @Bean
    @ConfigurationProperties("spring.datasource.snowflake")
    fun localSnowflakeDataSourceProperties(): DataSourceProperties = DataSourceProperties()
    
    @Bean(name = ["snowflakeDataSource"])
    fun localSnowflakeDataSource(): DataSource {
        logger.info("Using embedded PostgreSQL for Snowflake fallback in local development...")

        if (embeddedPostgres == null) {
            embeddedPostgres = try {
                EmbeddedPostgres.builder()
                    .setPort(5432)
                    .start()
            } catch (e: Exception) {
                logger.warn("Port 5432 is in use, using random available port")
                EmbeddedPostgres.builder()
                    .setPort(0)
                    .start()
            }
        }

        return embeddedPostgres!!.postgresDatabase
    }
    
    @PreDestroy
    fun cleanup() {
        embeddedPostgres?.close()
        logger.info("Embedded PostgreSQL stopped")
    }
}
