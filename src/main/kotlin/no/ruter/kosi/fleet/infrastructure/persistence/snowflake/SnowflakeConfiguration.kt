package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.datasource.SimpleDriverDataSource
import java.sql.SQLException
import java.util.concurrent.atomic.AtomicBoolean
import javax.sql.DataSource
import java.io.File

@Configuration
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true", matchIfMissing = true)
class SnowflakeConfiguration(
    private val snowflakeProperties: SnowflakeProperties,
) {
    private val logger = LoggerFactory.getLogger(SnowflakeConfiguration::class.java)

    companion object {
        private val snowflakeAvailable = AtomicBoolean(false)

        fun isSnowflakeAvailable(): Boolean = snowflakeAvailable.get()

        fun setSnowflakeAvailable(available: Boolean) {
            snowflakeAvailable.set(available)
        }
    }

    @Bean("snowflakeDataSource")
    fun snowflakeDataSource(): DataSource =
        try {
            val tempDir = System.getProperty("java.io.tmpdir")
            val keyFilePath = File(tempDir, "snowflake_rsa_key.p8").absolutePath
        
            val dataSourceProps =
                DataSourceProperties().apply {
                    username = "KOSI_SERVICE_USER"
                    driverClassName = "net.snowflake.client.jdbc.SnowflakeDriver"
                    url = "jdbc:snowflake://${snowflakeProperties.url}?private_key_file=$keyFilePath" +
                    "&ocspFailOpen=false&tracing=WARNING&client_result_column_case_insensitive=true&role=${snowflakeProperties.role}" +
                        "&schema=${snowflakeProperties.schema}"
                }

        // Create the data source but test the connection before returning it
        val dataSource = dataSourceProps.initializeDataSourceBuilder().build()
        testConnection(dataSource)
        setSnowflakeAvailable(true)
        logger.info("Successfully connected to Snowflake")
        dataSource
    } catch (e: Exception) {
        logger.error("Failed to connect to Snowflake: ${e.message}. Using H2 fallback.")
        setSnowflakeAvailable(false)

        // Create a simple in-memory fallback database
        val fallbackDataSource = SimpleDriverDataSource()
        fallbackDataSource.setDriverClass(org.h2.Driver::class.java)
        fallbackDataSource.url = "jdbc:h2:mem:snowflake_fallback;DB_CLOSE_DELAY=-1"
        fallbackDataSource.username = "sa"
        fallbackDataSource.password = ""
        fallbackDataSource
    }

    // Helper method to test database connection
    private fun testConnection(dataSource: DataSource) {
        try {
            dataSource.connection.use { connection ->
                val valid = connection.isValid(5) // 5 second timeout
                if (!valid) {
                    throw SQLException("Connection test failed")
                }
            }
        } catch (e: Exception) {
            logger.error("Error testing Snowflake connection: ${e.message}")
            throw e
        }
    }
}