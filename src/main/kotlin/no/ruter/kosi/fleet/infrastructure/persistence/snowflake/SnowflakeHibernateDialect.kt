package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import net.snowflake.client.jdbc.internal.org.bouncycastle.asn1.x500.style.RFC4519Style.l
import org.hibernate.boot.model.TypeContributions
import org.hibernate.dialect.Dialect
import org.hibernate.dialect.DatabaseVersion
import org.hibernate.service.ServiceRegistry
import org.hibernate.type.descriptor.sql.internal.DdlTypeImpl
import org.hibernate.type.descriptor.sql.spi.DdlTypeRegistry
import java.sql.Types

class SnowflakeHibernateDialect : Dialect(DatabaseVersion.make(6)) {

    override fun contributeTypes(typeContributions: TypeContributions, serviceRegistry: ServiceRegistry) {
        val ddlTypeRegistry: DdlTypeRegistry = typeContributions.typeConfiguration.ddlTypeRegistry

        ddlTypeRegistry.addDescriptor(
            DdlTypeImpl(
                Types.VARCHAR,
                false,
                "VARCHAR($l)",
                "VARCHAR",
                this
            )
        )

        ddlTypeRegistry.addDescriptor(
            DdlTypeImpl(
                Types.INTEGER,
                false,
                "NUMBER(10,0)",
                "NUMBER",
                this
            )
        )

        ddlTypeRegistry.addDescriptor(
            DdlTypeImpl(
                Types.BIGINT,
                false,
                "NUMBER(19,0)",
                "NUMBER",
                this
            )
        )

        ddlTypeRegistry.addDescriptor(
            DdlTypeImpl(
                Types.BOOLEAN,
                false,
                "BOOLEAN",
                "BOOLEAN",
                this
            )
        )

        ddlTypeRegistry.addDescriptor(
            DdlTypeImpl(
                Types.TIMESTAMP,
                false,
                "TIMESTAMP_LTZ",
                "TIMESTAMP_LTZ",
                this
            )
        )
    }

    override fun getCurrentTimestampSelectString(): String = "CURRENT_TIMESTAMP()"
}