package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import org.flywaydb.core.Flyway
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import javax.sql.DataSource

@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@EnableJpaRepositories(
    basePackages = [
        "no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa",
    ],
    entityManagerFactoryRef = "snowflakeEntityManagerFactory",
    transactionManagerRef = "snowflakeTransactionManager",
)
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true", matchIfMissing = true)
class SnowflakeJpaConfiguration(
    private val snowflakeProperties: SnowflakeProperties,
) {
    private val logger = LoggerFactory.getLogger(SnowflakeJpaConfiguration::class.java)

    @Bean(name = ["snowflakeEntityManagerFactory"])
    fun snowflakeEntityManagerFactory(
        @Qualifier("snowflakeDataSource") dataSource: DataSource?,
        @Qualifier("snowflakeEntityManagerFactoryBuilder") builder: EntityManagerFactoryBuilder,
    ): LocalContainerEntityManagerFactoryBean {
        try {
            if (SnowflakeConfiguration.isSnowflakeAvailable()) {
                Flyway
                    .configure()
                    .dataSource(dataSource)
                    .locations("db/migration/snowflake")
                    .baselineOnMigrate(true)
                    .schemas(snowflakeProperties.schema)
                    .baselineVersion("0")
                    .load()
                    .migrate()
            } else {
                logger.warn("Skipping Flyway migrations because Snowflake is unavailable")
            }
        } catch (e: Exception) {
            logger.error("Failed to run Snowflake migrations: ${e.message}")
        }

        return builder
            .dataSource(dataSource)
            .properties(
                mapOf(
                    "hibernate.dialect" to "no.ruter.kosi.fleet.infrastructure.persistence.snowflake.SnowflakeHibernateDialect",
                    "hibernate.hbm2ddl.auto" to "none",
                )
            )
            .packages(
                "no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities"
            ).build()
    }

    @Bean
    fun snowflakeTransactionManager(
        @Qualifier("snowflakeEntityManagerFactory") snowflakeEntityManagerFactory: LocalContainerEntityManagerFactoryBean,
    ): PlatformTransactionManager = JpaTransactionManager(snowflakeEntityManagerFactory.`object`!!)

    @Bean
    @Qualifier("snowflakeEntityManagerFactoryBuilder")
    fun snowflakeEntityManagerFactoryBuilder(jpaProperties: JpaProperties): EntityManagerFactoryBuilder =
        EntityManagerFactoryBuilder(HibernateJpaVendorAdapter(), jpaProperties.properties, null)
}
