package no.ruter.kosi.fleet.infrastructure.persistence.snowflake

import jakarta.annotation.PostConstruct
import org.springframework.boot.context.properties.ConfigurationProperties
import java.io.File
import java.nio.file.Files
import java.nio.file.attribute.PosixFilePermissions

@ConfigurationProperties(prefix = "snowflake")
class SnowflakeProperties(
    val enabled: Boolean = false,
    val url: String,
    private val privateKey: String,
    val schema: String,
    val role: String,
) {
    @PostConstruct
    private fun writeKeyToFile() {
        val tempDir = System.getProperty("java.io.tmpdir")
        val keyFile = File(tempDir, "snowflake_rsa_key.p8")
        
        keyFile.writeText(privateKey)
        
        try {
            // posix first
            val permissions = PosixFilePermissions.fromString("rw-------")
            Files.setPosixFilePermissions(keyFile.toPath(), permissions)
        } catch (e: UnsupportedOperationException) {
            // this should work for windows
            keyFile.setReadable(true, true)
            keyFile.setWritable(true, true)
            keyFile.setExecutable(false)
        }
    }
}