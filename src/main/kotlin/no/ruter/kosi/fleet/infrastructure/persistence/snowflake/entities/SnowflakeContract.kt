package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities

import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.vehicle.dto.ContractorDto
import java.io.Serializable

@Entity
@Table(name = "contracts")
data class SnowflakeContract(
    @Id
    val contractId: Int,
    val contractIdInFrida: Int,
    val name: String,
) : Serializable {

    companion object {
        fun from(domain: ContractorDto): SnowflakeContract = SnowflakeContract(
            contractId = domain.id,
            contractIdInFrida = domain.contractIdInFrida,
            name = domain.name
        )
    }
}