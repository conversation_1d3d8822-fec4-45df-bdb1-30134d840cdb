package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities

import com.fasterxml.jackson.annotation.JsonManagedReference
import jakarta.persistence.*
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import java.io.Serializable
import java.time.LocalDateTime

@Entity
@Table(name = "vehicles")
data class SnowflakeVehicle(
    @Id
    val vehicleId: Int,

    @Column(name = "internal_vehicle_id", nullable = false)
    val internalVehicleId: Int,

    val vehicleSisId: String?,
    val chassisNumber: String?,
    val length: Double? = null,
    val totalNumberOfPassengers: Int? = null,
    val numberOfPassengersSeated: Int? = null,
    val wheelchairSpaces: Int? = null,
    @OneToOne(cascade = [CascadeType.ALL])
    @JoinColumn(name = "vehicle_passenger_capacity_id", nullable = true)
    var capacity: SnowflakeVehiclePassengerCapacity? = null,

    @ManyToOne(cascade = [CascadeType.DETACH])
    @JoinColumn(name = "contract_id", nullable = false)
    @JsonManagedReference
    val contract: SnowflakeContract? = null,

    @OneToOne(cascade = [CascadeType.ALL], orphanRemoval = true)
    @JoinColumn(name = "vehicle_quality_id", nullable = true)
    @JsonManagedReference
    var vehicleQuality: SnowflakeVehicleQuality?,

    var isCurrent: Boolean = true,
    var effectiveFrom: LocalDateTime = LocalDateTime.now(),
    var effectiveTo: LocalDateTime? = null,
) : Serializable {
    companion object {
        fun from(domain: VehicleDto): SnowflakeVehicle = SnowflakeVehicle(
            vehicleId = domain.identification.vehicleId,
            internalVehicleId = domain.identification.vehicleId,
            vehicleSisId = domain.identification.vehicleSISId,
            chassisNumber = domain.identification.chassisNumber,
            length = domain.technical.dimensions.lengthInMeters,
            totalNumberOfPassengers = domain.capacity?.total,
            numberOfPassengersSeated = domain.capacity?.seated,
            wheelchairSpaces = domain.features.accessibility.wheelchairPlaces,
            capacity = domain.capacity?.let { SnowflakeVehiclePassengerCapacity.from(it) },
            contract = domain.contractor?.let { SnowflakeContract.from(it) },
            vehicleQuality = null,
            isCurrent = true,
            effectiveFrom = LocalDateTime.now(),
            effectiveTo = null,
        )
    }
}
