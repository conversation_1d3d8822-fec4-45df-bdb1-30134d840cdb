package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.quality.QualityType
import java.io.Serializable
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity as DomainFieldQualityEntity

@Entity
@Table(
    name = "vehicle_field_quality",
    indexes = [
        Index(name = "idx_field_quality_field_name", columnList = "field_name"),
        Index(name = "idx_field_quality_current", columnList = "is_current")
    ]
)
data class SnowflakeVehicleFieldQuality(
    @Id
    val id: Int,

    @Column(name = "field_name", nullable = false)
    val fieldName: String,

    @Column(name = "field_value")
    val fieldValue: String?,

    @Enumerated(EnumType.STRING)
    val qualityType: QualityType,

    val message: String,

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_quality_id")
    var vehicleQuality: SnowflakeVehicleQuality? = null
) : Serializable {
    companion object {
        fun from(domain: DomainFieldQualityEntity): SnowflakeVehicleFieldQuality =
            SnowflakeVehicleFieldQuality(
                id = domain.id,
                fieldName = domain.fieldName,
                fieldValue = domain.fieldValue,
                qualityType = QualityType.valueOf(domain.qualityType),
                message = domain.message!!,
                vehicleQuality = null
            )
    }
}
