package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import no.ruter.kosi.fleet.domain.vehicle.dto.CapacityDto
import java.io.Serializable

@Entity
@Table(name = "vehicle_passenger_capacity")
data class SnowflakeVehiclePassengerCapacity(
    @Id
    val vehiclePassengerCapacityId: Int,
    val standingArea: Double?,
    @Column(name = "predicted_total_passengers_2_per_m2")
    val predictedTotalNumberOfPassengers2PerM2: Int?,
    @Column(name = "predicted_total_passengers_3_per_m2")
    val predictedTotalNumberOfPassengers3PerM2: Int?,
    @Column(name = "predicted_total_passengers_4_per_m2")
    val predictedTotalNumberOfPassengers4PerM2: Int?
) : Serializable {
    companion object {
        fun from(domain: CapacityDto): SnowflakeVehiclePassengerCapacity =
            SnowflakeVehiclePassengerCapacity(
                vehiclePassengerCapacityId = 0,
                standingArea = domain.standingArea,
                predictedTotalNumberOfPassengers2PerM2 = domain.predictedTotal2PerM2,
                predictedTotalNumberOfPassengers3PerM2 = domain.predictedTotal3PerM2,
                predictedTotalNumberOfPassengers4PerM2 = domain.predictedTotal4PerM2,
            )
    }
}