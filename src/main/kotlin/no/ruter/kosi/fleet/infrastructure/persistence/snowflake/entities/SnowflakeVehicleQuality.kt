package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities

import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToMany
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.time.LocalDateTime

@Entity
@Table(
    name = "vehicle_quality",
    indexes = [
        Index(name = "idx_vehicle_quality_vehicle_id", columnList = "vehicle_id"),
        Index(name = "idx_vehicle_quality_current", columnList = "is_current")
    ]
)
data class SnowflakeVehicleQuality(
    @Id
    val id: Int,

    @Column(name = "vehicle_id")
    val vehicleId: Int?,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "vehicle_sis_quality_id", nullable = false)
    var vehicleSisQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "chassis_number_quality_id", nullable = false)
    var chassisNumberQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "length_quality_id", nullable = false)
    var lengthQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "total_number_of_passengers_quality_id", nullable = false)
    var totalNumberOfPassengersQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "number_of_passengers_seated_quality_id", nullable = false)
    var numberOfPassengersSeatedQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "wheelchair_spaces_quality_id", nullable = false)
    var wheelchairSpacesQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "capacity_quality_id", nullable = false)
    var capacityQuality: SnowflakeVehicleFieldQuality,

    @OneToOne(cascade = [CascadeType.ALL], fetch = FetchType.EAGER)
    @JoinColumn(name = "contract_quality_id", nullable = false)
    var contractQuality: SnowflakeVehicleFieldQuality,

    @OneToMany(mappedBy = "vehicleQuality", cascade = [CascadeType.ALL], orphanRemoval = true, fetch = FetchType.LAZY)
    val fieldQualities: MutableList<SnowflakeVehicleFieldQuality> = mutableListOf(),

    @Column(name = "is_current", nullable = false)
    var isCurrent: Boolean = true,
    var effectiveFrom: LocalDateTime = LocalDateTime.now(),
    var effectiveTo: LocalDateTime? = null,

    @Column(updatable = false)
    @CreationTimestamp
    private val createdAt: LocalDateTime? = null,
    @UpdateTimestamp
    private val updatedAt: LocalDateTime? = null,
) : Serializable