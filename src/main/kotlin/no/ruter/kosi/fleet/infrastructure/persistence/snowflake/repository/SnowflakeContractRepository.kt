package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository

import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities.SnowflakeContract
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa.SpringDataSnowflakeContractRepository
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Primary
@Repository
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true", matchIfMissing = true)
@Transactional(transactionManager = "snowflakeTransactionManager")
class SnowflakeContractRepository(
    private val jpaRepository: SpringDataSnowflakeContractRepository
) {

    fun deleteAll() {
        jpaRepository.deleteAll()
    }

    fun delete(contract: SnowflakeContract) {
        jpaRepository.delete(contract)
    }
}
