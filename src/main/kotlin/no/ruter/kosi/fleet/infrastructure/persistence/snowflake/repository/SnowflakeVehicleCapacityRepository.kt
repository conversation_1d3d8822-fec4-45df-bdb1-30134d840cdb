package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository

import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa.SpringDataSnowflakeVehicleCapacityRepository
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Repository

@Repository
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true", matchIfMissing = true)
class SnowflakeVehicleCapacityRepository(
    private val jpaRepository: SpringDataSnowflakeVehicleCapacityRepository
) {

    fun deleteAll() = jpaRepository.deleteAll()
}