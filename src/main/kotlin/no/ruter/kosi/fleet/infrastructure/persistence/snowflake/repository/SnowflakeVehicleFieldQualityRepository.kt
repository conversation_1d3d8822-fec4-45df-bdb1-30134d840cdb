package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository

import no.ruter.kosi.fleet.domain.quality.VehicleFieldQualityEntity
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities.SnowflakeVehicleFieldQuality
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa.SpringDataSnowflakeVehicleFieldQualityRepository
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true")
@Transactional(transactionManager = "snowflakeTransactionManager")
class SnowflakeVehicleFieldQualityRepository(
    private val jpaRepository: SpringDataSnowflakeVehicleFieldQualityRepository
) {

    fun save(vehicleFieldQualityEntity: VehicleFieldQualityEntity): SnowflakeVehicleFieldQuality {
        return jpaRepository.save(SnowflakeVehicleFieldQuality.from(vehicleFieldQualityEntity))
    }
}
