package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository

import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities.SnowflakeVehicleQuality
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa.SpringDataSnowflakeVehicleQualityRepository
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Primary
@Repository
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true")
@Transactional(transactionManager = "snowflakeTransactionManager")
class SnowflakeVehicleQualityRepository(
    private val jpaRepository: SpringDataSnowflakeVehicleQualityRepository
) {

    fun save(vehicleQuality: SnowflakeVehicleQuality): SnowflakeVehicleQuality {
        return jpaRepository.save(vehicleQuality)
    }
}
