package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository

import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities.SnowflakeVehicle
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa.SpringDataSnowflakeVehicleRepository
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Primary
@Repository
@ConditionalOnProperty(name = ["snowflake.enabled"], havingValue = "true", matchIfMissing = true)
@Transactional(transactionManager = "snowflakeTransactionManager")
class SnowflakeVehicleRepository(
    private val jpaRepository: SpringDataSnowflakeVehicleRepository
) {

    fun deleteAll() {
        jpaRepository.deleteAll()
    }

    fun delete(vehicle: SnowflakeVehicle) {
        jpaRepository.delete(vehicle)
    }
}