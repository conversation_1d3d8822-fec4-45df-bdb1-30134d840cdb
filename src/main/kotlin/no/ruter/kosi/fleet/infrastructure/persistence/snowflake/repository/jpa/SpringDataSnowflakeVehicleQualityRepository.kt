package no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.jpa

import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.entities.SnowflakeVehicleQuality
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SpringDataSnowflakeVehicleQualityRepository : JpaRepository<SnowflakeVehicleQuality, Int> {
    fun save(vehicleQuality: SnowflakeVehicleQuality): SnowflakeVehicleQuality
}
