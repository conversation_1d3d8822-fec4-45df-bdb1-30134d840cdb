package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysApiError
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysApiService
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysImporter
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class AutosysImportJob(
    override val jobService: JobService,
    private val autosysImporter: AutosysImporter,
    private val autosysApiService: AutosysApiService,
    private val vehicleRepository: AuroraInternalVehicleRepository,
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        try {
            logger.info("Starting AUTOSYS import")

            val allVehicles = vehicleRepository.findAll()
            logger.info("Processing {} vehicles for AUTOSYS import", allVehicles.size)

            fleetJob?.let {
                jobService.updateJobProgress(it.jobId, 0, allVehicles.size)
            }

            val managedJob = fleetJob ?: createTempFleetJob()

            allVehicles.forEachIndexed { index, vehicle ->
                if (fleetJob != null && jobService.findJob(fleetJob.jobId)?.status == FleetJob.JobStatus.CANCELLED) {
                    logger.info("Job cancelled, aborting AutosysImportJob")
                    throw JobCancelledException()
                }
                MdcUtils.setVehicleId(vehicle.vehicleId)
                MdcUtils.setVehicleRef(vehicle.vehicleRef)

                autosysApiService.getVehicleData(vehicle).fold(
                    { error ->
                        handleAutosysApiError(error, fleetJob?.jobId)
                    },
                    { vehicleData ->
                        try {
                            logger.debug("Processing AUTOSYS data for vehicle {}", vehicle.vehicleId)
                            autosysImporter.processVehicle(vehicleData, vehicle, managedJob)
                            logger.debug("Successfully processed AUTOSYS data for vehicle {}", vehicle.vehicleId)
                        } catch (e: Exception) {
                            logger.error("Error processing AUTOSYS data for vehicle {}: {}", vehicle.vehicleId, e.message, e)
                            fleetJob?.let {
                                jobService.recordJobError(
                                    it.jobId,
                                    "Error processing AUTOSYS data for vehicle ${vehicle.vehicleId}: ${e.message}",
                                )
                            }
                        }
                    },
                )

                fleetJob?.let {
                    jobService.updateJobProgress(it.jobId, index + 1, allVehicles.size)
                }

                MdcUtils.setVehicleId(null)
                MdcUtils.setVehicleRef(null)
            }

            logger.info("AUTOSYS import completed successfully")
        } catch (e: Exception) {
            logger.error("Error during AUTOSYS import process: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Error during AUTOSYS import: ${e.message}")
            }
            throw e
        }
    }

    private fun handleAutosysApiError(
        error: AutosysApiError,
        jobId: Int?,
    ) {
        val errorMessage =
            when (error) {
                is AutosysApiError.NotFound -> {
                    val refInfo = error.vehicleRef?.let { "vehicleRef: $it" } ?: "no reference"
                    "Vehicle not found in AUTOSYS (vehicleId: ${error.vehicleId}, $refInfo)"
                }
                is AutosysApiError.ClientError ->
                    "AUTOSYS API client error (Status code: ${error.statusCode}): ${error.message}"
                is AutosysApiError.NetworkError ->
                    "AUTOSYS API network error: ${error.cause.message}"
                is AutosysApiError.UnexpectedError ->
                    "Unexpected error from AUTOSYS API: ${error.cause.message}"
            }

        logger.error(errorMessage)
        jobId?.let {
            jobService.recordJobError(it, errorMessage)
        }
    }

    private fun createTempFleetJob(): FleetJob =
        FleetJob(
            type = FleetJob.JobType.IMPORT_AUTOSYS,
            manual = true,
            quartzJobId = "temp-autosys-job-${System.currentTimeMillis()}",
            quartzJobGroup = "temp",
        )
}
