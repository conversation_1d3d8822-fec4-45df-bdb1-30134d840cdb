package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import arrow.core.Either
import java.time.Instant

abstract class BaseJob : org.quartz.Job {
    protected val logger = LoggerFactory.getLogger(BaseJob::class.java)

    protected abstract val jobService: JobService

    override fun execute(context: JobExecutionContext) {
        MdcUtils.setJobName(context.jobDetail.key.name)

        val jobId = getJobIdFromContext(context)

        val job = jobId?.let { id ->
            try {
                jobService.findJob(id)
            } catch (e: Exception) {
                logger.warn("Error finding job with ID {}: {}", id, e.message)
                null
            }
        }

        MdcUtils.setJobId(jobId)

        if (context.result == "CANCELLED") {
            logger.info("Job was cancelled before starting work")
            job?.let { jobService.cancelJob(it.jobId) }
            MdcUtils.clearContext()
            return
        }

        job?.let {
            it.markRunning()
            jobService.save(it)
            logger.info("Starting job {} (ID: {})", it.type, it.jobId)
        } ?: logger.info("Starting job without database record")

        val result = Either.catch { executeInternal(context, job) }

        result.fold(
            { e ->
                when (e) {
                    is JobCancelledException -> {
                        logger.info("Job cancelled by JobCancelledException")
                        job?.let {
                            jobService.cancelJob(it.jobId)
                            logger.info("Job {} (ID: {}) cancelled", it.type, it.jobId)
                        }
                    }
                    else -> {
                        logger.error("Error executing job: {}", e.message, e)
                        job?.let {
                            jobService.failJob(it.jobId, e.message ?: "Unknown error")
                            logger.info("Job {} (ID: {}) failed", it.type, it.jobId)
                        }
                    }
                }
            },
            {
                job?.let {
                    if (it.status == FleetJob.JobStatus.RUNNING) {
                        jobService.completeJob(it.jobId)
                        logger.info("Job {} (ID: {}) completed successfully", it.type, it.jobId)
                    }
                }
            }
        )
        MdcUtils.clearContext()
    }

    private fun getJobIdFromContext(context: JobExecutionContext): Int? {
        val jobDataMap = context.mergedJobDataMap

        return if (jobDataMap.containsKey("jobId")) {
            try {
                jobDataMap.getInt("jobId")
            } catch (e: Exception) {
                logger.warn("Invalid jobId in job data map: {}", e.message)
                null
            }
        } else {
            null
        }
    }

    protected fun JobExecutionContext.getInstantOrNull(key: String): Instant? {
        val jobDataMap = this.mergedJobDataMap
        return if (jobDataMap.containsKey(key)) {
            jobDataMap[key] as? Instant
        } else {
            null
        }
    }

    protected abstract fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    )
}
