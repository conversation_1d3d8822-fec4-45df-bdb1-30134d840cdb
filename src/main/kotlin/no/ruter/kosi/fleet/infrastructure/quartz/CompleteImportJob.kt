package no.ruter.kosi.fleet.infrastructure.quartz

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@DisallowConcurrentExecution
@Component
class CompleteImportJob(
    private val jobService: JobService,
    private val jobSchedulerManager: JobSchedulerManager
) : Job {
    private val logger = LoggerFactory.getLogger(CompleteImportJob::class.java)

    sealed class CompleteImportJobError {
        data object JobLookupError : CompleteImportJobError()
        data object JobTransitionError : CompleteImportJobError()
        data object JobSchedulerError : CompleteImportJobError()
        data object JobCancelled : CompleteImportJobError()
        data object Timeout : CompleteImportJobError()
    }

    override fun execute(context: JobExecutionContext) {
        val jobKey = context.jobDetail.key
        val jobDataMap = context.mergedJobDataMap
        val jobId: Int? = if (jobDataMap.containsKey("jobId")) jobDataMap.getInt("jobId") else null
        logger.info("execute called for jobKey: $jobKey, jobId: $jobId")
        MdcUtils.setJobName(jobKey.name)
        MdcUtils.setJobId(jobId)

        if (jobId == null) {
            logger.error("jobId is null or missing from JobDataMap, cannot proceed!")
            logger.error("JobDataMap contents: ${jobDataMap.toMap()}")
            return
        }
        val job = jobService.findJob(jobId)
        if (job == null) {
            logger.error("No job found for jobId: $jobId")
            return
        } else {
            logger.info("Found job: $job")
        }
        logger.info("Starting job orchestration for jobId: $jobId, isCurrent status: ${job.status}")
        if (job.status == FleetJob.JobStatus.CANCELLED) {
            logger.info("CompleteImportJob: parent job {} already CANCELLED", jobId)
            jobService.cancelJob(jobId)
            return
        }
        val errors = mutableListOf<CompleteImportJobError>()
        listOf(
            FleetJob.JobType.IMPORT_FRIDA_CONTRACTS to FridaContractsImportJob::class.java,
            FleetJob.JobType.IMPORT_FRIDA_VEHICLES to FridaVehicleImportJob::class.java,
            FleetJob.JobType.IMPORT_AUTOSYS to AutosysImportJob::class.java,
            FleetJob.JobType.EVENTDATA_VALIDATION to EventDataValidationJob::class.java,
            FleetJob.JobType.VEHICLE_RECONSTRUCTION to ReconstructVehicleJob::class.java,
            FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA to PublishVehiclesToKafkaJob::class.java,
            FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE to PublishVehiclesToSnowflakeJob::class.java,
        ).forEach { (jobType, klass) ->
            MdcUtils.setJobName(jobType.name) // overide with child job name
            val res = scheduleAndWait(job, jobType, klass)
            res.fold(
                { err ->
                    errors += err
                    logger.error("error: {}", err)
                },
                {
                    logger.info("completed")
                }
            )
        }

        MdcUtils.setJobName(jobKey.name)
        if (errors.any()) {
            errors.forEach { logger.error("Job error: {}", it) }
            if (errors.any { it is CompleteImportJobError.JobCancelled }) {
                jobService.cancelJob(jobId)
                logger.info("Parent job $jobId cancelled due to child cancellation")
            } else {
                jobService.failJob(jobId, errors.joinToString())
            }
        } else {
            logger.info("CompleteImportJob executed successfully")
            jobService.completeJob(jobId)
        }
    }

    private fun scheduleAndWait(
        parentJob: FleetJob,
        fleetJobType: FleetJob.JobType,
        jobClass: Class<out Job>
    ): Either<CompleteImportJobError, FleetJob> {
        return try {
            val scheduledJob = jobSchedulerManager.scheduleJob(parentJob, fleetJobType, jobClass)
            val pollIntervalMs = 200L
            var completedJob: FleetJob?
            while (true) {
                completedJob = jobService.findJob(scheduledJob.jobId)
                if (completedJob != null &&
                    (completedJob.status == FleetJob.JobStatus.COMPLETED ||
                            completedJob.status == FleetJob.JobStatus.FAILED ||
                            completedJob.status == FleetJob.JobStatus.CANCELLED)
                ) {
                    break
                }
                Thread.sleep(pollIntervalMs)
            }
            if (completedJob == null) {
                logger.error("scheduleAndWait: Could not find job after scheduling (jobId=${scheduledJob.jobId})")
                return CompleteImportJobError.JobLookupError.left()
            }
            return when (completedJob.status) {
                FleetJob.JobStatus.FAILED -> {
                    logger.error("scheduleAndWait: Child job ${scheduledJob.jobId} FAILED. Last error: ${completedJob.lastError}")
                    if (completedJob.lastError?.contains("JobCancelledException") == true) {
                        CompleteImportJobError.JobCancelled.left()
                    } else CompleteImportJobError.JobTransitionError.left()
                }

                FleetJob.JobStatus.COMPLETED -> completedJob.right()
                else -> {
                    logger.error("scheduleAndWait: Child job ${scheduledJob.jobId} in unexpected status: ${completedJob.status}")
                    CompleteImportJobError.JobTransitionError.left()
                }
            }
        } catch (e: JobCancelledException) {
            CompleteImportJobError.JobCancelled.left()
        } catch (e: Exception) {
            logger.error("scheduleAndWait: Exception while scheduling/waiting: ${e.message}", e)
            CompleteImportJobError.JobSchedulerError.left()
        }
    }
}
