package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.eventdata.EventDataValidationService
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class EventDataValidationJob(
    override val jobService: JobService,
    private val eventDataValidationService: EventDataValidationService
) : BaseJob() {

    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?
    ) {
        logger.info("Validating EventData quality")
        eventDataValidationService.validateAllVehicles()
    }
}