package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiError
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class FridaContractsImportJob(
    override val jobService: JobService,
    // TODO replace with contracts importer
    private val fridaApiService: FridaApiService,
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        try {
            logger.info("Starting FRIDA contracts import")

            fridaApiService.fetchContracts().fold(
                { error ->
                    handleFridaApiError(error, fleetJob)
                    fleetJob?.let {
                        jobService.updateJobProgress(it.jobId, 0, 0)
                    }
                },
                { contracts ->
                    logger.info("Fetched {} contracts from FRIDA", contracts.size)

                    fleetJob?.let {
                        jobService.updateJobProgress(it.jobId, 0, contracts.size)
                    }

                    // TODO replace with contracts importing
//                    fridaVehicleImporter.processVehicles(contracts, fleetJob ?: createTempFleetJob()) { count ->
//                        fleetJob?.let {
//                            jobService.updateJobProgress(it.jobId, count, contracts.size)
//                        }
//                    }

                    fleetJob?.let {
                        jobService.updateJobProgress(it.jobId, contracts.size, contracts.size)
                    }

                    logger.info("FRIDA contract import completed successfully, processed {} contracts", contracts.size)
                },
            )
        } catch (e: Exception) {
            logger.error("Unexpected error during FRIDA contract import: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Unexpected error during FRIDA contract import: ${e.message}")
            }
            throw e
        }
    }

    // TODO simplify this
    private fun handleFridaApiError(
        error: FridaApiError,
        fleetJob: FleetJob?,
    ) {
        val errorMessage =
            when (error) {
                is FridaApiError.ClientError ->
                    "FRIDA API client error (Status code: ${error.statusCode}): ${error.message}"
                is FridaApiError.NetworkError ->
                    "FRIDA API network error: ${error.cause.message}"
                is FridaApiError.ParseError ->
                    "Error parsing FRIDA API response: ${error.cause.message}"
                is FridaApiError.EmptyResponse ->
                    "Empty response from FRIDA API: ${error.message}"
                is FridaApiError.UnexpectedError ->
                    "Unexpected error from FRIDA API: ${error.cause.message}"

                FridaApiError.Cancelled -> "FRIDA API operation was cancelled"
            }

        logger.error(errorMessage)
        fleetJob?.let {
            jobService.recordJobError(it.jobId, errorMessage)
        }
    }
}
