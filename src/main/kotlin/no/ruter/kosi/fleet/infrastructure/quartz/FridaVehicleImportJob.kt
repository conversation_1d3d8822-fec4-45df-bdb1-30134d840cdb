package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiError
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaVehicleImporter
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class FridaVehicleImportJob(
    override val jobService: JobService,
    private val fridaVehicleImporter: FridaVehicleImporter,
    private val fridaApiService: FridaApiService,
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        try {
            logger.info("Starting FRIDA import")

            fleetJob?.let {
                val currentJob = jobService.findJob(it.jobId)
                if (currentJob?.status == FleetJob.JobStatus.CANCELLED) {
                    logger.info("Job was cancelled before starting FRIDA import")
                    return
                }
            }

            val isCancelled = {
                fleetJob?.status == FleetJob.JobStatus.CANCELLED
            }

            val vehiclesResult = fridaApiService.fetchVehicles(isCancelled = isCancelled)

            vehiclesResult.fold(
                { error ->
                    handleFridaApiError(error, fleetJob)
                    fleetJob?.let {
                        jobService.updateJobProgress(it.jobId, 0, 0)
                    }
                },
                { vehicles ->
                    logger.info("Fetched {} vehicles from FRIDA", vehicles.size)

                    fleetJob?.let {
                        jobService.updateJobProgress(it.jobId, 0, vehicles.size)
                    }

                    val job = fleetJob ?: createTempFleetJob()

                    fridaVehicleImporter.processVehicles(
                        fridaVehicles = vehicles, 
                        job = job,
                        onVehicleProcessed = { count ->
                            fleetJob?.let {
                                jobService.updateJobProgress(it.jobId, count, vehicles.size)
                            }
                        },
                        isCancelled = isCancelled
                    )

                    fleetJob?.let {
                        val currentJob = jobService.findJob(it.jobId)
                        if (currentJob?.status != FleetJob.JobStatus.CANCELLED) {
                            jobService.updateJobProgress(it.jobId, vehicles.size, vehicles.size)
                            logger.info("FRIDA import completed successfully, processed {} vehicles", vehicles.size)
                        } else {
                            logger.info("FRIDA import cancelled, partially processed vehicles")
                        }
                    } ?: logger.info("FRIDA import completed successfully, processed {} vehicles", vehicles.size)
                },
            )
        } catch (e: Exception) {
            logger.error("Unexpected error during FRIDA import: {}", e.message, e)
            fleetJob?.let {
                val currentJob = jobService.findJob(it.jobId)
                if (currentJob?.status != FleetJob.JobStatus.CANCELLED) {
                    jobService.recordJobError(it.jobId, "Unexpected error during FRIDA import: ${e.message}")
                } else {
                    logger.info("Job was already cancelled, not recording error")
                }
            }
            throw e
        }
    }

    private fun handleFridaApiError(
        error: FridaApiError,
        fleetJob: FleetJob?,
    ) {
        if (error is FridaApiError.Cancelled) {
            logger.info("FRIDA API operation was cancelled")
            return
        }

        val errorMessage =
            when (error) {
                is FridaApiError.ClientError ->
                    "FRIDA API client error (Status code: ${error.statusCode}): ${error.message}"
                is FridaApiError.NetworkError ->
                    "FRIDA API network error: ${error.cause.message}"
                is FridaApiError.ParseError ->
                    "Error parsing FRIDA API response: ${error.cause.message}"
                is FridaApiError.EmptyResponse ->
                    "Empty response from FRIDA API: ${error.message}"
                is FridaApiError.UnexpectedError ->
                    "Unexpected error from FRIDA API: ${error.cause.message}"
                FridaApiError.Cancelled ->
                    "FRIDA API operation was cancelled" // This should never be reached due to the early return above
            }

        logger.error(errorMessage)
        fleetJob?.let {
            val currentJob = jobService.findJob(it.jobId)
            if (currentJob?.status != FleetJob.JobStatus.CANCELLED) {
                jobService.recordJobError(it.jobId, errorMessage)
            } else {
                logger.info("Job was already cancelled, not recording FRIDA API error")
            }
        }
    }

    private fun createTempFleetJob(): FleetJob =
        FleetJob(
            type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
            manual = true,
            quartzJobId = "temp-frida-job-${System.currentTimeMillis()}",
            quartzJobGroup = "temp",
        )
}
