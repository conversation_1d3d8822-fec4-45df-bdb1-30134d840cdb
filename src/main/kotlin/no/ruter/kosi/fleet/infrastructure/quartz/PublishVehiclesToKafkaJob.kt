package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.vehicle.capacity.VehicleCapacityService
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.vehicle.capacity.VehicleCapacityAvroMappingError
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class PublishVehiclesToKafkaJob(
    private val vehicleService: VehicleService,
    private val vehicleCapacityService: VehicleCapacityService,
    override val jobService: JobService,
) : BaseJob() {

    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        MdcUtils.setJobName("PublishVehiclesToKafkaJob")
        try {
            val allVehicles = vehicleService.getAllVehicles()
            val totalVehicles = allVehicles.size
            fleetJob?.let {
                it.totalItems = totalVehicles
                jobService.save(it)
            }

            vehicleService.publishVehiclesToKafka { count ->
                if (fleetJob != null && jobService.findJob(fleetJob.jobId)?.status == FleetJob.JobStatus.CANCELLED) {
                    logger.info("Job cancelled, aborting PublishVehiclesToKafkaJob")
                    throw JobCancelledException()
                }
                fleetJob?.let {
                    jobService.updateJobProgress(it.jobId, count, totalVehicles)
                }
            }.fold(
                { errors ->
                    fleetJob?.let {
                        for (error in errors) {
                            val errorMessage = when (error) {
                                is VehicleService.VehiclePublishError.FetchError ->
                                    "Error fetching vehicles: ${error.message} - ${error.cause.message}"
                                is VehicleService.VehiclePublishError.RecordBuildError ->
                                    "Error building Avro record for vehicle ID ${error.vehicleId}: ${error.cause.message}"
                                is VehicleService.VehiclePublishError.MissingChassisNumberError ->
                                    "Missing chassis number for vehicle ID ${error.vehicleId}: ${error.message}"
                                is VehicleService.VehiclePublishError.KafkaPublishError ->
                                    "Error publishing to Kafka with key ${error.key}: ${error.cause.message}"
                                is VehicleService.VehiclePublishError.UnexpectedError ->
                                    "Unexpected error: ${error.message} - ${error.cause!!.message}" // should have a cause here, right?
                            }
                            jobService.recordJobError(it.jobId, errorMessage)
                        }
                    }
                    logger.warn("Vehicle publishing completed with {} errors", errors.size)
                },
                { _ ->
                    logger.info("Vehicle publishing completed successfully")
                }
            )

            // publish capacity here as well
            vehicleCapacityService.publishAllCapacitiesToKafka().fold(
                { errors ->
                    fleetJob?.let {
                        for (error in errors) {
                            val errorMessage = when (error) {
                                is VehicleCapacityService.CapacityProcessingError.PreparationError ->
                                    "Error preparing capacity: ${error.message}${error.cause?.message?.let { " - $it" } ?: ""}"
                                is VehicleCapacityService.CapacityProcessingError.PublishingError ->
                                    "Error publishing capacity to Kafka with key ${error.key}: ${error.cause.message}"
                                is VehicleCapacityService.CapacityProcessingError.RecordCreationError -> {
                                    val mappingError = error.mappingError
                                    when (mappingError) {
                                        is VehicleCapacityAvroMappingError.MissingChassisNumberError ->
                                            "Missing chassis number for vehicle ID ${mappingError.vehicleId}, capacity ID ${mappingError.capacityId}: ${mappingError.message}"
                                        is VehicleCapacityAvroMappingError.RecordBuildError ->
                                            "Error building Avro record for vehicle ID ${mappingError.vehicleId}, capacity ID ${mappingError.capacityId}: ${mappingError.message}"
                                    }
                                }
                                is VehicleCapacityService.CapacityProcessingError.UnexpectedError ->
                                    "Unexpected error processing capacity: ${error.message}${error.cause.message?.let { " - $it" } ?: ""}"
                            }
                            jobService.recordJobError(it.jobId, errorMessage)
                        }
                    }
                    logger.warn("Capacity publishing completed with {} errors", errors.size)
                },
                { _ ->
                    logger.info("Capacity publishing completed successfully")
                }
            )
        } catch (e: Exception) {
            logger.error("Error during Kafka vehicle publishing: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Error during Kafka vehicle publishing: ${e.message}")
            }
            throw e
        }
    }
}
