package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.SnowflakePublicationService
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@DisallowConcurrentExecution
class PublishVehiclesToSnowflakeJob(
    private val publicationService: SnowflakePublicationService,
    override val jobService: JobService,
) : BaseJob() {

    @Transactional
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) = publicationService.publishVehiclesToSnowflake(context)
}
