package no.ruter.kosi.fleet.infrastructure.quartz

import org.quartz.spi.JobFactory
import org.quartz.spi.TriggerFiredBundle
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.quartz.SpringBeanJobFactory

@Configuration
class QuartzConfig(
    private val applicationContext: ApplicationContext,
) {
    @Bean
    fun springBeanJobFactory(): JobFactory {
        val jobFactory = AutowiringSpringBeanJobFactory()
        jobFactory.setApplicationContext(applicationContext)
        return jobFactory
    }
}

class AutowiringSpringBeanJobFactory :
    SpringBeanJobFactory(),
    JobFactory {
    private lateinit var applicationContext: ApplicationContext

    override fun setApplicationContext(context: ApplicationContext) {
        this.applicationContext = context
    }

    override fun createJobInstance(bundle: TriggerFiredBundle): Any {
        val jobClass = bundle.jobDetail.jobClass
        // get job from spring context
        return applicationContext.getBean(jobClass)
    }
}
