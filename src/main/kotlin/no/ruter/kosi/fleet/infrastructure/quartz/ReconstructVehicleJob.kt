package no.ruter.kosi.fleet.infrastructure.quartz

import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.vehicle.dto.IdentificationDto
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.infrastructure.cache.CacheService
import no.ruter.kosi.fleet.infrastructure.logging.MdcUtils
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import org.quartz.DisallowConcurrentExecution
import org.quartz.JobExecutionContext
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class ReconstructVehicleJob(
    private val vehicleService: VehicleService,
    private val internalVehicleRepository: AuroraInternalVehicleRepository,
    override val jobService: JobService,
    private val cacheService: CacheService
) : BaseJob() {
    override fun executeInternal(
        context: JobExecutionContext,
        fleetJob: FleetJob?,
    ) {
        val timestamp = context.getInstantOrNull("timestamp")
        MdcUtils.setVehicleRef("all")

        try {
            logger.info(
                "Starting reconstruction of all vehicles{}",
                timestamp?.let { " at timestamp $it" } ?: "",
            )

            cacheService.evictCache("vehiclesList")
            cacheService.evictCache("vehicleDetails")
            cacheService.evictCache("vehicleReconstruction")

            val allVehicles = internalVehicleRepository.findAll()
            logger.info("Found {} vehicles to reconstruct", allVehicles.size)

            val totalCount = allVehicles.size
            fleetJob?.let {
                it.totalItems = totalCount
                jobService.save(it)
            }

            allVehicles.forEachIndexed { index, internalVehicle ->
                if (fleetJob != null && jobService.findJob(fleetJob.jobId)?.status == FleetJob.JobStatus.CANCELLED) {
                    logger.info("Job cancelled, aborting ReconstructVehicleJob")
                    throw JobCancelledException()
                }

                try {
                    MdcUtils.setVehicleRef(internalVehicle.vehicleRef)
                    vehicleService.reconstructAndSave(
                        internalVehicle,
                        timestamp ?: java.time.Instant.now(),
                    )
                } catch (e: Exception) {
                    logger.error("Error reconstructing vehicle ${internalVehicle.vehicleRef}: ${e.message}", e)
                    fleetJob?.let {
                        jobService.recordJobError(it.jobId, "Error reconstructing vehicle ${internalVehicle.vehicleRef}: ${e.message}")
                    }
                }

                val progressCount = index + 1
                fleetJob?.let {
                    jobService.updateJobProgress(it.jobId, progressCount, totalCount)
                }

                if (progressCount % 100 == 0 || progressCount == totalCount) {
                    logger.info("Reconstruction progress: {}/{} vehicles processed", progressCount, totalCount)
                }
            }

            logger.info("Completed reconstruction of all vehicles")
        } catch (e: Exception) {
            logger.error("Error during vehicle reconstruction: {}", e.message, e)
            fleetJob?.let {
                jobService.recordJobError(it.jobId, "Error during vehicle reconstruction: ${e.message}")
            }
            throw e
        }
    }
}
