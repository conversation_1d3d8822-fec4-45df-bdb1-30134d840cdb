package no.ruter.kosi.fleet.infrastructure.web

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.util.UUID

@Component
class MdcWebFilter : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val requestId = request.getHeader("X-Request-ID") ?: UUID.randomUUID().toString()
        val path = request.requestURI
        val method = request.method
        
        try {
            MDC.put("requestId", requestId)
            MDC.put("path", path)
            MDC.put("method", method)
            
            logger.info("Processing $method request to $path with requestId: $requestId")
            
            filterChain.doFilter(request, response)
        } finally {
            MDC.clear()
        }
    }
}
