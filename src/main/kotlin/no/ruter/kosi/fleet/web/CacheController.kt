package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.infrastructure.cache.CacheService
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/cache")
class CacheController(
    private val cacheService: CacheService
) {
    private val logger = LoggerFactory.getLogger(CacheController::class.java)

    @GetMapping
    fun getCacheInfo(): ResponseEntity<Map<String, Any>> {
        logger.info("Retrieving cache information")

        try {
            val cacheInfo = cacheService.getCachesInfo()
            return ResponseEntity.ok(mapOf("caches" to cacheInfo))
        } catch (e: Exception) {
            logger.error("Error retrieving cache information: {}", e.message, e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error retrieving cache information: ${e.message}"
                )
            )
        }
    }

    @DeleteMapping("/{cacheName}")
    fun clearCache(@PathVariable cacheName: String): ResponseEntity<Map<String, Any>> {
        logger.info("Request to clear cache: {}", cacheName)

        try {
            cacheService.evictCache(cacheName)
            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "Cache cleared successfully: $cacheName"
                )
            )
        } catch (e: Exception) {
            logger.error("Error clearing cache {}: {}", cacheName, e.message, e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error clearing cache: ${e.message}"
                )
            )
        }
    }

    @DeleteMapping("/{cacheName}/{key}")
    fun clearCacheEntry(
        @PathVariable cacheName: String,
        @PathVariable key: String
    ): ResponseEntity<Map<String, Any>> {
        logger.info("Request to clear cache entry: {} with key: {}", cacheName, key)

        try {
            cacheService.evictCacheEntry(cacheName, key)
            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "Cache entry cleared successfully: $cacheName/$key"
                )
            )
        } catch (e: Exception) {
            logger.error("Error clearing cache entry {}/{}: {}", cacheName, key, e.message, e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error clearing cache entry: ${e.message}"
                )
            )
        }
    }

    @DeleteMapping
    fun clearAllCaches(): ResponseEntity<Map<String, Any>> {
        logger.info("Request to clear all caches")

        try {
            cacheService.evictAllCaches()
            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "All caches cleared successfully"
                )
            )
        } catch (e: Exception) {
            logger.error("Error clearing all caches: {}", e.message, e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Error clearing all caches: ${e.message}"
                )
            )
        }
    }
}