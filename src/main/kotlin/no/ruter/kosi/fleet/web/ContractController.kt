package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.vehicle.contract.entities.Contract
import no.ruter.kosi.fleet.domain.vehicle.contract.ContractService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/contracts")
class ContractController(
    private val contractService: ContractService
) {

    @GetMapping
    fun getContracts(): ResponseEntity<List<Contract>> {
        val contracts = contractService.getContractors()
        return ResponseEntity.ok(contracts)
    }
}