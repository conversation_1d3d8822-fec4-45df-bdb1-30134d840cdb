package no.ruter.kosi.fleet.web

import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/eventdata")
class EventDataController(
    private val eventDataRepository: AuroraEventDataRepository
) {
    private val logger = LoggerFactory.getLogger(EventDataController::class.java)

    @GetMapping("/{id}")
    fun getEventDataById(@PathVariable id: Int): ResponseEntity<Any> {
        logger.info("Fetching event data with id: {}", id)
        val eventData = eventDataRepository.findByEventDataId(id)
        return if (eventData != null) {
            ResponseEntity.ok(eventData)
        } else {
            ResponseEntity.notFound().build()
        }
    }

    @Cacheable(cacheNames = ["eventDataList"],
        key = "T(java.lang.String).format('%s_%s_%s_%s_%s_%s_%s_%s_%s_%s', " +
       "#vehicleId ?: 'null', " +
       "#vehicleRef ?: 'null', " +
       "#fieldName ?: 'null', " +
       "#fieldValue ?: 'null', " +
       "#startDate ?: 'null', " +
       "#endDate ?: 'null', " +
       "#isCurrent ?: 'null', " +
       "#pageable.pageNumber, " +
       "#pageable.pageSize, " +
       "#pageable.sort.toString())")
    @GetMapping
    fun getEventData(
        @RequestParam(required = false) vehicleId: Int?,
        @RequestParam(required = false) vehicleRef: String?,
        @RequestParam(required = false) fieldName: String?,
        @RequestParam(required = false) fieldValue: String?,
        @RequestParam(required = false) startDate: String?,
        @RequestParam(required = false) endDate: String?,
        @RequestParam(required = false) isCurrent: Boolean?,
        pageable: Pageable
    ): Page<EventData> {
        logger.info("Fetching event data with filters: vehicleId={}, vehicleRef={}, fieldName={}, fieldValue={}, startDate={}, endDate={}, isCurrent={}, sort={}",
                  vehicleId, vehicleRef, fieldName, fieldValue, startDate, endDate, isCurrent, pageable.sort)
        
        return eventDataRepository.findWithFilters(
            vehicleId,
            vehicleRef,
            fieldName,
            fieldValue,
            startDate,
            endDate,
            isCurrent,
            pageable
        )
    }
}