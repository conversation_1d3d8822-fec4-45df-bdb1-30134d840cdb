package no.ruter.kosi.fleet.web

import arrow.core.Either
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.quartz.PublishVehiclesToKafkaJob
import no.ruter.kosi.fleet.infrastructure.quartz.PublishVehiclesToSnowflakeJob
import no.ruter.kosi.fleet.infrastructure.quartz.ReconstructVehicleJob
import org.quartz.JobBuilder
import org.quartz.JobDataMap
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.server.ResponseStatusException
import java.time.Instant
import java.util.UUID

@RestController
@RequestMapping("/vehicles")
class VehicleController(
    private val scheduler: Scheduler,
    private val vehicleService: VehicleService,
    private val internalVehicleRepository: AuroraInternalVehicleRepository,
    private val jobService: JobService,
) {
    private val logger = LoggerFactory.getLogger(VehicleController::class.java)

    @GetMapping("/{vehicleRef}")
    @Cacheable(value = ["vehicleDetails"], key = "#vehicleRef + '_' + (#timestamp != null ? #timestamp.toString() : 'latest')")
    fun getVehicle(
        @PathVariable vehicleRef: String,
        @RequestParam(required = false) timestamp: Instant?,
    ): VehicleDto {
        logger.info("Cache miss for vehicle {}, fetching fresh data", vehicleRef)
        return if (timestamp == null) {
            vehicleService.getLatestVehicle(vehicleRef)
                ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "Vehicle with reference $vehicleRef not found")
        } else {
            when (val result = vehicleService.getReconstructedVehicleAtTime(vehicleRef, timestamp)) {
                is Either.Left -> when (result.value) {
                    VehicleService.VehicleError.NotFound ->
                        throw ResponseStatusException(HttpStatus.NOT_FOUND, "Vehicle with reference $vehicleRef not found at the specified time")
                    is VehicleService.VehicleError.Unexpected ->
                        throw ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Unexpected error: ${(result.value as VehicleService.VehicleError.Unexpected).message}")
                }
                is Either.Right -> result.value.first
            }
        }
    }

    @GetMapping("")
    @Cacheable(value = ["vehiclesList"], key = "#timestamp != null ? #timestamp.toString() : 'latest'")
    fun getAllVehicles(
        @RequestParam(required = false) timestamp: Instant?,
    ): List<VehicleDto> {
        logger.info("Cache miss for vehicles list, fetching fresh data {}",
        if (timestamp != null) "for timestamp $timestamp" else "")
        return if (timestamp == null) {
            vehicleService.getAllVehicles()
        } else {
            vehicleService.getAllVehicles()
            // TODO add logic to get vehicles at a specific time
        }
    }

    @PostMapping("/reconstructall")
    @CacheEvict(value = ["vehiclesList", "vehicleDetails", "vehicleReconstruction"], allEntries = true)
    fun reconstructAllVehicles(
        @RequestParam(required = false) timestamp: Instant?,
        @RequestParam(required = false, defaultValue = "true") createJob: Boolean,
    ): ResponseEntity<Map<String, Any>> {
        logger.info("Received request to reconstruct all vehicles" + (timestamp?.let { " at timestamp $it" } ?: ""))
        logger.info("Evicting all vehicle caches due to reconstruction")

        val fleetJob: FleetJob? =
            if (createJob) {
                jobService.createJob(
                    type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                    manual = true,
                    quartzJobId = "reconstructAll-${UUID.randomUUID()}",
                    quartzJobGroup = "vehicles",
                )
            } else {
                null
            }

        val jobDataMap =
            JobDataMap().apply {
                timestamp?.let { put("timestamp", it) }
                put("vehicleRef", "all") // not sure about this, but for now, we will override it anyway
                fleetJob?.let { put("jobId", it.jobId) }
            }

        val jobKey = "reconstructAllVehicles-${UUID.randomUUID()}"
        val triggerKey = "reconstructAllVehiclesTrigger-${UUID.randomUUID()}"

        val jobDetail =
            JobBuilder
                .newJob(ReconstructVehicleJob::class.java)
                .withIdentity(JobKey.jobKey(jobKey, "vehicles"))
                .usingJobData(jobDataMap)
                .build()

        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity(triggerKey, "vehicles")
                .startNow()
                .build()

        scheduler.scheduleJob(jobDetail, trigger)
        return ResponseEntity.accepted().body(
            mapOf(
                "message" to "Vehicle reconstruction job scheduled in background.",
                "timestamp" to (timestamp ?: "isCurrent time"),
                "jobKey" to jobKey,
            ),
        )
    }

    @PostMapping("/{vehicleRef}/reconstruct")
    @CacheEvict(value = ["vehicleDetails", "vehicleReconstruction"], key = "#vehicleRef + '_latest'")
    fun reconstructVehicle(
        @PathVariable vehicleRef: String,
        @RequestParam(required = false) timestamp: Instant?,
        @RequestParam(required = false, defaultValue = "false") createJob: Boolean,
    ): ResponseEntity<Map<String, Any>> {
        logger.info("Scheduling reconstruction for vehicle $vehicleRef" + (timestamp?.let { " at timestamp $it" } ?: ""))
        logger.info("Evicting cache entry for vehicle $vehicleRef")

        val vehicle =
            internalVehicleRepository.findByVehicleRef(vehicleRef)
                ?: return ResponseEntity.notFound().build()

        val fleetJob: FleetJob? =
            if (createJob) {
                jobService.createJob(
                    type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                    manual = true,
                    quartzJobId = "reconstruct-${vehicle.vehicleId}-${UUID.randomUUID()}",
                    quartzJobGroup = "vehicles",
                )
            } else {
                null
            }

        val jobDataMap =
            JobDataMap().apply {
                put("vehicleId", vehicle.vehicleId)
                put("vehicleRef", vehicleRef)
                timestamp?.let { put("timestamp", it) }
                fleetJob?.let { put("jobId", it.jobId) }
            }

        val jobKey = "reconstructVehicle-${vehicle.vehicleId}-${UUID.randomUUID()}"
        val triggerKey = "reconstructVehicleTrigger-${vehicle.vehicleId}-${UUID.randomUUID()}"

        val jobDetail =
            JobBuilder
                .newJob(ReconstructVehicleJob::class.java)
                .withIdentity(JobKey.jobKey(jobKey, "vehicles"))
                .usingJobData(jobDataMap)
                .build()

        val trigger =
            TriggerBuilder
                .newTrigger()
                .withIdentity(triggerKey, "vehicles")
                .startNow()
                .build()

        scheduler.scheduleJob(jobDetail, trigger)

        return ResponseEntity.accepted().body(
            mapOf(
                "success" to true,
                "message" to "Vehicle reconstruction scheduled in background for $vehicleRef",
                "vehicleId" to vehicle.vehicleId,
                "jobKey" to jobKey,
            ),
        )
    }

    @PostMapping("/publish")
    fun publishVehiclesToKafka(): ResponseEntity<Map<String, Any>> {
        logger.info("Scheduling publication of all vehicles to Kafka and Snowflake")
        val fleetJob = jobService.createJob(
            type = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
            manual = true,
            quartzJobId = "publishKafka-${UUID.randomUUID()}",
            quartzJobGroup = "vehicles",
        )
        val kafkaJobData = JobDataMap().apply { put("jobId", fleetJob.jobId) }
        val (kafkaJobKey, kafkaJobId) = schedulePublishingJob(
            FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
            PublishVehiclesToKafkaJob::class.java,
            fleetJob,
            kafkaJobData
        )
        val snowflakeJobData = JobDataMap().apply { put("jobId", fleetJob.jobId) }
        val (snowflakeJobKey, snowflakeJobId) = schedulePublishingJob(
            FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE,
            PublishVehiclesToSnowflakeJob::class.java,
            fleetJob,
            snowflakeJobData
        )
        val responseData = mutableMapOf<String, Any>(
            "success" to true,
            "message" to "Vehicle publication scheduled",
            "jobKey" to kafkaJobKey,
            "jobId" to kafkaJobId,
            "snowflakeJobKey" to snowflakeJobKey,
            "snowflakeJobId" to snowflakeJobId
        )
        return ResponseEntity.accepted().body(responseData)
    }

    @PostMapping("/{vehicleRef}/publish")
    fun publishVehicleToKafka(
        @PathVariable vehicleRef: String
    ): ResponseEntity<Map<String, Any>> {
        logger.info("Scheduling publication of vehicle $vehicleRef to Kafka and Snowflake")
        val vehicle =
            internalVehicleRepository.findByVehicleRef(vehicleRef)
                ?: return ResponseEntity.notFound().build()
        val fleetJob = jobService.createJob(
            type = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
            manual = true,
            quartzJobId = "publish-${vehicle.vehicleId}-${UUID.randomUUID()}",
            quartzJobGroup = "vehicles",
        )
        val kafkaJobData = JobDataMap().apply {
            put("vehicleId", vehicle.vehicleId)
            put("vehicleRef", vehicleRef)
            put("includeReconstruction", true)
            put("jobId", fleetJob.jobId)
        }
        val (kafkaJobKey, kafkaJobId) = schedulePublishingJob(
            FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
            PublishVehiclesToKafkaJob::class.java,
            fleetJob,
            kafkaJobData
        )
        val snowflakeJobData = JobDataMap().apply {
            put("vehicleId", vehicle.vehicleId)
            put("vehicleRef", vehicleRef)
            put("includeReconstruction", true)
            put("jobId", fleetJob.jobId)
        }
        val (snowflakeJobKey, snowflakeJobId) = schedulePublishingJob(
            FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE,
            PublishVehiclesToSnowflakeJob::class.java,
            fleetJob,
            snowflakeJobData
        )
        val responseData = mutableMapOf<String, Any>(
            "success" to true,
            "message" to "Vehicle publication to Kafka and Snowflake scheduled in background for $vehicleRef",
            "jobKey" to kafkaJobKey,
            "jobId" to kafkaJobId,
            "snowflakeJobKey" to snowflakeJobKey,
            "snowflakeJobId" to snowflakeJobId
        )
        return ResponseEntity.accepted().body(responseData)
    }

    @PostMapping("/reconstruct-and-publish")
    @CacheEvict(value = ["vehiclesList", "vehicleDetails"], allEntries = true)
    fun reconstructAndPublishAllVehicles(
        @RequestParam(required = false, defaultValue = "true") createJob: Boolean,
    ): ResponseEntity<Map<String, Any>> {
        logger.info("Scheduling reconstruction and publication of all vehicles")
        logger.info("Evicting all vehicle caches due to reconstruction")

        val parentJob: FleetJob? = if (createJob) {
            jobService.createJob(
                type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                manual = true,
                quartzJobId = "reconstruct-and-publish-${UUID.randomUUID()}",
                quartzJobGroup = "vehicles",
            )
        } else {
            null
        }

        val jobDataMap = JobDataMap().apply {
            parentJob?.let { put("jobId", it.jobId) }
        }

        val reconstructJobKey = "reconstructAllVehicles-${UUID.randomUUID()}"
        val reconstructTriggerKey = "reconstructAllVehiclesTrigger-${UUID.randomUUID()}"
        val reconstructJobDetail = JobBuilder
            .newJob(ReconstructVehicleJob::class.java)
            .withIdentity(JobKey.jobKey(reconstructJobKey, "vehicles"))
            .usingJobData(jobDataMap)
            .build()
        val reconstructTrigger = TriggerBuilder
            .newTrigger()
            .withIdentity(reconstructTriggerKey, "vehicles")
            .startNow()
            .build()
        scheduler.scheduleJob(reconstructJobDetail, reconstructTrigger)

        val kafkaJob = jobService.createChildJob(
            parentFleetJob = parentJob ?: return ResponseEntity.internalServerError().body(mapOf("success" to false, "message" to "Failed to create parent job")),
            type = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
            quartzJobId = "publishKafka-${UUID.randomUUID()}",
            quartzJobGroup = "vehicles"
        )
        val kafkaJobDataMap = JobDataMap().apply { put("jobId", kafkaJob.jobId) }
        val kafkaJobKey = "publishVehiclesToKafka-${UUID.randomUUID()}"
        val kafkaTriggerKey = "publishVehiclesToKafkaTrigger-${UUID.randomUUID()}"
        val kafkaJobDetail = JobBuilder
            .newJob(PublishVehiclesToKafkaJob::class.java)
            .withIdentity(JobKey.jobKey(kafkaJobKey, "vehicles"))
            .usingJobData(kafkaJobDataMap)
            .build()
        val kafkaTrigger = TriggerBuilder
            .newTrigger()
            .withIdentity(kafkaTriggerKey, "vehicles")
            .startNow()
            .build()
        scheduler.scheduleJob(kafkaJobDetail, kafkaTrigger)

        val snowflakeJob = jobService.createChildJob(
            parentFleetJob = parentJob,
            type = FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE,
            quartzJobId = "publishSnowflake-${UUID.randomUUID()}",
            quartzJobGroup = "vehicles"
        )
        val snowflakeJobDataMap = JobDataMap().apply { put("jobId", snowflakeJob.jobId) }
        val snowflakeJobKey = "publishVehiclesToSnowflake-${UUID.randomUUID()}"
        val snowflakeTriggerKey = "publishVehiclesToSnowflakeTrigger-${UUID.randomUUID()}"
        val snowflakeJobDetail = JobBuilder
            .newJob(PublishVehiclesToSnowflakeJob::class.java)
            .withIdentity(JobKey.jobKey(snowflakeJobKey, "vehicles"))
            .usingJobData(snowflakeJobDataMap)
            .build()
        val snowflakeTrigger = TriggerBuilder
            .newTrigger()
            .withIdentity(snowflakeTriggerKey, "vehicles")
            .startNow()
            .build()
        scheduler.scheduleJob(snowflakeJobDetail, snowflakeTrigger)

        val responseData = mutableMapOf<String, Any>(
            "success" to true,
            "message" to "Vehicle reconstruction and publication scheduled in background",
            "parentJobId" to (parentJob?.jobId ?: "n/a"),
            "reconstructJobKey" to reconstructJobKey,
            "kafkaJobKey" to kafkaJobKey,
            "snowflakeJobKey" to snowflakeJobKey
        )
        parentJob?.let { responseData["jobId"] = it.jobId }
        return ResponseEntity.accepted().body(responseData)
    }

    @GetMapping("/by-contract/{contractId}")
    fun getVehiclesByContract(@PathVariable contractId: Int): List<VehicleDto> {
        return vehicleService.getVehiclesByContract(contractId)
    }

    private fun schedulePublishingJob(
        jobType: FleetJob.JobType,
        jobClass: Class<out org.quartz.Job>,
        parentFleetJob: FleetJob,
        jobData: JobDataMap,
        groupSuffix: String = "vehicles"
    ): Pair<String, Int> {
        val job = jobService.createChildJob(
            parentFleetJob = parentFleetJob,
            type = jobType,
            quartzJobId = "${jobType.name.lowercase()}-${UUID.randomUUID()}",
            quartzJobGroup = groupSuffix
        )
        jobData.put("jobId", job.jobId)
        val jobKey = "${jobType.name.lowercase()}-${UUID.randomUUID()}"
        val triggerKey = "${jobType.name.lowercase()}Trigger-${UUID.randomUUID()}"
        val jobDetail = JobBuilder.newJob(jobClass)
            .withIdentity(JobKey.jobKey(jobKey, groupSuffix))
            .usingJobData(jobData)
            .build()
        val trigger = TriggerBuilder.newTrigger()
            .withIdentity(triggerKey, groupSuffix)
            .startNow()
            .build()
        scheduler.scheduleJob(jobDetail, trigger)
        return jobKey to job.jobId
    }
}