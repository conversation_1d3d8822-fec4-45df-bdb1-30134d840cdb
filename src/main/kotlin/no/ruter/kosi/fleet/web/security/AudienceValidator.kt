package no.ruter.kosi.fleet.web.security

import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.core.OAuth2Error
import org.springframework.security.oauth2.core.OAuth2TokenValidator
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult
import org.springframework.security.oauth2.jwt.Jwt

class AudienceValidator(
    private val audience: String,
) : OAuth2TokenValidator<Jwt> {
    private val logger = LoggerFactory.getLogger(AudienceValidator::class.java)

    override fun validate(jwt: Jwt): OAuth2TokenValidatorResult {
        val error = OAuth2Error("invalid_token", "The required audience is missing", null)

        val jwtAudiences = jwt.audience
        logger.debug("Validating JWT audience: {} against required: {}", jwtAudiences, audience)

        return if (jwtAudiences.contains(audience)) {
            logger.debug("Audience validation successful")
            OAuth2TokenValidatorResult.success()
        } else {
            logger.warn("Audience validation failed. JWT has audiences {} but required {}", jwtAudiences, audience)
            OAuth2TokenValidatorResult.failure(error)
        }
    }
}
