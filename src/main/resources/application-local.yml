spring:
  config:
    activate:
      on-profile: local
  datasource:
    aurora:
      url: ********************************************
      driver-class-name: org.postgresql.Driver
      username: fleet_user
      password: fleet_password
      hikari:
        maximum-pool-size: 10
        connection-timeout: 30000
        minimum-idle: 2
    snowflake:
      url: ************************************************
      driver-class-name: org.postgresql.Driver
      username: fleet_user
      password: fleet_password
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        format_sql: true
  flyway:
    enabled: true
    locations: classpath:db/migration/aurora
    baseline-on-migrate: true
    baseline-version: "0"

scheduler:
  import:
    enabled: false

snowflake:
  enabled: false

logging:
  level:
    no.ruter.kosi.fleet: DEBUG
    org.springframework.jdbc: DEBUG
    org.flywaydb: DEBUG
