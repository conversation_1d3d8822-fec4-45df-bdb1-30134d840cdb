scheduler:
  import:
    cron: "0 0 1 * * ?"
    enabled: true
spring:
  kafka:
    publishing:
      enabled: true
    topics:
      vehicles: fleet.vehicle-snapshots
      entityVehicleCapacityNpraDetails: fleet.entity.vehicle.capacity
    qvcCleanupPolicy: "compact,delete"
    qvcRetentionMs: 691_200_000_000 # 800 days retention (8 * 24 * 60 * 60 * 1000 ms)
    qvcSegmentMs: 3_600_000 # 1 hour segment size
    properties:
      schema.registry.url: ${SCHEMA_REGISTRY_URL}
      specific.avro.reader: true
    configuration:
      "[bootstrap.servers]": ${kafka.bootstrapServers}
      "[schema.registry.url]": ${kafka.schemaRegistryUrl}
      "[application.id]": ${info.app.name}
      "[replication.factor]": ${kafka.replicationFactor}
      "[default.key.serde]": "org.apache.kafka.common.serialization.Serdes$StringSerde"
      "[default.value.serde]": "io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde"
      "[auto.offset.reset]": earliest
      "[topology.optimization]": all
      "[commit.interval.ms]": 1000
      "[statestore.cache.max.bytes]": 0
  jpa.properties.hibernate:
    physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    order_updates: true
    order_inserts: true
    batch_versioned_data: true
    jdbc:
      batch_size: 50
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://dev-kosi-tet.eu.auth0.com/
  management:
    endpoints:
      web:
        exposure:
          include: metrics,health,info,loggers
    endpoint:
      metrics:
        enabled: true
      loggers:
        enabled: true
      health:
        enabled: true
      info:
        enabled: true
  flyway:
    enabled: false
    locations: classpath:db/migration/aurora
  application:
    name: Fleet
  main:
    allow-bean-definition-overriding: true
  aop:
    proxy-target-class: false
auth0:
  audience: http://fleet.ruter.no/
snowflake:
  enabled: false
  url: ${SNOWFLAKE_URL}
  private-key: ${SECRET_SNOWFLAKE_PRIVATE_KEY}
  schema: FLEET
  role: KOSI_LOADER
frida:
  api:
    client-id: ${SECRET_FRIDA_CLIENT_ID}
    client-secret: ${SECRET_FRIDA_CLIENT_SECRET}
    username: ${SECRET_FRIDA_CLIENT_USERNAME}
    password: ${SECRET_FRIDA_CLIENT_PASSWORD}
    token-url: https://ruter-auth.frida.nordicport.se/connect/token
    base-url: https://ruter.frida.nordicport.se
    scope: fridaApi
autosys:
  api:
    base-url: https://akfell-datautlevering.atlas.vegvesen.no/
    token: ${SECRET_KJORETOYDATA_CLIENT_TOKEN}