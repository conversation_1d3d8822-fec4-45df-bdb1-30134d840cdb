CREATE TABLE IF NOT EXISTS contracts
(
    contract_id          BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    contract_id_in_frida BIGINT NOT NULL,
    name                 VARCHAR(255),
    CONSTRAINT pk_contracts PRIMARY KEY (contract_id)
);

CREATE TABLE IF NOT EXISTS contract_events
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    vehicle_id    INT NOT NULL,
    contract_id   BIGINT NOT NULL,
    timestamp     TIMESTAMP,
    CONSTRAINT pk_contract_event PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS event_data
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    event_id      INT    NOT NULL,
    field_name    VARCHAR(255),
    field_value   TEXT,
    timestamp     TIMESTAMP,

    business_key   TEXT,
    effective_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_to   TIMESTAMP NULL,
    is_current     BOOLEAN DEFAULT TRUE,
    CONSTRAINT pk_event_data PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS event_data_quality_issues
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    event_data_id INT NOT NULL,
    field_name    VARCHAR(255),
    actual_value   TEXT,
    expected_value TEXT,
    type          TEXT,
    timestamp     TIMESTAMP,

    effective_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    effective_to TIMESTAMP NULL,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    business_key VARCHAR(255) NOT NULL,

    CONSTRAINT pk_event_data_quality_issues PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS events
(
    event_id   BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    vehicle_id INT NOT NULL,
    job_id  INT NOT NULL,
    timestamp  TIMESTAMP,
    source     VARCHAR(255),
    priority   INT NOT NULL,
    CONSTRAINT pk_events PRIMARY KEY (event_id)
);

CREATE TABLE IF NOT EXISTS jobs
(
    job_id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    manual BOOLEAN NOT NULL,
    quartz_job_id VARCHAR(255),
    quartz_job_group VARCHAR(255),
    parent_job_id INT REFERENCES jobs(job_id),
    total_items INT NOT NULL DEFAULT 0,
    processed_items INT NOT NULL DEFAULT 0,
    phase VARCHAR(255),
    stream_group VARCHAR(50),
    import_date DATE,
    error_count INT NOT NULL DEFAULT 0,
    last_error TEXT,
    parameters TEXT NOT NULL DEFAULT '{}',
    created_at TIMESTAMP NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS quarantined_fields
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    event_id       INT NOT NULL,
    field_name     VARCHAR(255) NOT NULL,
    reason         TEXT,
    timestamp      TIMESTAMP,

    business_key        TEXT,
    effective_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_to   TIMESTAMP NULL,
    is_current     BOOLEAN DEFAULT TRUE,

    CONSTRAINT pk_quarantined_fields PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS internal_vehicles
(
    vehicle_id       INT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    data_source      VARCHAR(255),
    source_id        INT NOT NULL,
    public_id_string VARCHAR(255),
    vehicle_ref      VARCHAR(255),

    created_at                          TIMESTAMP,
    updated_at                          TIMESTAMP,
    CONSTRAINT pk_internal_vehicles     PRIMARY KEY (vehicle_id)
);

CREATE TABLE IF NOT EXISTS vehicles (
    id                  INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    internal_vehicle_id INT NOT NULL,

    vehicle_ref         TEXT NOT NULL,

    identification      TEXT,
    registration        TEXT,
    features            TEXT,
    capacity            TEXT,
    contractor          TEXT,
    technical           TEXT,

    vehicle_quality_id  INT,

    business_key        TEXT,
    effective_from      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_to        TIMESTAMP NULL,
    is_current          BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS vehicle_quality
(
    id           BIGINT    GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    vehicle_id   INT       NOT NULL
    REFERENCES vehicles(id),

    business_key        TEXT,
    effective_from    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_to      TIMESTAMP NULL,
    is_current        BOOLEAN   DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS vehicle_field_quality
(
    id                  BIGINT    GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    vehicle_quality_id  BIGINT    NOT NULL
    REFERENCES vehicle_quality(id),

    -- json path to the field (features.safety.alcoLock)
    field_name          TEXT      NOT NULL,
    field_value         TEXT,

    quality_type        TEXT,
    message             TEXT,

    created_at                          TIMESTAMP,
    updated_at                          TIMESTAMP
);

ALTER TABLE contract_events DROP CONSTRAINT IF EXISTS FK_CONTRACT_EVENT_ON_CONTRACT;

ALTER TABLE contract_events DROP CONSTRAINT IF EXISTS FK_CONTRACT_EVENT_ON_VEHICLE;
ALTER TABLE contract_events
    ADD CONSTRAINT FK_CONTRACT_EVENT_ON_VEHICLE FOREIGN KEY (vehicle_id) REFERENCES vehicles (id);

ALTER TABLE events DROP CONSTRAINT IF EXISTS FK_EVENTS_ON_VEHICLE;
ALTER TABLE events
    ADD CONSTRAINT FK_EVENTS_ON_VEHICLE FOREIGN KEY (vehicle_id) REFERENCES internal_vehicles (vehicle_id);

ALTER TABLE events DROP CONSTRAINT IF EXISTS FK_EVENTS_ON_IMPORT;
ALTER TABLE events
    ADD CONSTRAINT FK_EVENTS_ON_IMPORT FOREIGN KEY (job_id) REFERENCES jobs (job_id);

ALTER TABLE event_data DROP CONSTRAINT IF EXISTS FK_EVENT_DATA_ON_EVENT;
ALTER TABLE event_data
    ADD CONSTRAINT FK_EVENT_DATA_ON_EVENT FOREIGN KEY (event_id) REFERENCES events (event_id);

ALTER TABLE quarantined_fields DROP CONSTRAINT IF EXISTS FK_QUARANTINED_FIELDS_ON_EVENT;
ALTER TABLE quarantined_fields
    ADD CONSTRAINT FK_QUARANTINED_FIELDS_ON_EVENT FOREIGN KEY (event_id) REFERENCES events (event_id);

ALTER TABLE event_data_quality_issues DROP CONSTRAINT IF EXISTS FK_EVENT_DATA_QUALITY_ISSUE_ON_EVENT_DATA;
ALTER TABLE event_data_quality_issues
    ADD CONSTRAINT FK_EVENT_DATA_QUALITY_ISSUE_ON_EVENT_DATA FOREIGN KEY (event_data_id) REFERENCES event_data (id);

-- INDEXES

-- Primary SCD2 performance index: business_key + is_current
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_business_key_current
    ON event_data (business_key, is_current)
    WHERE is_current = true;

-- Partial index for current records only (much smaller, faster)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_current_only
    ON event_data (business_key)
    WHERE is_current = true;

-- Composite index for SCD2 temporal queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_business_key_time_range
    ON event_data (business_key, effective_from, effective_to);

-- Index for vehicle-specific queries (used in change detection)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_vehicle_id_current
    ON event_data (event_id, field_name, is_current)
    WHERE is_current = true;

-- =====================================================
-- VEHICLES TABLE INDEXES
-- =====================================================

-- SCD2 performance index for vehicles
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_business_key_current
    ON vehicles (business_key, is_current)
    WHERE is_current = true;

-- Partial index for current vehicles only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_current_only
    ON vehicles (business_key)
    WHERE is_current = true;

-- Index for vehicle reference lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_vehicle_ref_current
    ON vehicles (vehicle_ref, is_current)
    WHERE is_current = true;

-- =====================================================
-- VEHICLE_QUALITY TABLE INDEXES
-- =====================================================

-- SCD2 performance index for vehicle quality
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_quality_business_key_current
    ON vehicle_quality (business_key, is_current)
    WHERE is_current = true;

-- Partial index for current vehicle quality records
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_quality_current_only
    ON vehicle_quality (business_key)
    WHERE is_current = true;

-- Index for vehicle-specific quality lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_quality_vehicle_id_current
    ON vehicle_quality (vehicle_id, is_current)
    WHERE is_current = true;

-- =====================================================
-- EVENT_DATA_QUALITY_ISSUES TABLE INDEXES
-- =====================================================

-- SCD2 performance index for quality issues
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_quality_issues_business_key_current
    ON event_data_quality_issues (business_key, is_current)
    WHERE is_current = true;

-- Partial index for current quality issues
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_quality_issues_current_only
    ON event_data_quality_issues (business_key)
    WHERE is_current = true;

-- =====================================================
-- QUARANTINED_FIELDS TABLE INDEXES
-- =====================================================

-- SCD2 performance index for quarantined fields
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quarantined_fields_business_key_current
    ON quarantined_fields (business_key, is_current)
    WHERE is_current = true;

-- Partial index for current quarantined fields
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quarantined_fields_current_only
    ON quarantined_fields (business_key)
    WHERE is_current = true;

-- =====================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- =====================================================

-- Index for event lookups by vehicle (used in joins)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_vehicle_id
    ON events (vehicle_id);

-- Index for event data lookups by event (used in joins)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_event_data_event_id
    ON event_data (event_id);

-- Index for internal vehicle lookups by source
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_internal_vehicles_source_id
    ON internal_vehicles (source_id, data_source);
-- Index for internal vehicle lookups by ref and id
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_vehicle_ref ON internal_vehicles(vehicle_ref);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_public_id_string ON internal_vehicles(public_id_string);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicle_source_id ON internal_vehicles(source_id);

-- jobs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_stream_group ON jobs(stream_group);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_import_date ON jobs(import_date);

-- =====================================================
-- ANALYZE TABLES FOR QUERY PLANNER
-- =====================================================
-- Update table statistics for optimal query planning
ANALYZE event_data;
ANALYZE vehicles;
ANALYZE vehicle_quality;
ANALYZE event_data_quality_issues;
ANALYZE quarantined_fields;
ANALYZE events;
ANALYZE internal_vehicles;
