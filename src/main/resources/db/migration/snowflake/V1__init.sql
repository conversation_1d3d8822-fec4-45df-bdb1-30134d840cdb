CREATE TABLE IF NOT EXISTS CONTRACTS (
    contract_id BIGINT PRIMARY KEY,
    contract_id_in_frida BIGINT NOT NULL,
    name VA<PERSON>HA<PERSON>(255),
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ
);

CREATE TABLE IF NOT EXISTS INTERNAL_VEHICLES (
    vehicle_id INT PRIMARY KEY,
    data_source VARCHAR(255),
    source_id INT NOT NULL,
    public_id_string VARCHAR(255),
    vehicle_ref VARCHAR(255),
    created_at TIMESTAMP_NTZ,
    updated_at TIMESTAMP_NTZ
);

CREATE TABLE IF NOT EXISTS VEHICLES (
    vehicle_id INT PRIMARY KEY,
    internal_vehicle_id INT NOT NULL,
    vehicle_ref TEXT NOT NULL,
    
    identification VARIANT,
    registration VARIANT,
    features VARIANT,
    capacity VARIANT,
    contractor VARIANT,
    technical VARIANT,
    
    effective_from TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    effective_to TIMESTAMP_NTZ,
    is_current BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ
);

CREATE TABLE IF NOT EXISTS VEHICLE_QUALITY (
    id BIGINT PRIMARY KEY,
    vehicle_id INT NOT NULL,
    effective_from TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    effective_to TIMESTAMP_NTZ,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_at TIMESTAMP_NTZ
);

CREATE TABLE IF NOT EXISTS VEHICLE_FIELD_QUALITY (
    id BIGINT PRIMARY KEY,
    vehicle_quality_id BIGINT NOT NULL,
    field_name TEXT NOT NULL,
    field_value TEXT,
    quality_type TEXT,
    message TEXT,
    effective_from TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    effective_to TIMESTAMP_NTZ,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Events tables
CREATE TABLE IF NOT EXISTS EVENTS (
    event_id BIGINT PRIMARY KEY,
    vehicle_id INT NOT NULL,
    job_id INT NOT NULL,
    timestamp TIMESTAMP_NTZ,
    source VARCHAR(255),
    priority INT NOT NULL,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

CREATE TABLE IF NOT EXISTS EVENT_DATA (
    event_data_id BIGINT PRIMARY KEY,
    event_id INT NOT NULL,
    field_name VARCHAR(255),
    field_value TEXT,
    timestamp TIMESTAMP_NTZ,
    effective_from TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    effective_to TIMESTAMP_NTZ,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

CREATE TABLE IF NOT EXISTS JOBS (
    job_id INT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    manual BOOLEAN NOT NULL,
    quartz_job_id VARCHAR(255),
    quartz_job_group VARCHAR(255),
    parent_job_id INT,
    total_items INT NOT NULL DEFAULT 0,
    processed_items INT NOT NULL DEFAULT 0,
    phase VARCHAR(255),
    stream_group VARCHAR(50),
    import_date DATE,
    error_count INT NOT NULL DEFAULT 0,
    last_error TEXT,
    parameters VARIANT DEFAULT PARSE_JSON('{}'),
    created_at TIMESTAMP_NTZ NOT NULL,
    started_at TIMESTAMP_NTZ,
    completed_at TIMESTAMP_NTZ
);

CREATE TABLE IF NOT EXISTS QUARANTINED_FIELDS (
    id BIGINT PRIMARY KEY,
    event_id INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    reason TEXT,
    timestamp TIMESTAMP_NTZ,
    effective_from TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    effective_to TIMESTAMP_NTZ,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

ALTER TABLE VEHICLES ADD CONSTRAINT FK_VEHICLES_INTERNAL_VEHICLE
    FOREIGN KEY (internal_vehicle_id) REFERENCES INTERNAL_VEHICLES(vehicle_id);

ALTER TABLE VEHICLE_QUALITY ADD CONSTRAINT FK_VEHICLE_QUALITY_VEHICLE 
    FOREIGN KEY (vehicle_id) REFERENCES VEHICLES(vehicle_id);

ALTER TABLE VEHICLE_FIELD_QUALITY ADD CONSTRAINT FK_FIELD_QUALITY_VEHICLE_QUALITY 
    FOREIGN KEY (vehicle_quality_id) REFERENCES VEHICLE_QUALITY(id);

ALTER TABLE EVENTS ADD CONSTRAINT FK_EVENTS_VEHICLE 
    FOREIGN KEY (vehicle_id) REFERENCES INTERNAL_VEHICLES(vehicle_id);

ALTER TABLE EVENT_DATA ADD CONSTRAINT FK_EVENT_DATA_EVENT 
    FOREIGN KEY (event_id) REFERENCES EVENTS(event_id);

ALTER TABLE QUARANTINED_FIELDS ADD CONSTRAINT FK_QUARANTINED_FIELDS_EVENT 
    FOREIGN KEY (event_id) REFERENCES EVENTS(event_id);

CREATE INDEX IF NOT EXISTS IDX_VEHICLES_INTERNAL_ID ON VEHICLES(internal_vehicle_id);
CREATE INDEX IF NOT EXISTS IDX_VEHICLES_REF ON VEHICLES(vehicle_ref);
CREATE INDEX IF NOT EXISTS IDX_VEHICLES_CURRENT ON VEHICLES(is_current) WHERE is_current = TRUE;
CREATE INDEX IF NOT EXISTS IDX_VEHICLE_QUALITY_VEHICLE ON VEHICLE_QUALITY(vehicle_id);
CREATE INDEX IF NOT EXISTS IDX_VEHICLE_QUALITY_CURRENT ON VEHICLE_QUALITY(is_current) WHERE is_current = TRUE;
CREATE INDEX IF NOT EXISTS IDX_EVENTS_VEHICLE ON EVENTS(vehicle_id);
CREATE INDEX IF NOT EXISTS IDX_EVENTS_TIMESTAMP ON EVENTS(timestamp);
CREATE INDEX IF NOT EXISTS IDX_EVENT_DATA_EVENT ON EVENT_DATA(event_id);
CREATE INDEX IF NOT EXISTS IDX_EVENT_DATA_CURRENT ON EVENT_DATA(is_current) WHERE is_current = TRUE;
