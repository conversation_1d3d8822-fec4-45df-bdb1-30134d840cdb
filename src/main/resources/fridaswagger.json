{"x-generator": "NSwag v14.0.3.0 (NJsonSchema v11.0.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Frida API", "version": "v1.4"}, "servers": [{"url": "https://ruter.frida.nordicport.se"}], "paths": {"/api/vehicle/vehicles": {"get": {"tags": ["vehicle"], "operationId": "VehiclesV_GetAllVehicles", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicles/{id}": {"get": {"tags": ["vehicle"], "summary": "Hämtar fordon med angivet id.", "description": "Hämtar ett fordon med matchande id.", "operationId": "VehiclesV_GetVehicleById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Fordons id.", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "date", "in": "query", "description": "Datum", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 2}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 3}], "responses": {"200": {"description": "Det efterfrågade fordonet.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiVehicleV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicles/planned": {"get": {"tags": ["vehicle"], "operationId": "VehiclesV_GetAllPlannedVehicles", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/expectedcontracts": {"get": {"tags": ["vehicle"], "summary": "Hämtar alla förväntade avtal som är tillgängliga för användaren.", "description": "Hämtar en lista av alla förväntade avtal som är tillgängliga för användaren.", "operationId": "ExpectedContractsV_GetAllVehicleExpectedContracts", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "En lista av alla förväntade avtal som är tillgängliga för användaren.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleExpectedContractV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicles/{vehicleId}/expectedcontracts": {"get": {"tags": ["vehicle"], "summary": "Hämtar alla förväntade avtal som är tillgängliga för fordonet.", "description": "Hämtar en lista av alla förväntade avtal som är tillgängliga för fordonet.", "operationId": "ExpectedContractsV_GetVehicleExpectedContractsForVehicle", "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "En lista av alla förväntade avtal som är tillgängliga för fordonet.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleExpectedContractV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/incidents": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar en lista över alla registrerade trafikhändelser tillgängliga för användaren", "operationId": "TrafficIncidentReportingIncidents_GetAllIncidents", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiTrafficIncidentReportingIncidentModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}, "put": {"tags": ["trafficincidentreporting"], "summary": "Uppdaterar en trafikhändelse", "operationId": "TrafficIncidentReportingIncidents_PutIncident", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "requestBody": {"x-name": "model", "description": "Trafikhändelsen som skall uppdateras", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}, "post": {"tags": ["trafficincidentreporting"], "operationId": "TrafficIncidentReportingIncidents_PostIncident", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/incidents/updates": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar en lista över alla trafikhändelser som är uppdaterade (tillagda/ändrade) efter det angivna datumet", "operationId": "TrafficIncidentReportingIncidents_GetAllUpdatedIncidents", "parameters": [{"name": "date", "in": "query", "description": "Datum och klockslag från vilket alla uppdaterade (tillagda/ändrade) trafikhändelser skall hämtas. Om inget anges antas dagens datum (från midnatt).", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiTrafficIncidentReportingIncidentModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/incidents/{id}": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar trafikhändelse med angivet id.", "operationId": "TrafficIncidentReportingIncidents_GetIncidentById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "trafikhändelsens id", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/contractroutes": {"get": {"tags": ["contract"], "summary": "Hämtar alla linjer som är tillgängliga för användaren", "operationId": "ContractRoutes_GetAllContractRoutes", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av alla linjer som är tillgängliga för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiContractRouteModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/contracts/{contractid}/routes": {"get": {"tags": ["contract"], "summary": "Hämtar alla tillgängliga linjer på ett givet avtal", "operationId": "ContractRoutes_GetRoutesForContract", "parameters": [{"name": "contractid", "in": "path", "required": true, "description": "Identitet på det eftersökta avtalet", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiContractRouteModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/contracts": {"get": {"tags": ["contract"], "summary": "Hämtar alla avtal som är tillgängliga för användaren", "operationId": "ContractsV_GetAllContracts", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "En lista av alla avtal som är tillgängliga för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiContractV3Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/environmentalrequirementappendices": {"get": {"tags": ["contract"], "summary": "Hämtar alla miljökravsbilagor som är tillgängliga för användaren", "operationId": "EnvironmentalRequirementAppendix_GetAllContracts", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av alla miljökravsbilagor som är tillgängliga för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEnvironmentalRequirementAppendixModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/environmentalrequirementexhaust": {"get": {"tags": ["contract"], "summary": "Hämtar alla avgaskrav som är tillgängliga för användaren ett givet datum", "operationId": "EnvironmentalRequirementExhausts_GetAllEnvironmentalExhaustRequirements", "parameters": [{"name": "date", "in": "query", "description": "Eftersökt datum (just nu om inget anges)", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "En lista av alla avgaskrav som är tillgängliga för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEnvironmentalRequirementExhaustModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/environmentalrequirementfuels": {"get": {"tags": ["contract"], "summary": "Hämtar alla miljökrav rörande bränslen som är tillgängliga för användaren", "operationId": "EnvironmentalRequirementFuels_GetAllEnvironmentalRequirementFuels", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEnvironmentalRequirementFuelModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/environmentalrequirementreductionofclimatechanginggases": {"get": {"tags": ["contract"], "summary": "Hämtar alla Miljökrav för minskning av klimatpåverkandegaser som är tillgängliga för användaren", "operationId": "EnvironmentalRequirementReductionOfClimateChangingGases_GetAllEnvironmentalRequirementReductionOfClimateChangingGases", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEnvironmentalRequirementReductionOfClimateChangingGasesModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/vehicleplans": {"get": {"tags": ["vehicleplan"], "summary": "Hämtar alla fordonsplaner", "operationId": "VehiclePlan_GetAllVehiclePlans", "parameters": [{"name": "filter", "in": "query", "description": "Filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehiclePlanModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/contract/vehicleplans/{id}": {"get": {"tags": ["vehicleplan"], "summary": "Hämtar fordonsplanen för kontrakt med angivet id", "operationId": "VehiclePlan_GetVehiclePlanByContractId", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Kontraktets id", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiVehiclePlanModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/environmentalaccounting/answerreviews": {"get": {"tags": ["environmentalaccounting"], "summary": "Hämtar en lista över alla aktiva avtal och deras status för miljöredovisning", "operationId": "AnswerReview_GetAllAnswerReviews", "parameters": [{"name": "date", "in": "query", "description": "Datum för bedömning av status", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEnvironmentalAccountingAnswerModel2"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/system/contractors": {"get": {"tags": ["system"], "summary": "Hämtar alla trafikföretag som är tillgängliga för användaren", "operationId": "Contractors_GetAllContractors", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av alla trafikföretag som är tillgängliga för användaren.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiContractorModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/system/contractors/{id}": {"get": {"tags": ["system"], "summary": "Hämtar trafikföretag med angivet id", "operationId": "Contractors_GetContractorById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Id på trafikföretaget", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "Det efterfrågade trafikföretaget", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiContractorModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/system/units": {"get": {"tags": ["system"], "summary": "Hämtar alla enheter som är inlagda i Frida.", "operationId": "Units_GetAllUnits", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av enheter.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiUnitModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/system/units/{id}": {"get": {"tags": ["system"], "summary": "Hämtar enhet med angivet id", "operationId": "Units_GetUnitById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Id på trafikföretaget", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "Efterfrågad enhet", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiUnitModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/categories": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar en lista över alla trafikhändelsekategorier tillgängliga för användaren", "operationId": "TrafficIncidentReportingIncidentCategory_GetAllIncidentCategories", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiTrafficIncidentReportingIncidentCategoryModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/categories/{id}": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar trafikhändelsekategori med givet id", "operationId": "TrafficIncidentReportingIncidentCategory_GetIncidentCategoryById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trafikhändelsekategori id", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "Trafikhändelsekategori med givet id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentCategoryModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/trafficincidentreporting/municipalities": {"get": {"tags": ["trafficincidentreporting"], "summary": "Hämtar en lista av alla kommuner som är tillgängliga för användaren.", "operationId": "TrafficIncidentReportingMunicipalities_GetAllMunicipalities", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiTrafficIncidentReportingMunicipalitiesModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicles/{vehicleId}/customattributevalues": {"get": {"tags": ["vehicle"], "summary": "Hämtar värdena på alla egendefinerade fordonsattribut för ett givet fordon.", "operationId": "CustomAttributeValues_GetCustomAttributeValuesByVehicle", "parameters": [{"name": "vehicleId", "in": "path", "required": true, "description": "Id på det eftersökta fordonet.", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiCustomAttributeValueModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/enginetypes": {"get": {"tags": ["vehicle"], "summary": "Hämtar alla motortekniker tillgängliga för användaren.", "operationId": "EngineTypes_GetAllEngineTypes", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiEngineTypeModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/enginetypes/{id}": {"get": {"tags": ["vehicle"], "summary": "Hämtar motorteknik med matchande id.", "operationId": "EngineTypes_GetEngineTypeById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Id på den eftersökta motortekniken.", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiEngineTypeModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/periodicvehicleinspectionstatus": {"get": {"tags": ["periodicvehicleinspection"], "summary": "Hämtar alla tillgängliga besiktningsdata", "operationId": "PeriodicVehicleInspectionStatus_GetAllPeriodicVehicleInspectionStatuses", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av all besiktningsdata som är tillgänglig för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiPeriodicVehicleInspectionStatusModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehiclereferencepoints": {"get": {"tags": ["vehicle"], "summary": "Hämtar en lista över alla trafikeringsperioder.", "description": "Hämtar endast trafikeringsperioder tillgängliga för den inloggade användaren.", "operationId": "VehicleReferencePoints_GetAllVehicleReferencePoints", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista över alla trafikeringsperioder.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleReferencePointModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehiclereferencepoints/{id}": {"get": {"tags": ["vehicle"], "summary": "Hämtar trafikeringsperiod med angivet id.", "operationId": "VehicleReferencePoints_GetVehicleReferencePointById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trafikeringsperiodens id", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiVehicleReferencePointModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicles/{vehicleId}/referencepoints": {"get": {"tags": ["vehicle"], "summary": "Hämtar en lista över trafikeringsperioder för ett givet fordon.", "description": "Hämtar endast trafikeringsperioder tillgängliga för den inloggade användaren.", "operationId": "VehicleReferencePoints_GetVehicleReferencePointsByVehicleId", "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "Lista med trafikeringsperioder för fordonet", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleReferencePointModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicletypes": {"get": {"tags": ["vehicle"], "summary": "Gets a list of all available vehicle types", "operationId": "VehicleTypesV_GetAllVehicleTypes", "parameters": [{"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleTypeV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicle/vehicletypes/{id}": {"get": {"tags": ["vehicle"], "summary": "Hämtar trafikslag med matchande id.", "operationId": "VehicleTypesV_GetVehicleTypeById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Id på det eftersökta trafikslaget.", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiVehicleTypeV2Model"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicleinspection/vehicleinspectionprotocolregularcontrols": {"get": {"tags": ["vehicleinspection"], "operationId": "VehicleInspectionProtocolRegularControls_GetAllVehicleInspectionProtocolRegularControls", "parameters": [{"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleInspectionProtocolRegularControlModel2"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicleinspection/vehicleinspectionprotocolregularcontrols/updates": {"get": {"tags": ["vehicleinspection"], "operationId": "VehicleInspectionProtocolRegularControls_GetAllVehicleInspectionProtocolRegularControlsUpdates", "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time", "nullable": true}, "x-position": 1}, {"name": "filter", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}, {"name": "pagesize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 4}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 5}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleInspectionProtocolRegularControlModel2"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicleinspection/vehicleinspections": {"get": {"tags": ["vehicleinspection"], "summary": "Hämtar alla fordonskontroller som är tillgängliga för användaren", "operationId": "VehicleInspections_GetAllVehicleInspections", "parameters": [{"name": "filter", "in": "query", "description": "filter", "schema": {"type": "string", "nullable": true}, "x-position": 1}, {"name": "page", "in": "query", "description": "Anger vilken 'sida' av delmängden som skall hämtas", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pagesize", "in": "query", "description": "Anger hur många objekt som skall returneras per begäran", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 3}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 4}], "responses": {"200": {"description": "En lista av alla fordonskontroller som är tillgängliga för användaren", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiListResponseModelOfApiVehicleInspectionModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}, "/api/vehicleinspection/vehicleinspections/{id}": {"get": {"tags": ["vehicleinspection"], "summary": "Hämtar fordonskontroll med givet id.", "operationId": "VehicleInspections_GetVehicleInspectionById", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Fordonskontroll id", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}, {"name": "api-version", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "Fordonskontroll med givet id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiVehicleInspectionModel"}}}}}, "security": [{"oauth2pwd": []}, {"oauth2client": []}]}}}, "components": {"schemas": {"ApiListResponseModelOfApiVehicleV2Model": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleV2Model"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehicleV2Model": {"type": "object", "description": "Resurs som representerar ett fordon (>=1.4)", "additionalProperties": false, "required": ["Id"], "properties": {"Id": {"type": "integer", "description": "Fordons id.", "format": "int32"}, "PublicIDString": {"type": "string", "description": "Registreringsnummer", "nullable": true}, "TemporaryPublicIDString": {"type": "string", "description": "Temporärt registreringsnummer", "nullable": true}, "PublicIdNumber": {"type": "integer", "description": "Fordonets id nummer, om det används av denna typen av fordon", "format": "int32", "nullable": true}, "ChassiNumber": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "FabricCode": {"type": "string", "description": "Fordonstillverkare", "nullable": true}, "EngineTypeId": {"type": "integer", "description": "Motortypens id", "format": "int32"}, "VehicleTypeId": {"type": "integer", "description": "Trafikslagets id", "format": "int32"}, "AC": {"type": "boolean", "nullable": true}, "AccessabilityRamp": {"type": "boolean", "description": "<PERSON><PERSON>", "nullable": true}, "AirbagPassenger": {"type": "boolean", "description": "Airbag passagerare fram", "nullable": true}, "AlcoLock": {"type": "string", "nullable": true}, "Armrest": {"type": "boolean", "description": "Armstöd", "nullable": true}, "Backrest": {"type": "boolean", "description": "Lutningsbara r<PERSON>ggstöd", "nullable": true}, "BackupCameras": {"type": "boolean", "description": "Backkamera", "nullable": true}, "BikeRack": {"type": "boolean", "description": "Cykelhållare", "nullable": true}, "BoosterSeat": {"type": "boolean", "description": "Bälteskudde", "nullable": true}, "CameraSurveillanceSafety": {"type": "boolean", "description": "Videoinspelning av händelser i passagerarutrymmet​", "nullable": true}, "CameraSurveillanceTrafficSafety": {"type": "string", "description": "Chaufförens övervakning av fordonet​", "nullable": true}, "CertificationUnitId": {"type": "integer", "format": "int32", "nullable": true}, "CertificationValueHC": {"type": "number", "format": "double", "nullable": true}, "CertificationValueNOx": {"type": "number", "format": "double", "nullable": true}, "CertificationValuePM": {"type": "number", "format": "double", "nullable": true}, "ChassisManufacturer": {"type": "string", "description": "Chassitillverkare", "nullable": true}, "ChildSeats": {"type": "boolean", "description": "Barnsäten", "nullable": true}, "Comment": {"type": "string", "description": "Kommentar", "nullable": true}, "ConsumptionMixedDriving": {"type": "number", "description": "Förbrukning blandad körning", "format": "double", "nullable": true}, "CurbWeight": {"type": "integer", "description": "Tjänstevikt (faktisk vikt)", "format": "int32", "nullable": true}, "Defibrillators": {"type": "boolean", "description": "Hjärtstartare", "nullable": true}, "Depot": {"type": "string", "description": "Depåtillhörighet", "nullable": true}, "DestinationSign": {"type": "string", "description": "Destinationsskylt (anger skyltens placering för utvändig information​)", "nullable": true}, "Doors": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> (anger dörrantal/kombination​)", "nullable": true}, "EmissionUnitId": {"type": "integer", "format": "int32", "nullable": true}, "EmissionValueHC": {"type": "number", "format": "double", "nullable": true}, "EmissionValueNOx": {"type": "number", "format": "double", "nullable": true}, "EmissionValuePM": {"type": "number", "format": "double", "nullable": true}, "EnergyUse": {"type": "integer", "description": "Energianvändning", "format": "int32", "nullable": true}, "EnvironmentalClass": {"type": "string", "description": "Miljöklass", "nullable": true}, "EnvironmentalVehicleTaxExempt": {"type": "boolean", "description": "Uppfyller Vägtrafikskattelagens krav för milj<PERSON>bil", "nullable": true}, "EnvironmentalZone": {"type": "string", "description": "För trafikslag Buss beräknas enligt fastslaget regelverk (se...) om och hur länge (till vilket år) fordonet får föras i miljözon​", "nullable": true}, "EnvironmentVehicle": {"type": "boolean", "description": "Definition av miljöfordon i Miljökravsbilaga som används vid upphandling.​", "nullable": true}, "EVN": {"type": "string", "description": "EVN-nummer", "nullable": true}, "ExternalDestinationCall": {"type": "boolean", "description": "Automatiskt yttre utrop om destination​", "nullable": true}, "ExtraBreakControlDate": {"type": "string", "description": "<PERSON>e datum för senaste extra bromskontroll, dvs kontroll som görs utöver ordinarie kontrollbesiktning. B<PERSON>r vara ca 6 månader efter ordinarie besiktning.​", "format": "date-time", "nullable": true}, "FireExtinguisher": {"type": "boolean", "description": "Brandsläckare", "nullable": true}, "FireExtinguisherInEngineCompartment": {"type": "boolean", "description": "Brandsläckning i motorrum", "nullable": true}, "FuelConsumptionMixedNEDC1": {"type": "number", "format": "double", "nullable": true}, "FuelConsumptionMixedNEDC2": {"type": "number", "format": "double", "nullable": true}, "FuelConsumptionMixedWLTP1": {"type": "number", "format": "double", "nullable": true}, "FuelConsumptionMixedWLTP2": {"type": "number", "format": "double", "nullable": true}, "HearingLoop": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "Heater": {"type": "string", "description": "Värma<PERSON>", "nullable": true}, "HorizontalTransport": {"type": "boolean", "description": "Liggande transport", "nullable": true}, "HybridVehicle": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "InternalInformationSign": {"type": "boolean", "description": "information om nästa hållplats​", "nullable": true}, "InternalNumber": {"type": "integer", "description": "Inventarienummer el motsv hos trafikföretaget.​", "format": "int32", "nullable": true}, "ISA": {"type": "boolean", "description": "ISA (Intelligen Speed Adaptment)", "nullable": true}, "Kneeling": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "Kombi": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "LatestInspection": {"type": "string", "description": "Kontrollbesiktning senaste", "format": "date-time", "nullable": true}, "Lift": {"type": "boolean", "description": "Lift", "nullable": true}, "LimitedNumberOfSeats": {"type": "integer", "description": "Begränsat antal sittplatser", "format": "int32", "nullable": true}, "Littera": {"type": "string", "description": "Fordonstyp (tåg/spårvagn)", "nullable": true}, "LowEntry": {"type": "boolean", "description": "Lågentré", "nullable": true}, "LowFloor": {"type": "boolean", "description": "Låggolv: Minst 35% av utrymmet för ståplatspassagerare utgör ett område utan trappsteg och ger möjlighet att nå minst en på- och avstigningsdörr.​", "nullable": true}, "LuggageCompartment": {"type": "boolean", "description": "Bagageförvaring", "nullable": true}, "MobileNumber": {"type": "string", "description": "Mobilnummer", "nullable": true}, "ModelYear": {"type": "integer", "description": "<PERSON><PERSON><PERSON>dell", "format": "int32", "nullable": true}, "MonthOfProduction": {"type": "string", "description": "Tillverkningsmånad", "format": "date-time", "nullable": true}, "NeckSupport": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "NoiceLevel": {"type": "integer", "description": "Bullernivå", "format": "int32", "nullable": true}, "NoiseLevelEngine1": {"type": "integer", "description": "Ljudnivå vid körning", "format": "int32", "nullable": true}, "NumberOfPassengersSeated": {"type": "integer", "description": "<PERSON>tal sittande <PERSON>, max (exkl rullstol)​", "format": "int32", "nullable": true}, "NumberOfSteps": {"type": "integer", "description": "<PERSON><PERSON> trap<PERSON><PERSON>g", "format": "int32", "nullable": true}, "PassengerCounter": {"type": "boolean", "description": "Automatisk trafikanträkning​", "nullable": true}, "PlanetManagementSystem": {"type": "string", "description": "Planet-ledningssystem", "nullable": true}, "PlanetTrafficPermit": {"type": "integer", "description": "Planet-trafiktillståndsnummer", "format": "int32", "nullable": true}, "PrimaryEngineCategory": {"type": "string", "nullable": true}, "Radio": {"type": "boolean", "description": "Kommunikationsutrustning", "nullable": true}, "RadioNumber": {"type": "integer", "description": "Radionummer", "format": "int32", "nullable": true}, "Ramp": {"type": "boolean", "description": "ramp/lift för o<PERSON>ng​", "nullable": true}, "ReadingLightsForPassengers": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "nullable": true}, "RealtimeCameraDoorOpenings": {"type": "boolean", "description": "Realtidskamera dörröppningar", "nullable": true}, "RegistrationDate": {"type": "string", "description": "Första registreringsdatum hos Transportstyrelsen​", "format": "date-time", "nullable": true}, "RetrofitPurificationEquipment": {"type": "string", "description": "Eftermonterad reningsutrustning (ange tilläggsutrustning till motorn​)", "nullable": true}, "SatisfiesCleanLightWeightCarFirstReferencePeriod": {"type": "boolean", "description": "Uppfyller lagkravet 2011:846 på ”Ren lätt bil” – första referensperioden", "nullable": true}, "SatisfiesCleanLightWeightCarSecondReferencePeriod": {"type": "boolean", "description": "Uppfyller lagkravet 2011:846 på ”Ren lätt bil” – andra referensper<PERSON>den", "nullable": true}, "SeatBeltReminder": {"type": "boolean", "description": "Audiell bilbältespåminnare", "nullable": true}, "SeatBelts": {"type": "string", "description": "Bälten", "nullable": true}, "SecondaryEngineCategory": {"type": "string", "nullable": true}, "SORT": {"type": "number", "description": "Tillverkarens värde för fordonets energianvändning. Anges som liter diesel/100 km eller kWh/10 km​", "format": "double", "nullable": true}, "SpecialVehicle": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "StairClimber": {"type": "boolean", "description": "Trappklättrare", "nullable": true}, "StopAnnouncements": {"type": "boolean", "description": "Automatiskt hållplatsutrop om nästa hållplats​", "nullable": true}, "StrollerPlaces": {"type": "string", "description": "<PERSON><PERSON>​", "nullable": true}, "TicketMachine": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "Toilet": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "TotalNumberOfPassengers": {"type": "integer", "description": "<PERSON>tal <PERSON>, max (exkl rullstol)​", "format": "int32", "nullable": true}, "TotalWeight": {"type": "number", "description": "Totalvikt enl registreringsbevis (kg)​", "format": "double", "nullable": true}, "TradeName": {"type": "string", "description": "Handelsbeteckning", "nullable": true}, "TrafficEnd": {"type": "string", "description": "<PERSON>tt fordon hör till (är i trafik på) ett visst avtalsområde mellan trafikstart och trafikslut. Det kan finnas många sådana trafikperioder under ett fordons livstid men ett fordon kan inte och får inte något givet datum vara i trafik samtidigt på flera avtalsområden​", "format": "date-time", "nullable": true}, "TrafficPermitNumber": {"type": "string", "description": "Trafiktillståndsnummer", "nullable": true}, "TrafficStart": {"type": "string", "description": "<PERSON>tt fordon kommer genom sin koppling till avtalsområde alltid direkt eller indirekt ha en ägare under sina trafikeringsperioder. I ägarbegreppet ingår också behörighet och vem som får se viss data om fordonet.​", "format": "date-time", "nullable": true}, "TransportWheelChair": {"type": "boolean", "description": "Transportrullstol", "nullable": true}, "TypeOfFuel1": {"type": "string", "nullable": true}, "TypeOfFuel2": {"type": "string", "nullable": true}, "VehicleClass": {"type": "string", "description": "Fordonsklass", "nullable": true}, "VehicleClassSpecialVehicle2006": {"type": "string", "description": "Fordonsklass specialfordon 2006", "nullable": true}, "VehicleClassSpecialVehicle2014": {"type": "string", "description": "Fordonsklass specialfordon 2006", "nullable": true}, "VehicleClassSpecialVehicle2022": {"type": "string", "description": "Fordonsklass specialfordon 2022", "nullable": true}, "VehicleComputer": {"type": "boolean", "description": "Fordons<PERSON><PERSON>", "nullable": true}, "VehicleHeight": {"type": "number", "description": "Fordonshöjd", "format": "double", "nullable": true}, "VehicleLength": {"type": "number", "description": "Fordonslängd", "format": "double", "nullable": true}, "VehicleManufacturer": {"type": "string", "description": "Fordonstillverkare", "nullable": true}, "VehicleReversingAlarm": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON>", "nullable": true}, "VehicleSize": {"type": "string", "description": "anger typbeteckning​", "nullable": true}, "VehicleStatus": {"type": "string", "description": "Fordonet skall alltid ha en status.\nOrdinarie och reserv är enligt avtal.\nBeredskapsfordon ingår ej i avtalade fordon. Fordonet används vid extraordinära omständigheter så som när det behövs ersättningsbussar vid snökaos etc.​", "nullable": true}, "VehicleWidth": {"type": "number", "description": "Fordonsbredd", "format": "double", "nullable": true}, "VisualSafetyBeltReminder": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON> bältespåminnare", "nullable": true}, "WheelChairAndWalkerPlaces": {"type": "integer", "description": "Antal rull<PERSON>ls-/rollatorplatser", "format": "int32", "nullable": true}, "WheelChairPlaces": {"type": "string", "description": "<PERSON><PERSON>", "nullable": true}, "WiFi": {"type": "boolean", "description": "WiFi", "nullable": true}, "Contractor": {"description": "<PERSON>tt fordon kommer genom sin koppling till avtalsområde alltid direkt eller indirekt ha en ägare under sina trafikeringsperioder. I ägarbegreppet ingår också behörighet och vem som får se viss data om fordonet.​", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiVehicleContractorModel"}]}, "CustomAttributeValues": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiCustomAttributeValueModel"}}}}, "ApiVehicleContractorModel": {"type": "object", "additionalProperties": false, "required": ["Id"], "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}}}, "ApiCustomAttributeValueModel": {"type": "object", "additionalProperties": false, "properties": {"VehicleAttributeName": {"type": "string", "nullable": true}, "TypeOfValue": {"type": "string", "nullable": true}, "FromDate": {"type": "string", "format": "date-time", "nullable": true}, "ToDate": {"type": "string", "format": "date-time", "nullable": true}, "UnitId": {"type": "integer", "format": "int32", "nullable": true}, "ValueGivenDate": {"type": "string", "format": "date-time", "nullable": true}, "ValueGivenBy": {"type": "string", "nullable": true}, "Value": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiVehicleExpectedContractV2Model": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleExpectedContractV2Model"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehicleExpectedContractV2Model": {"type": "object", "additionalProperties": false, "properties": {"VehicleId": {"type": "integer", "format": "int32"}, "VehicleExpectedContractType": {"$ref": "#/components/schemas/ApiVehicleExpectedContractType"}, "ContractorId": {"type": "integer", "format": "int32"}, "FromDate": {"type": "string", "format": "date-time"}, "ContractId": {"type": "integer", "format": "int32", "nullable": true}, "ToDate": {"type": "string", "format": "date-time"}, "RegDate": {"type": "string", "format": "date-time", "nullable": true}, "AwaitingApproval": {"type": "boolean"}}}, "ApiVehicleExpectedContractType": {"type": "string", "description": "", "x-enumNames": ["MainContract", "FirstSubsidiaryContract", "SecondSubsidiaryContract"], "enum": ["MainContract", "FirstSubsidiaryContract", "SecondSubsidiaryContract"]}, "ApiListResponseModelOfApiTrafficIncidentReportingIncidentModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiTrafficIncidentReportingIncidentModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "description": "Id på händelsen i FRIDA (sätt till 0 när man skapar en ny)", "format": "int32"}, "PublicEventId": {"type": "string", "description": "Unikt id för händelsen (fylls i av FRIDA).", "nullable": true}, "ContractorId": {"type": "integer", "description": "Id på trafikföretaget i FRIDA som äger fordonet.", "format": "int32", "nullable": true}, "ContractorName": {"type": "string", "description": "Namn på trafikföretaget som äger fordonet (fylls i av FRIDA).", "nullable": true}, "InformerName": {"type": "string", "description": "Namn på den som lämnar uppgifter om händelsen.", "nullable": true}, "InformerMail": {"type": "string", "description": "E-postadress till den som lämnar uppgifter om händelsen.", "nullable": true}, "ContractorsOwnEventId": {"type": "string", "description": "Entreprenöre ns interna händelseid, entreprenörens id på den specifika händelsen i sitt eget system. Detta används tillsammans med Säkerhets nyckel om anrop skall uppdatera post.", "nullable": true}, "PrimaryInformer": {"type": "string", "description": "E-post till ursprunglig anmälare", "nullable": true}, "IncidentDateTime": {"type": "string", "description": "Händelsedatum", "format": "date-time"}, "RegistrationDate": {"type": "string", "description": "Registreringsdatum (fylls i av systemet)", "format": "date-time"}, "Title": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "nullable": true}, "Description": {"type": "string", "description": "Beskrivning", "nullable": true}, "VehicleTypeBus": {"type": "integer", "format": "int32"}, "VehicleTypeId": {"type": "integer", "description": "<PERSON>d för tra<PERSON>", "format": "int32"}, "VehicleId": {"type": "integer", "description": "Id för fordonet i FRIDA.", "format": "int32", "nullable": true}, "VehiclePublicId": {"type": "string", "description": "Registreringsnummer/Fordons-ID/temporärt id (fylls i av FRIDA)", "nullable": true}, "TrafficType": {"type": "integer", "description": "Trafiktyp, enbart för buss (1 = Regionalbuss, 2 = Tätortsbuss, 3 = Skolskjuts, 4 = Serviceresor)", "format": "int32", "nullable": true}, "IncidentCategory": {"type": "integer", "description": "Händelsekategori", "format": "int32", "nullable": true}, "PersonalInjury": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>, Om personskada ange o<PERSON> minst ett av fälten i antal lindrigt skadade, antal allvarligt skadade, antal avlidna", "nullable": true}, "MinorInjury": {"type": "integer", "description": "<PERSON><PERSON> l<PERSON> s<PERSON>", "format": "int32", "nullable": true}, "SeriousInjury": {"type": "integer", "description": "<PERSON><PERSON> s<PERSON>", "format": "int32", "nullable": true}, "Deaths": {"type": "integer", "description": "<PERSON><PERSON>", "format": "int32", "nullable": true}, "County": {"type": "integer", "description": "Id på Län i FRIDA", "format": "int32", "nullable": true}, "Municipality": {"type": "integer", "description": "Id på kommun i FRIDA", "format": "int32", "nullable": true}, "Line": {"type": "integer", "description": "Id på linjen i FRIDA", "format": "int32", "nullable": true}, "InternalLineNumber": {"type": "integer", "description": "Tekniskt linje<PERSON>, Något av Linjenr el<PERSON> måste vara ifylld för Buss och Spårvagn. Gäller ej om Annan plats har angivits", "format": "int32", "nullable": true}, "PublicLineDesignation": {"type": "string", "description": "<PERSON>lik<PERSON> l<PERSON>, Något av Linjenr eller <PERSON> måste vara ifylld för Buss och Spårvagn Gäller ej om Annan plats har angivits", "nullable": true}, "UserCommentOnMissingLine": {"type": "string", "description": "Kommentar om linje saknas på händelsen.", "nullable": true}, "Toward": {"type": "string", "description": "Riktning mot.", "nullable": true}, "StopArea": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ej obligatoriskt för personbil och båt - en hållplats anges med nummer och FRIDATHR hämtar rätt Hållplatsnamn. Vi läser in detta från Huvudmannens system. ", "format": "int32", "nullable": true}, "StopAreaName": {"type": "string", "description": "Hållplatsnamn (Fylls i av systemet)", "nullable": true}, "AnotherPlace": {"type": "string", "description": "<PERSON><PERSON> plats, om ej på hållplats.", "nullable": true}, "InvestigationAtContractor": {"type": "boolean", "description": "Utredning finns hos entreprenör, anger om det finns en utredning kopplad till händelse.", "nullable": true}, "PoliceReport": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sker utanför FRIDA men Polisanmälan kan skickas manuellt från FRIDA.", "nullable": true}, "Cancellation": {"type": "boolean", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> tur, om turen har blivit inställd p g a en oönskad händelse.", "nullable": true}, "EmergencyMeasures": {"type": "string", "nullable": true}, "InternalNote": {"type": "string", "description": "Intern kommentar", "nullable": true}, "Resources": {"type": "array", "description": "<PERSON><PERSON><PERSON> resurser, <PERSON><PERSON> <PERSON><PERSON><PERSON> \"Inga resurser\" an<PERSON>, så får det inte finnas några andra värden.\nResurser - 1: Ingen resurs tillkallad 2: Polis 3: Väktare 4: Ordningsvakt 5: Trafikledare 6: Sjukvårdspersonal 7: Ambulans 8: Brandkår 9: B<PERSON>rgare 10: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "nullable": true, "items": {"type": "integer", "format": "int32"}}}}, "ApiListResponseModelOfApiContractRouteModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiContractRouteModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiContractRouteModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "ContractId": {"type": "integer", "format": "int32"}, "InternalRouteNumber": {"type": "integer", "format": "int32", "nullable": true}, "PublicRouteNumber": {"type": "string", "nullable": true}, "RouteSummary": {"type": "string", "nullable": true}, "FromDate": {"type": "string", "format": "date-time", "nullable": true}, "ToDate": {"type": "string", "format": "date-time", "nullable": true}, "Comment": {"type": "string", "nullable": true}, "ExternalIdPubTrans": {"type": "string", "nullable": true}, "ExternalIdPubTransGID": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiContractV3Model": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiContractV3Model"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiContractV3Model": {"type": "object", "additionalProperties": false, "properties": {"Category": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiContractCategory"}]}, "Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "PacketNumber": {"type": "integer", "format": "int32", "nullable": true}, "Identifier": {"type": "string", "nullable": true}, "ExplanatoryText": {"type": "string", "nullable": true}, "FromDate": {"type": "string", "format": "date-time"}, "ToDate": {"type": "string", "format": "date-time"}, "OriginalToDate": {"type": "string", "format": "date-time"}, "NumberOfOptionalYears": {"type": "integer", "format": "int32"}, "NumberOfUsedOptionalYears": {"type": "integer", "format": "int32"}, "MainContractor": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiContractorModel"}]}, "EnvironmentalRequirementExhaustId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementExhaustABId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementExhaustCompensationLevel": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiEnvironmentalRequirementExhaustCompensationLevel"}]}, "EnvironmentalRequirementExhaustsIsFrozenFromYear": {"type": "string", "format": "date-time", "nullable": true}, "ExhaustAddition": {"type": "boolean", "nullable": true}, "ExhaustAdditionFromYear": {"type": "string", "format": "date-time", "nullable": true}, "ExhaustAdditionToYear": {"type": "string", "format": "date-time", "nullable": true}, "ExhaustAdditionAllowedOverdraftPercent": {"type": "integer", "format": "int32", "nullable": true}, "FunctionalRequirementPackageId": {"type": "integer", "format": "int32", "nullable": true}, "FunctionalRequirementPackageName": {"type": "string", "nullable": true}, "FirstTrafficDateForFunctionalRequirements": {"type": "string", "format": "date-time", "nullable": true}, "AdditionalFunctionalRequirementForOlderVehiclesInTrafficFromDate": {"type": "string", "format": "date-time", "nullable": true}, "EnvironmentalRequirementFuelId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementFuelName": {"type": "string", "nullable": true}, "CompensationWhenEnvironmentalRequirementFuelMeetsOrExceedsDemands": {"type": "string", "nullable": true}, "EnvironmentalRequirementFuelsIsFrozenFromYear": {"type": "string", "format": "date-time", "nullable": true}, "EnvironmentalRequirementAppendixId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementAppendixName": {"type": "string", "nullable": true}, "EnvironmentalAccountCalculationStartYear": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalAccountCalculationStartPeriod": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiReportingPeriodDefintion"}]}, "EnvironmentalRequirementFuelMethaneGasId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementFuelMethaneGasName": {"type": "string", "nullable": true}, "EnvironmentalReportIsRequired": {"type": "boolean", "nullable": true}, "EnvironmentalPerformanceId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalPerformanceABId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementGreenCarId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementGreenCarName": {"type": "string", "nullable": true}, "EnvironmentalRequirementReductionOfClimateChangingGasesId": {"type": "integer", "format": "int32", "nullable": true}, "ExternalIdPubTrans": {"type": "string", "nullable": true}, "ExternalIdPWS": {"type": "string", "nullable": true}, "SORTRequirementId": {"type": "integer", "format": "int32", "nullable": true}, "IsExcludedFromCalculations": {"type": "boolean", "nullable": true}, "AverageSeatOccupation": {"type": "integer", "format": "int32", "nullable": true}, "EmptyTripFactor": {"type": "integer", "format": "int32", "nullable": true}, "PlannedNumberOfVehicles": {"type": "integer", "format": "int32", "nullable": true}, "PlannedTotalDistanceOrHours": {"type": "integer", "format": "int32", "nullable": true}, "Comment": {"type": "string", "nullable": true}, "ContractVehicleType": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiVehicleTypeV2Model"}]}, "ResponsibleUserId": {"type": "string", "nullable": true}, "EnvironmentalRequirementPackageId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalAccountingRemindNrOfDaysBeforeEndDate": {"type": "integer", "format": "int32", "nullable": true}, "ContractSubContractors": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiContractSubContractors"}}}}, "ApiContractCategory": {"type": "integer", "description": "", "x-enumNames": ["CommonPublicTransport", "SpecialPublicTransport"], "enum": [1, 2]}, "ApiContractorModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "IsUmbrellaOrganization": {"type": "boolean"}, "CustomersGID": {"type": "integer", "format": "int32", "nullable": true}, "StandardTHRNumber": {"type": "integer", "format": "int32", "nullable": true}}}, "ApiEnvironmentalRequirementExhaustCompensationLevel": {"type": "string", "description": "", "x-enumNames": ["None", "Base", "Extended", "Custom"], "enum": ["None", "Base", "Extended", "Custom"]}, "ApiReportingPeriodDefintion": {"type": "integer", "description": "", "x-enumNames": ["Year", "Quarter1", "Quarter2", "Quarter3", "Quarter4", "HalfYear1", "Tertial1", "HalfYear2", "Tertial2", "Tertial3"], "enum": [0, 1, 2, 3, 4, 6, 10, 12, 20, 30]}, "ApiVehicleTypeV2Model": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "Comment": {"type": "string", "description": "Kommentar till trafikslaget", "nullable": true}, "TypeOfId": {"description": "Typ av publikt id", "oneOf": [{"$ref": "#/components/schemas/ApiVehicleIDTypes"}]}, "LabelForId": {"type": "string", "description": "Namn för <PERSON>-begrepp - exvis Regnr, Tågsetnr, Fartygsbenämning", "nullable": true}, "TSCode": {"type": "string", "description": "<PERSON><PERSON> ho<PERSON>sty<PERSON>", "nullable": true}, "StandardId": {"type": "integer", "description": "Standard id i FRIDA", "format": "int32"}, "TrafficIncidentReportingId": {"type": "integer", "description": "<PERSON><PERSON> fö<PERSON> inrapportering via THR tjänst", "format": "int32"}}}, "ApiVehicleIDTypes": {"type": "string", "description": "", "x-enumNames": ["Numeric", "AplhaNumeric", "RegistrationNumber"], "enum": ["Numeric", "AplhaNumeric", "RegistrationNumber"]}, "ApiContractSubContractors": {"type": "object", "additionalProperties": false, "properties": {"ContractorId": {"type": "integer", "format": "int32"}, "ContractorName": {"type": "string", "nullable": true}, "UmbrellaContractorId": {"type": "integer", "format": "int32", "nullable": true}, "UmbrellaContractorName": {"type": "string", "nullable": true}, "TypeOfContractConnection": {"$ref": "#/components/schemas/ApiTypeOfContractConnection"}}}, "ApiTypeOfContractConnection": {"type": "string", "description": "", "x-enumNames": ["IsSubContractorAndMemberOfMainContractor", "IsSubContractor", "IsSubContractorAsMemberOfAnotherSubContractor"], "enum": ["IsSubContractorAndMemberOfMainContractor", "IsSubContractor", "IsSubContractorAsMemberOfAnotherSubContractor"]}, "ApiListResponseModelOfApiEnvironmentalRequirementAppendixModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementAppendixModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEnvironmentalRequirementAppendixModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "Version": {"type": "string", "nullable": true}, "FromYear": {"type": "string", "format": "date-time"}, "StandardId": {"$ref": "#/components/schemas/ApiStandardEnvironmentalRequirementAppendices"}, "Comment": {"type": "string", "nullable": true}}}, "ApiStandardEnvironmentalRequirementAppendices": {"type": "string", "description": "", "x-enumNames": ["Miljökravsbilaga_2001", "Miljökravsbilaga_2004", "Miljökravsbilaga_2006", "Miljökravsbilaga_2007", "Miljökravsbilaga_2008", "Miljökravsbilaga_2009", "Miljökravsbilaga_före2001", "Miljökravsbilaga_2010", "Miljökravsbilaga_2011", "Miljökravsbilaga_2013"], "enum": ["Miljökravsbilaga_2001", "Miljökravsbilaga_2004", "Miljökravsbilaga_2006", "Miljökravsbilaga_2007", "Miljökravsbilaga_2008", "Miljökravsbilaga_2009", "Miljökravsbilaga_före2001", "Miljökravsbilaga_2010", "Miljökravsbilaga_2011", "Miljökravsbilaga_2013"]}, "ApiListResponseModelOfApiEnvironmentalRequirementExhaustModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementExhaustModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEnvironmentalRequirementExhaustModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "FromYear": {"type": "string", "format": "date-time"}, "ToYear": {"type": "string", "format": "date-time"}, "EnvironmentalRequirementAppendixId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementLadderNOxId": {"type": "integer", "format": "int32"}, "EnvironmentalRequirementLadderPMId": {"type": "integer", "format": "int32"}, "NotInUse": {"type": "boolean", "nullable": true}, "NoPMCompensationWhen": {"type": "number", "format": "double", "nullable": true}, "LastYearEqualsPreviousYear": {"type": "boolean", "nullable": true}, "AppliesToBussClass": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementExhaustAppliesToBussClass"}, "StandardId": {"type": "integer", "format": "int32", "nullable": true}, "Comment": {"type": "string", "nullable": true}, "ValueNOX": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiEnvironmentalRequirementExhaustCurrentValueModel"}]}, "ValuePM": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiEnvironmentalRequirementExhaustCurrentValueModel"}]}}}, "ApiEnvironmentalRequirementExhaustAppliesToBussClass": {"type": "integer", "description": "", "x-enumNames": ["NoneSpecified", "Class123", "ClassAB", "AllClasses"], "enum": [0, 1, 2, 3]}, "ApiEnvironmentalRequirementExhaustCurrentValueModel": {"type": "object", "additionalProperties": false, "properties": {"RequirementType": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementType"}, "AdditionalRequirementType": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiEnvironmentalRequirementLadderAdditionalRequirementType"}]}, "AdditionalRequirementDetailed": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiEnvironmentalRequirementLadderAdditionalRequirementDetailed"}]}, "YearConcerns": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementLadderYearConcerns"}, "Value": {"type": "number", "format": "double", "nullable": true}}}, "ApiEnvironmentalRequirementType": {"type": "integer", "description": "", "x-enumNames": ["Percent", "GramPerkWh", "EnvironmentalClass", "GenericEuroClassAndGreenCar", "GenericEuroClass"], "enum": [1, 2, 3, 4, 5]}, "ApiEnvironmentalRequirementLadderAdditionalRequirementType": {"type": "integer", "description": "", "x-enumNames": ["None", "IsPossibleButNoneIsSpecified", "GreenCar"], "enum": [0, 1, 2]}, "ApiEnvironmentalRequirementLadderAdditionalRequirementDetailed": {"type": "integer", "description": "", "x-enumNames": ["NoAdditionalRequirement", "CarWithUpToFourPassengers", "AppliesToAll"], "enum": [0, 1, 2]}, "ApiEnvironmentalRequirementLadderYearConcerns": {"type": "integer", "description": "", "x-enumNames": ["ExactlyThisYear", "FromThisYear"], "enum": [1, 2]}, "ApiListResponseModelOfApiEnvironmentalRequirementFuelModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementFuelModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEnvironmentalRequirementFuelModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "EnvironmentalRequirementAppendixId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementLadderId": {"type": "integer", "format": "int32"}, "FromYear": {"type": "string", "format": "date-time"}, "ToYear": {"type": "string", "format": "date-time"}, "StandardId": {"type": "integer", "format": "int32", "nullable": true}, "NotInUse": {"type": "boolean", "nullable": true}, "Comment": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiEnvironmentalRequirementReductionOfClimateChangingGasesModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEnvironmentalRequirementReductionOfClimateChangingGasesModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEnvironmentalRequirementReductionOfClimateChangingGasesModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "EnvironmentalRequirementAppendixId": {"type": "integer", "format": "int32", "nullable": true}, "EnvironmentalRequirementLadderId": {"type": "integer", "format": "int32"}, "FromYear": {"type": "string", "format": "date-time"}, "ToYear": {"type": "string", "format": "date-time"}, "NotInUse": {"type": "boolean", "nullable": true}, "StandardId": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiStandardReductionOfClimateChangingGases"}]}}}, "ApiStandardReductionOfClimateChangingGases": {"type": "integer", "description": "", "x-enumNames": ["<PERSON><PERSON>", "Base", "Extended"], "enum": [1, 2, 3]}, "ApiListResponseModelOfApiVehiclePlanModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehiclePlanModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehiclePlanModel": {"type": "object", "additionalProperties": false, "properties": {"ContractId": {"type": "integer", "format": "int32"}, "ContractName": {"type": "string", "nullable": true}, "ReserveToOrdinaryRatioPercent": {"type": "number", "format": "double"}}}, "ApiListResponseModelOfApiEnvironmentalAccountingAnswerModel2": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEnvironmentalAccountingAnswerModel2"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEnvironmentalAccountingAnswerModel2": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "AnswerStatus": {"type": "integer", "format": "int32"}, "ReviewStatus": {"type": "integer", "format": "int32"}, "ContractorId": {"type": "integer", "format": "int32", "nullable": true}, "ContractId": {"type": "integer", "format": "int32"}, "VehicleTypeId": {"type": "integer", "format": "int32"}, "MainContractorHasResponsibleUser": {"type": "boolean"}, "AnswerStatusString": {"type": "string", "nullable": true}, "ReviewStatusString": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiContractorModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiContractorModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiListResponseModelOfApiUnitModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiUnitModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiUnitModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "UnitName": {"type": "string", "nullable": true}, "UnitType": {"type": "string", "nullable": true}, "Abbreviation": {"type": "string", "nullable": true}, "Comment": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiTrafficIncidentReportingIncidentCategoryModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingIncidentCategoryModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiTrafficIncidentReportingIncidentCategoryModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Title": {"type": "string", "nullable": true}, "StandardId": {"$ref": "#/components/schemas/ApiStandardTrafficIncidentReportingIncidentCategory"}, "Caption": {"type": "string", "nullable": true}, "CodingAccordingToNationalCouncil": {"type": "string", "nullable": true}, "Laws": {"type": "string", "nullable": true}}}, "ApiStandardTrafficIncidentReportingIncidentCategory": {"type": "string", "description": "", "x-enumNames": ["Urspårning_avåkning", "<PERSON><PERSON><PERSON>_mellan_fordon", "Påkörning_oskyddad_trafikant", "Påkörning_övrigt", "Dörrklämning_fordon", "Av_på<PERSON><PERSON><PERSON>olycka", "<PERSON>n_fordonsrelaterad_olycka", "<PERSON>_fordon", "Sabotage", "Försenad_inställd_tur_oframkomlig_gata_väg", "Hot_of<PERSON>ande_ärekränkning_förare", "Hot_of<PERSON><PERSON><PERSON>_ärekränkning_övrig_personal", "Hot_of<PERSON>and<PERSON>_ärekränkning_resenär", "<PERSON><PERSON><PERSON>_misshand<PERSON>_förare", "<PERSON><PERSON><PERSON>_mot_<PERSON><PERSON><PERSON>_personal", "<PERSON><PERSON><PERSON>_mot_resenär", "Betalningsvägran", "<PERSON><PERSON><PERSON>_fr<PERSON><PERSON>_personal", "<PERSON><PERSON><PERSON>_fr<PERSON><PERSON>_resen<PERSON>r", "<PERSON><PERSON><PERSON>_mot_personal", "<PERSON><PERSON><PERSON>_mot_resenär", "Annan_ordningsstörning", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_vandalisering", "<PERSON><PERSON><PERSON><PERSON>_el<PERSON>_annan_säkerhetspersonal_alkohol_och_eller_drogpåverkad"], "enum": ["Urspårning_avåkning", "<PERSON><PERSON><PERSON>_mellan_fordon", "Påkörning_oskyddad_trafikant", "Påkörning_övrigt", "Dörrklämning_fordon", "Av_på<PERSON><PERSON><PERSON>olycka", "<PERSON>n_fordonsrelaterad_olycka", "<PERSON>_fordon", "Sabotage", "Försenad_inställd_tur_oframkomlig_gata_väg", "Hot_of<PERSON>ande_ärekränkning_förare", "Hot_of<PERSON><PERSON><PERSON>_ärekränkning_övrig_personal", "Hot_of<PERSON>and<PERSON>_ärekränkning_resenär", "<PERSON><PERSON><PERSON>_misshand<PERSON>_förare", "<PERSON><PERSON><PERSON>_mot_<PERSON><PERSON><PERSON>_personal", "<PERSON><PERSON><PERSON>_mot_resenär", "Betalningsvägran", "<PERSON><PERSON><PERSON>_fr<PERSON><PERSON>_personal", "<PERSON><PERSON><PERSON>_fr<PERSON><PERSON>_resen<PERSON>r", "<PERSON><PERSON><PERSON>_mot_personal", "<PERSON><PERSON><PERSON>_mot_resenär", "Annan_ordningsstörning", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_vandalisering", "<PERSON><PERSON><PERSON><PERSON>_el<PERSON>_annan_säkerhetspersonal_alkohol_och_eller_drogpåverkad"]}, "ApiListResponseModelOfApiTrafficIncidentReportingMunicipalitiesModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiTrafficIncidentReportingMunicipalitiesModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiTrafficIncidentReportingMunicipalitiesModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "SCBCode": {"type": "integer", "format": "int32", "nullable": true}, "StandardPublicId": {"type": "integer", "format": "int32", "nullable": true}, "TrafficIncidentReportingCountyId": {"type": "integer", "format": "int32"}, "PostBox": {"type": "string", "nullable": true}, "PostAdress": {"type": "string", "nullable": true}, "VisitingAdress": {"type": "string", "nullable": true}, "TelephoneNumber": {"type": "string", "nullable": true}, "Fax": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Web": {"type": "string", "nullable": true}, "DateUpdated": {"type": "string", "format": "date-time"}}}, "ApiListResponseModelOfApiCustomAttributeValueModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiCustomAttributeValueModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiListResponseModelOfApiEngineTypeModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiEngineTypeModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiEngineTypeModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "Label": {"type": "string", "nullable": true}, "Definition": {"type": "string", "nullable": true}}}, "ApiListResponseModelOfApiPeriodicVehicleInspectionStatusModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiPeriodicVehicleInspectionStatusModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiPeriodicVehicleInspectionStatusModel": {"type": "object", "additionalProperties": false, "properties": {"VehicleId": {"type": "integer", "format": "int32"}, "VehicleTypeId": {"type": "integer", "format": "int32"}, "ContractorId": {"type": "integer", "format": "int32", "nullable": true}, "InspectionDate": {"type": "string", "format": "date-time", "nullable": true}, "InspectedToDate": {"type": "string", "format": "date-time", "nullable": true}, "LatestUpdateVTR": {"type": "string", "format": "date-time", "nullable": true}, "StatusRoadTraficRegistry": {"$ref": "#/components/schemas/ApiInspectionVehicleStatus"}, "PublicIDString": {"type": "string", "nullable": true}, "TemporaryID": {"type": "string", "nullable": true}, "InternalNumber": {"type": "integer", "format": "int32", "nullable": true}, "CommercialTraffic": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiCommercialTraffic"}]}, "CommercialTrafficDate": {"type": "string", "format": "date-time", "nullable": true}, "InspectionStatus": {"type": "string", "nullable": true}}}, "ApiInspectionVehicleStatus": {"type": "string", "description": "", "x-enumNames": ["Missing2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deregistered", "Unoccupied", "Missing"], "enum": ["Missing2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deregistered", "Unoccupied", "Missing"]}, "ApiCommercialTraffic": {"type": "string", "description": "", "x-enumNames": ["Bus<PERSON>raff<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TaxiTaxameterTraffic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MissingPermission"], "enum": ["Bus<PERSON>raff<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TaxiTaxameterTraffic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MissingPermission"]}, "ApiListResponseModelOfApiVehicleReferencePointModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleReferencePointModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehicleReferencePointModel": {"type": "object", "additionalProperties": false, "required": ["Id"], "properties": {"Id": {"type": "integer", "description": "Trafikeringsperiodens id.", "format": "int32"}, "VehicleId": {"type": "integer", "description": "Fordons-ID för fordonet som trafikeringsperioden tillhör", "format": "int32"}, "FromDate": {"type": "string", "description": "Trafikeringsperiodens start datum.", "format": "date-time", "nullable": true}, "ToDate": {"type": "string", "description": "Trafikeringsperiodens slut datum.", "format": "date-time", "nullable": true}, "ContractAreaId": {"type": "integer", "description": "Id för avtalsområde som trafikeringsperioden är knuten till", "format": "int32"}, "ContractAreaName": {"type": "string", "description": "Namn på avtalsområde som trafikeringsperioden är knuten till", "nullable": true}}}, "ApiListResponseModelOfApiVehicleTypeV2Model": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleTypeV2Model"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiListResponseModelOfApiVehicleInspectionProtocolRegularControlModel2": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleInspectionProtocolRegularControlModel2"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehicleInspectionProtocolRegularControlModel2": {"allOf": [{"$ref": "#/components/schemas/ApiVehicleInspectionProtocolRegularControlModel"}, {"type": "object", "additionalProperties": false, "properties": {"CommissionedInspectionLastInspectionDate": {"type": "string", "format": "date-time", "nullable": true}, "CommissionedInspectionLastInspectionDateChanged": {"type": "boolean", "nullable": true}}}]}, "ApiVehicleInspectionProtocolRegularControlModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "VehicleInspectionId": {"type": "integer", "format": "int32"}, "SequenceNumber": {"type": "integer", "format": "int32"}, "RegistrationDate": {"type": "string", "format": "date-time", "nullable": true}, "InspectionDateTime": {"type": "string", "format": "date-time", "nullable": true}, "ControllerUserName": {"type": "string", "nullable": true}, "CreatedByUserName": {"type": "string", "nullable": true}, "VehicleInspectionFormId": {"type": "integer", "format": "int32", "nullable": true}, "PublicProtocolId": {"type": "string", "nullable": true}, "isDone": {"type": "boolean"}, "VehicleInspectionType": {"$ref": "#/components/schemas/ApiVehicleInspectionProtocolType"}, "hasFailedInspection": {"type": "boolean"}, "totalRating": {"type": "number", "format": "double", "nullable": true}, "targetRating": {"type": "number", "format": "double", "nullable": true}, "isReviewedForMailNotification": {"type": "boolean"}, "isReviewed": {"type": "boolean"}, "ReviewDate": {"type": "string", "format": "date-time", "nullable": true}, "ContractId": {"type": "integer", "format": "int32", "nullable": true}, "isReCalculated": {"type": "boolean"}, "VehicleInspectionStationId": {"type": "integer", "format": "int32", "nullable": true}, "VehicleInspectionStationAssignmentDate": {"type": "string", "format": "date-time", "nullable": true}, "CommissionedInspectionReminder": {"type": "string", "format": "date-time", "nullable": true}, "VehicleInspectionStationLock": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiVehicleInspectionProtocolStationLock"}]}, "ControlPointValues": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleInspectionProtocolDataControlPointValue"}}, "PublicIDString": {"type": "string", "nullable": true}, "PublicIdNumber": {"type": "integer", "format": "int32", "nullable": true}, "ContractorName": {"type": "string", "nullable": true}, "ContractorsVehicleNumber": {"type": "integer", "format": "int32", "nullable": true}, "ControlTypeName": {"type": "string", "nullable": true}}}, "ApiVehicleInspectionProtocolType": {"type": "string", "description": "", "x-enumNames": ["FirstInspection", "SubsequentInspection"], "enum": ["FirstInspection", "SubsequentInspection"]}, "ApiVehicleInspectionProtocolStationLock": {"type": "string", "description": "", "x-enumNames": ["Selectable", "WillBeLockedTonight", "LockedLight", "LockedHard"], "enum": ["Selectable", "WillBeLockedTonight", "LockedLight", "LockedHard"]}, "ApiVehicleInspectionProtocolDataControlPointValue": {"type": "object", "additionalProperties": false, "properties": {"ControlPointId": {"type": "integer", "format": "int32"}, "ControlPointLabel": {"type": "string", "nullable": true}, "ControlPointStandardId": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiStandardVehicleInspectionControlPoints"}]}, "Value": {"type": "string", "nullable": true}, "Unit": {"type": "string", "nullable": true}, "ValueType": {"$ref": "#/components/schemas/ApiControlPointValueType"}}}, "ApiStandardVehicleInspectionControlPoints": {"type": "string", "description": "", "x-enumNames": ["VehicleIdentification", "VehicleOwnersInternalNumber", "Contractor", "Place", "LineNumberFreeInput", "Weather", "LineNumberInRegister", "MainContract", "ControlDateAndTime"], "enum": ["VehicleIdentification", "VehicleOwnersInternalNumber", "Contractor", "Place", "LineNumberFreeInput", "Weather", "LineNumberInRegister", "MainContract", "ControlDateAndTime"]}, "ApiControlPointValueType": {"type": "string", "description": "", "x-enumNames": ["Bool", "Choice", "Date", "Rating", "String", "Int", "Double"], "enum": ["Bool", "Choice", "Date", "Rating", "String", "Int", "Double"]}, "ApiListResponseModelOfApiVehicleInspectionModel": {"type": "object", "additionalProperties": false, "properties": {"Items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleInspectionModel"}}, "Count": {"type": "integer", "format": "int32"}, "TotalCount": {"type": "integer", "format": "int32"}}}, "ApiVehicleInspectionModel": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "VehicleInspectionControlType": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ApiVehicleInspectionControlType"}]}, "VehicleId": {"type": "integer", "format": "int32"}, "PublicIDString": {"type": "string", "nullable": true}, "PublicIdNumber": {"type": "integer", "format": "int32", "nullable": true}, "ReferenceNumber": {"type": "integer", "format": "int32"}, "ContractorId": {"type": "integer", "format": "int32", "nullable": true}, "ContractorName": {"type": "string", "nullable": true}, "ContractorsVehicleNumber": {"type": "integer", "format": "int32", "nullable": true}, "isCompleted": {"type": "boolean"}, "ContractAreaId": {"type": "integer", "format": "int32", "nullable": true}, "ContractAreaName": {"type": "string", "nullable": true}, "DeliveryControlPlannedTrafficStart": {"type": "string", "format": "date-time", "nullable": true}, "DeliveryControlConcearnsContractId": {"type": "integer", "format": "int32", "nullable": true}, "IsVehicleInspectionFlowHandled": {"type": "boolean", "nullable": true}, "LineAndContracts": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ApiVehicleInspectionLineAndContractModel"}}}}, "ApiVehicleInspectionControlType": {"type": "object", "additionalProperties": false, "properties": {"Id": {"type": "integer", "format": "int32"}, "Name": {"type": "string", "nullable": true}, "MaxNumberOfFollowUpInspections": {"type": "integer", "format": "int32", "nullable": true}, "allowChangeOfName": {"type": "boolean"}, "isActive": {"type": "boolean"}, "MainControlType": {"$ref": "#/components/schemas/ApiStandardInspectionMainControlType"}, "ControlCategory": {"$ref": "#/components/schemas/ApiStandardControlCategory"}, "InspectionType": {"$ref": "#/components/schemas/ApiStandardInspectionType"}}}, "ApiStandardInspectionMainControlType": {"type": "string", "description": "", "x-enumNames": ["RatingCleaningMaintenceEquipmentEtcOnly", "DataControlOnly", "MixedControlDataAndVehicleMaintenance"], "enum": ["RatingCleaningMaintenceEquipmentEtcOnly", "DataControlOnly", "MixedControlDataAndVehicleMaintenance"]}, "ApiStandardControlCategory": {"type": "string", "description": "", "x-enumNames": ["Normal", "DeliveryAndTrafficStart", "DepotControl", "Targeted", "TrafficControl"], "enum": ["Normal", "DeliveryAndTrafficStart", "DepotControl", "Targeted", "TrafficControl"]}, "ApiStandardInspectionType": {"type": "string", "description": "", "x-enumNames": ["Normal", "StationControl", "ContractorsOwnInspection"], "enum": ["Normal", "StationControl", "ContractorsOwnInspection"]}, "ApiVehicleInspectionLineAndContractModel": {"type": "object", "additionalProperties": false, "properties": {"LineInternalNumber": {"type": "integer", "format": "int32", "nullable": true}, "LinePublicName": {"type": "string", "nullable": true}, "LineTimeTableSummary": {"type": "string", "nullable": true}, "LineContractId": {"type": "integer", "format": "int32", "nullable": true}, "InspectionMainContractId": {"type": "integer", "format": "int32", "nullable": true}, "InspectionCommentOnLine": {"type": "string", "nullable": true}, "InspectionCommentOnMainContract": {"type": "string", "nullable": true}}}}, "securitySchemes": {"oauth2pwd": {"type": "oauth2", "flows": {"password": {"authorizationUrl": "https://ruter-auth.frida.nordicport.se/connect/authorize", "tokenUrl": "https://ruter-auth.frida.nordicport.se/connect/token", "scopes": {"fridaApi": "Frida API"}}}}, "oauth2client": {"type": "oauth2", "flows": {"clientCredentials": {"authorizationUrl": "https://ruter-auth.frida.nordicport.se/connect/authorize", "tokenUrl": "https://ruter-auth.frida.nordicport.se/connect/token", "scopes": {"fridaApi": "Frida API"}}}}}}, "tags": [{"name": "vehicle"}, {"name": "trafficincidentreporting"}, {"name": "contract"}, {"name": "vehicleplan"}, {"name": "environmentalaccounting"}, {"name": "system"}, {"name": "periodicvehicleinspection"}, {"name": "vehicleinspection"}]}