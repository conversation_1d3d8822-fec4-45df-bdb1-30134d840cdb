<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

<property name="SERVICE_NAME" value="fleet" />

<property name="CONSOLE_LOG_PATTERN"
          value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr([%X{correlationId:-}]){magenta} %clr([imp:%X{importId:-}, v:%X{vehicleId:-}/%X{vehicleRef:-}]){yellow} %clr([%X{phase:-}/%X{source:-}]){blue} %m%n%wEx"/>

<appender name="JSON_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
        <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
            <name>healthCheckFilter</name>
            <expression>
                return message.contains("/actuator/health");
            </expression>
        </evaluator>
        <OnMatch>DENY</OnMatch>
        <OnMismatch>NEUTRAL</OnMismatch>
    </filter>
    <encoder class="net.logstash.logback.encoder.LogstashEncoder">
        <includeMdcKeyName>correlationId</includeMdcKeyName>
        <includeMdcKeyName>importId</includeMdcKeyName>
        <includeMdcKeyName>lastProcessedVehicleId</includeMdcKeyName>
        <includeMdcKeyName>lastProcessedVehicleRef</includeMdcKeyName>
        <includeMdcKeyName>jobName</includeMdcKeyName>
        <customFields>{"service_name":"${SERVICE_NAME}"}</customFields>
        <fieldNames>
            <timestamp>timestamp</timestamp>
            <message>message</message>
            <logger>logger_name</logger>
            <thread>thread_name</thread>
            <level>level</level>
            <levelValue>[ignore]</levelValue>
            <version>[ignore]</version>
        </fieldNames>
    </encoder>
</appender>

<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
        <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        <charset>utf8</charset>
    </encoder>
</appender>

<logger name="no.ruter.kosi.fleet" level="DEBUG" />

<root level="INFO">
    <appender-ref ref="JSON_CONSOLE" />
</root>

<springProfile name="local">
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>
</springProfile>
</configuration>