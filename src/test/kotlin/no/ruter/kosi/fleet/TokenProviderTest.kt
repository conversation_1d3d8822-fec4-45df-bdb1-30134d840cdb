package no.ruter.kosi.fleet

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.FridaApiProperties
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import okhttp3.Call
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class TokenProviderTest {
    private val mockHttpClient = mockk<OkHttpClient>()
    private val mockCall = mockk<Call>()
    private val mockResponse = mockk<Response>()
    private val mockResponseBody = mockk<ResponseBody>()

    private lateinit var tokenProvider: TokenProvider

    @BeforeEach
    fun setup() {
        every { mockHttpClient.newCall(any()) } returns mockCall
        every { mockCall.execute() } returns mockResponse
        every { mockResponse.isSuccessful } returns true
        every { mockResponse.body } returns mockResponseBody
        every { mockResponseBody.string() } returns """{"access_token": "mock-token", "expires_in": 3600}"""
        every { mockResponse.close() } just runs

        tokenProvider =
            TokenProvider(
                apiProperties =
                    FridaApiProperties(
                        clientId = "test-client",
                        clientSecret = "test-secret",
                        username = "test-user",
                        password = "test-pass",
                        scope = "test-scope",
                        tokenUrl = "http://mock-token-url.com",
                        baseUrl = "http://mock-api.com",
                    ),
                objectMapper = ObjectMapper().apply { registerKotlinModule() },
            ).apply { this.tokenHttpClient = mockHttpClient }
    }

    @Test
    fun `getAccessToken - fetches new token when cache is empty`() {
        val token = tokenProvider.getAccessToken()
        assertEquals("mock-token", token)

        verify { mockHttpClient.newCall(any<Request>()) }
    }
}
