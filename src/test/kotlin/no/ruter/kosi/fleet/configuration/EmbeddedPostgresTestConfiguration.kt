package no.ruter.kosi.fleet.configuration

import io.zonky.test.db.postgres.embedded.EmbeddedPostgres
import jakarta.annotation.PreDestroy
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import javax.sql.DataSource

@TestConfiguration
class EmbeddedPostgresTestConfiguration {
    
    private val logger = LoggerFactory.getLogger(EmbeddedPostgresTestConfiguration::class.java)
    private var embeddedPostgres: EmbeddedPostgres? = null
    
    @Bean
    @Primary
    fun testAuroraDataSource(): DataSource {
        logger.info("Starting embedded PostgreSQL for tests...")
        
        embeddedPostgres = EmbeddedPostgres.builder()
            .setPort(5433)
            .start()
            
        val dataSource = embeddedPostgres!!.postgresDatabase
        logger.info("Embedded PostgreSQL started successfully on port 5433 for tests")
        
        return dataSource
    }
    
    @Bean(name = ["snowflakeDataSource"])
    fun testSnowflakeDataSource(): DataSource {
        logger.info("Using embedded PostgreSQL for Snowflake in tests...")
        
        if (embeddedPostgres == null) {
            embeddedPostgres = EmbeddedPostgres.builder()
                .setPort(5433)
                .start()
        }
        
        return embeddedPostgres!!.postgresDatabase
    }
    
    @PreDestroy
    fun cleanup() {
        embeddedPostgres?.close()
        logger.info("Embedded PostgreSQL stopped for tests")
    }
}
