package no.ruter.kosi.fleet.configuration

import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleFieldQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleFieldQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleQualityRepository
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary


@TestConfiguration
class SCD2IntegrationTestConfig {
    // Data source is provided by TestDataSourceConfiguration

    @Bean
    @Primary
    fun auroraEventDataRepository(
        jpaRepository: SpringDataEventDataRepository,
        @Qualifier("eventDataSCD2Service") scd2Service: ISCD2Service<EventData, Int, String>
    ): AuroraEventDataRepository {
        return AuroraEventDataRepository(jpaRepository, scd2Service)
    }

    @Bean
    @Primary
    fun auroraVehicleQualityRepository(
        vehicleQualityJpaRepository: SpringDataVehicleQualityRepository,
    ): AuroraVehicleQualityRepository {
        val repository = AuroraVehicleQualityRepository(vehicleQualityJpaRepository)
        return repository
    }

    @Bean
    @Primary
    fun auroraVehicleFieldQualityRepository(
        vehicleFieldQualityRepository: SpringDataVehicleFieldQualityRepository
    ): AuroraVehicleFieldQualityRepository {
        return AuroraVehicleFieldQualityRepository(vehicleFieldQualityRepository)
    }
}