package no.ruter.kosi.fleet.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.mockk
import io.mockk.spyk
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.FridaApiProperties
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleCapacityRepository
import okhttp3.OkHttpClient
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class TestConfiguration {
    @Bean
    @Primary
    fun mockOkHttpClient(): OkHttpClient = mockk(relaxed = true)

    @Bean
    @Primary
    fun mockTokenProvider(
        apiProperties: FridaApiProperties,
        objectMapper: ObjectMapper,
    ): TokenProvider = spyk(TokenProvider(apiProperties, objectMapper))


    @Bean
    @Primary
    fun mockSnowflakeVehicleCapacityRepository(): SnowflakeVehicleCapacityRepository = mockk<SnowflakeVehicleCapacityRepository>()
}