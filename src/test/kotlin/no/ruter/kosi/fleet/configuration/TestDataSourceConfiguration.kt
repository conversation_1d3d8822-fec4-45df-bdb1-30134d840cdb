package no.ruter.kosi.fleet.configuration

import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName

@Testcontainers
abstract class BaseIntegrationTest {

    companion object {
        @Container
        @JvmStatic
        val postgresContainer = PostgreSQLContainer(DockerImageName.parse("postgres:15-alpine"))
            .withDatabaseName("fleet_test")
            .withUsername("test_user")
            .withPassword("test_password")

        @DynamicPropertySource
        @JvmStatic
        fun configureProperties(registry: DynamicPropertyRegistry) {
            registry.add("spring.datasource.aurora.url") { postgresContainer.jdbcUrl }
            registry.add("spring.datasource.aurora.username") { postgresContainer.username }
            registry.add("spring.datasource.aurora.password") { postgresContainer.password }
            registry.add("spring.datasource.aurora.driver-class-name") { "org.postgresql.Driver" }

            registry.add("spring.datasource.snowflake.url") { postgresContainer.jdbcUrl }
            registry.add("spring.datasource.snowflake.username") { postgresContainer.username }
            registry.add("spring.datasource.snowflake.password") { postgresContainer.password }
            registry.add("spring.datasource.snowflake.driver-class-name") { "org.postgresql.Driver" }
        }
    }
}
