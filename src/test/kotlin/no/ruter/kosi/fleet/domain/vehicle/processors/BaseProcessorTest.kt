package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import io.mockk.mockk
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import no.ruter.kosi.fleet.domain.vehicle.processors.common.getErrorMessage
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
abstract class BaseProcessorTest<T : VehicleFieldProcessor> {

    protected var mockJob = mockk<FleetJob>(relaxed = true)
    protected var mockInternalVehicle = mockk<InternalVehicle>(relaxed = true)

    protected abstract val processor: T

    protected open fun createEventWithEventData(
        eventDataList: List<EventData>,
        source: String,
        priority: Int
    ): Event {
        return Event.create(
            vehicle = mockInternalVehicle,
            job = mockJob,
            source = source,
            priority = priority,
            eventDataList = eventDataList,
            timestamp = Instant.now()
        )
    }


    protected fun assertProcessSuccess(
        eventData: Map<String, EventData?> = emptyMap(),
        expectedValue: Any?,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        lastSnapshot: VehicleEntity? = null,
        timestamp: Instant? = null
    ) {
        val result = processor.process(
            eventData,
            processorResults,
            lastSnapshot,
            timestamp
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()?.getErrorMessage()}")
        val processedField = (result as Either.Right<ProcessedField>).value
        assertEquals(expectedValue, processedField.value)
    }

    protected fun assertProcessFailure(
        eventData: Map<String, EventData?> = emptyMap(),
        expectedErrorType: KClass<out ProcessingError>,
        expectedMessage: String? = null,
        processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        lastSnapshot: VehicleEntity? = null,
        timestamp: Instant? = null
    ) {
        val result = processor.process(
            eventData,
            processorResults,
            lastSnapshot,
            timestamp
        )

        assertTrue(result.isLeft(), "Expected failure but got success")
        val error = (result as Either.Left<ProcessingError>).value
        assertTrue(
            expectedErrorType.isInstance(error),
            "Expected ${expectedErrorType.simpleName} but got ${error::class.simpleName}"
        )
        expectedMessage?.let { message ->
            assertEquals(message, error.getErrorMessage())
        }
    }

    protected fun createQuality(value: Any) = VehicleFieldQuality(
        value = value,
        type = QualityType.VALID,
        message = "Test quality"
    )

    protected fun createProcessedField(
        processor: KClass<out VehicleFieldProcessor>,
        value: Any?,
        quality: VehicleFieldQuality = createQuality(value ?: "")
    ) = ProcessedField(
        originProcessor = processor,
        value = value,
        quality = quality
    )

    //Expand or modify this as needed when mocking Event
    data class EventOptions(
        val source: String = "FRIDA",
        val priority: Int = 1,
    )

    data class ProcessorTestCase<T : Any>(
        val name: String,
        val input: Map<String, String?> = emptyMap(),
        val expectedResult: Either<ProcessingError, ProcessedField>? = null,
        val expectedValue: T? = null,
        val expectedError: ProcessingError? = null,
        val processorResults: Map<KClass<out VehicleFieldProcessor>, ProcessedField> = emptyMap(),
        val lastSnapshot: VehicleEntity? = null,
        val timestamp: Instant? = null,
        val includeEvent: EventOptions = EventOptions()
        ) {
        init {
            require(expectedResult != null || expectedValue != null || expectedError != null) {
                "Either expectedResult, expectedValue, or expectedError must be provided"
            }
        }
    }
    
    protected fun <T : Any> runTestCases(testCases: List<ProcessorTestCase<T>>) {
        testCases.forEach { testCase ->

            val event = createEventWithEventData(emptyList(),
                source = testCase.includeEvent.source,
                priority = testCase.includeEvent.priority
            )

            // For each input field, add EventData to the Event
            testCase.input.forEach { (fieldName, value) ->
                event.addEventData(fieldName, value)
            }

            // Build inputEventData map
            val inputEventData = event.eventDataList.associateBy { it.fieldName }

            val result = processor.process(
                inputEventData,
                testCase.processorResults,
                testCase.lastSnapshot,
                testCase.timestamp
            )

            when {
                testCase.expectedResult != null -> {
                    assertEquals(testCase.expectedResult, result, "Test case '${testCase.name}' failed")
                }
                testCase.expectedValue != null -> {
                    assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()?.getErrorMessage()}")
                    val processedField = (result as Either.Right<ProcessedField>).value
                    assertEquals(testCase.expectedValue, processedField.value, "Test case '${testCase.name}' failed")
                }
                testCase.expectedError != null -> {
                    assertTrue(result.isLeft(), "Expected failure but got success for test case '${testCase.name}'")
                    val error = (result as Either.Left<ProcessingError>).value
                    assertTrue(
                        testCase.expectedError::class.isInstance(error),
                        "Expected ${testCase.expectedError::class.simpleName} but got ${error::class.simpleName} in test case '${testCase.name}'"
                    )
                    assertEquals(
                        testCase.expectedError.getErrorMessage(),
                        error.getErrorMessage(),
                        "Test case '${testCase.name}' failed: Error messages don't match"
                    )
                }
            }
        }
    }
}
