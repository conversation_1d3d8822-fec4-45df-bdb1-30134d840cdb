package no.ruter.kosi.fleet.domain.vehicle.processors

import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

open class ChassisNumberProcessorTest : BaseProcessorTest<ChassisNumberProcessor>() {
    override val processor = ChassisNumberProcessor()
    
    companion object {
        @JvmStatic
        fun testCases() = listOf(
            // happy cases
            ProcessorTestCase(
                name = "valid chassis number from customAttr_ChassiNumber",
                input = mapOf("customAttr_ChassiNumber" to "WDDWF4KB2KR000001"),
                expectedValue = "WDDWF4KB2KR000001"
            ),
            
            ProcessorTestCase(
                name = "valid chassis number from chassiNumber",
                input = mapOf("chassiNumber" to "WDDWF4KB2KR000001"),
                expectedValue = "WDDWF4KB2KR000001"
            ),
            
            ProcessorTestCase(
                name = "convert to uppercase and remove spaces",
                input = mapOf("customAttr_ChassiNumber" to "wdd wf4 kb2 kr0 0001"),
                expectedValue = "WDDWF4KB2KR00001"
            ),
            
            // priority test
            ProcessorTestCase(
                name = "prioritize chassiNumber over customAttr_ChassiNumber",
                input = mapOf(
                    "chassiNumber" to "PRIORITY",
                    "customAttr_ChassiNumber" to "SECONDARY"
                ),
                expectedValue = "PRIORITY"
            ),
            
            // error cases
            ProcessorTestCase(
                name = "missing field",
                input = emptyMap(),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = ChassisNumberProcessor::class,
                    message = "No value provided for required string field"
                )
            ),
            
            ProcessorTestCase(
                name = "null value",
                input = mapOf("customAttr_ChassiNumber" to null),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = ChassisNumberProcessor::class,
                    message = "No value provided for required string field"
                )
            ),
            
            ProcessorTestCase(
                name = "empty string",
                input = mapOf("customAttr_ChassiNumber" to ""),
                expectedError = ProcessingError.InvalidFormat(
                    processor = ChassisNumberProcessor::class,
                    value = "",
                    message = "Empty string value"
                )
            )
        )
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    protected open fun `test chassis number processor`(testCase: ProcessorTestCase<String>) {
        runTestCases(listOf(testCase))
    }
}
