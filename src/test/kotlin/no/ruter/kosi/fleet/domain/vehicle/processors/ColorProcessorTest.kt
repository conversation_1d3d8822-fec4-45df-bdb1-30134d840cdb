package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ColorProcessorTest : BaseProcessorTest<ColorProcessor>() {
    override val processor = ColorProcessor()

    @Test
    fun `should parse Norwegian green color to Green`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Regionbuss ny (lys grønn)")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals("Green", processedField.value)
        assertEquals(QualityType.ENRICHED_VALUE, processedField.quality.type)
        assertEquals("Parsed color 'Regionbuss ny (lys grønn)' to 'Green'", processedField.quality.message)
    }

    @Test
    fun `should parse English green color to Green`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Light Green")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Green"
        )
    }

    @Test
    fun `should parse Norwegian red color to Red`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Rød buss")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Red"
        )
    }

    @Test
    fun `should parse English red color to Red`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Dark Red")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Red"
        )
    }

    @Test
    fun `should return original value with UNKNOWN_VALUE quality for unknown colors`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Blue")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals("", processedField.value, "Expected original color value for unknown color")
        assertEquals(QualityType.UNKNOWN_VALUE, processedField.quality.type, "Expected UNKNOWN_VALUE quality type")
        assertEquals("Could not determine color from value 'Blue'", processedField.quality.message)
    }

    @Test
    fun `should return original value with UNKNOWN_VALUE quality for other unknown colors`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Yellow")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals("", processedField.value, "Expected original color value for unknown color")
        assertEquals(QualityType.UNKNOWN_VALUE, processedField.quality.type, "Expected UNKNOWN_VALUE quality type")
        assertEquals("Could not determine color from value 'Yellow'", processedField.quality.message)
    }

    @Test
    fun `should handle case insensitive matching`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "GRØNN")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Green"
        )
    }

    @Test
    fun `should parse Swedish green color to Green`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Grön färg")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Green"
        )
    }

    @Test
    fun `should parse Swedish red color to Red`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Röd färg")
        assertProcessSuccess(
            eventData = eventData.associateBy { it.fieldName },
            expectedValue = "Red"
        )
    }

    @Test
    fun `should return VALID quality when color is already Green`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Green")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals("Green", processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
        assertEquals("", processedField.quality.message)
    }

    @Test
    fun `should return VALID quality when color is already Red`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "Red")
        val result = processor.process(
            eventData.associateBy { it.fieldName },
            emptyMap(),
            null,
            null
        )

        assertTrue(result.isRight(), "Expected success but got ${result.leftOrNull()}")
        val processedField = (result as Either.Right).value
        assertEquals("Red", processedField.value)
        assertEquals(QualityType.VALID, processedField.quality.type)
        assertEquals("", processedField.quality.message)
    }

    @Test
    fun `should return error when no color data is found`() {
        assertProcessFailure(
            eventData = emptyMap(),
            expectedErrorType = ProcessingError.MissingRequiredData::class,
            expectedMessage = "Missing data for processor 'ColorProcessor': No color data found"
        )
    }

    @Test
    fun `should handle empty color string`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to "")
        assertProcessFailure(
            eventData = eventData.associateBy { it.fieldName },
            expectedErrorType = ProcessingError.MissingRequiredData::class,
            expectedMessage = "Missing data for processor 'ColorProcessor': No color data found"
        )
    }

    @Test
    fun `should handle null color value`() {
        val eventData = toEventData("customAttr_Utvendig_lakk" to null)
        assertProcessFailure(
            eventData = eventData.associateBy { it.fieldName },
            expectedErrorType = ProcessingError.MissingRequiredData::class,
            expectedMessage = "Missing data for processor 'ColorProcessor': No color data found"
        )
    }
}
