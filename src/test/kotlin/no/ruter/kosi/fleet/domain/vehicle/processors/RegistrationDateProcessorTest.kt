package no.ruter.kosi.fleet.domain.vehicle.processors

import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate

open class RegistrationDateProcessorTest : BaseProcessorTest<RegistrationDateProcessor>() {
    override val processor = RegistrationDateProcessor()
    
    companion object {
        @JvmStatic
        fun testCases() = listOf(
            // happy case
            ProcessorTestCase(
                name = "ISO-date-format",
                input = mapOf("registrationDate" to "2023-01-15T00:00Z"),
                expectedValue = LocalDate.parse("2023-01-15")
            ),

            // error cases
            ProcessorTestCase(
                name = "missing field",
                input = emptyMap(),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = RegistrationDateProcessor::class,
                    message = "No registration date provided"
                )
            ),

            ProcessorTestCase(
                name = "null value",
                input = mapOf("registrationDate" to null),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = RegistrationDateProcessor::class,
                    message = "No registration date provided"
                )
            ),

            ProcessorTestCase(
                name = "invalid format - wrong format",
                input = mapOf("registrationDate" to "15-01-2023"),
                expectedError = ProcessingError.InvalidFormat(
                    processor = RegistrationDateProcessor::class,
                    value = "15-01-2023",
                    message = "Invalid date format. java.time.format.DateTimeParseException: Text '15-01-2023' could not be parsed at index 0. Expected format: yyyy-MM-dd'T'HH:mmX"
                )
            ),
        )
    }
//
    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    protected open fun `test registration date processor`(testCase: ProcessorTestCase<String>) {
        runTestCases(listOf(testCase))
    }
}
