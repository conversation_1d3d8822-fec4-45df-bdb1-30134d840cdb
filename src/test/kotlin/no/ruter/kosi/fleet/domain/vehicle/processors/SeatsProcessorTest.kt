package no.ruter.kosi.fleet.domain.vehicle.processors

import arrow.core.Either
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.test.assertEquals
import kotlin.test.assertTrue

open class SeatsProcessorTest : BaseProcessorTest<SeatsProcessor>() {
    override val processor = SeatsProcessor()
    
    companion object {
        @JvmStatic
        fun testCases() = listOf(
            // happy case
            ProcessorTestCase(
                name = "valid integer value",
                input = mapOf(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "42"
                ),
                expectedValue = 42
            ),
            
            // error cases
            ProcessorTestCase(
                name = "missing field",
                input = emptyMap(),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = SeatsProcessor::class,
                    message = "No value provided for required integer field"
                )
            ),
            
            ProcessorTestCase(
                name = "non-numeric value",
                input = mapOf(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "ceci-n'est-pas-une-number"
                ),
                expectedError = ProcessingError.InvalidFormat(
                    processor = SeatsProcessor::class,
                    message = "Cannot parse String to Int: ceci-n'est-pas-une-number",
                    value = "ceci-n'est-pas-une-number",
                )
            ),
            
            ProcessorTestCase(
                name = "null value",
                input = mapOf(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to null
                ),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = SeatsProcessor::class,
                    message = "No value provided for required integer field",
                )
            ),
            
            ProcessorTestCase(
                name = "empty string",
                input = mapOf(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to ""
                ),
                expectedError = ProcessingError.InvalidFormat(
                    processor = SeatsProcessor::class,
                    message = "Cannot parse String to Int: ",
                    value = "",
                )
            ),
            
            ProcessorTestCase(
                name = "decimal value",
                input = mapOf(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "42.5"
                ),
                expectedError = ProcessingError.InvalidFormat(
                    processor = SeatsProcessor::class,
                    message = "Cannot parse String to Int: 42.5",
                    value = 42.5,
                )
            )
        )
    }
    
    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    protected open fun `test seats processor`(testCase: ProcessorTestCase<Int>) {
        runTestCases(listOf(testCase))
    }
}
