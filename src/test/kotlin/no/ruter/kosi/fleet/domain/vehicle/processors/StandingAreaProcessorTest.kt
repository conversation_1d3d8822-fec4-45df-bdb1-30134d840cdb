package no.ruter.kosi.fleet.domain.vehicle.processors

import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.domain.quality.VehicleFieldQuality
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessedField
import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessor
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.assertEquals

open class StandingAreaProcessorTest : BaseProcessorTest<StandingAreaProcessor>() {
    override val processor = StandingAreaProcessor()
    
    companion object {
        @JvmStatic
        fun testCases() = listOf(
            // happy cases
            ProcessorTestCase(
                name = "SL79 trikk should return fixed standing area",
                processorResults = mapOf(
                    ChassisNumberProcessor::class to ProcessedField(
                        originProcessor = ChassisNumberProcessor::class,
                        value = "SL790001",
                        quality = createQuality("SL790001")
                    ),
                    NumberOfPassengersSeatedProcessor::class to ProcessedField(
                        originProcessor = NumberOfPassengersSeatedProcessor::class,
                        value = 40,
                        quality = createQuality(40)
                    ),
                    VehicleLengthProcessor::class to ProcessedField(
                        originProcessor = VehicleLengthProcessor::class,
                        value = 18.0,
                        quality = createQuality(18.0)
                    )
                ),
                expectedValue = 18.7
            ),
            
            ProcessorTestCase(
                name = "SL95 trikk should return fixed standing area",
                processorResults = mapOf(
                    ChassisNumberProcessor::class to ProcessedField(
                        originProcessor = ChassisNumberProcessor::class,
                        value = "SL950001",
                        quality = createQuality("SL950001")
                    ),
                    NumberOfPassengersSeatedProcessor::class to ProcessedField(
                        originProcessor = NumberOfPassengersSeatedProcessor::class,
                        value = 40,
                        quality = createQuality(40)
                    ),
                    VehicleLengthProcessor::class to ProcessedField(
                        originProcessor = VehicleLengthProcessor::class,
                        value = 18.0,
                        quality = createQuality(18.0)
                    )
                ),
                expectedValue = 28.6
            ),
            
            ProcessorTestCase(
                name = "SL18 trikk should return fixed standing area",
                processorResults = mapOf(
                    ChassisNumberProcessor::class to ProcessedField(
                        originProcessor = ChassisNumberProcessor::class,
                        value = "SL180001",
                        quality = createQuality("SL180001")
                    ),
                    NumberOfPassengersSeatedProcessor::class to ProcessedField(
                        originProcessor = NumberOfPassengersSeatedProcessor::class,
                        value = 40,
                        quality = createQuality(40)
                    ),
                    VehicleLengthProcessor::class to ProcessedField(
                        originProcessor = VehicleLengthProcessor::class,
                        value = 18.0,
                        quality = createQuality(18.0)
                    )
                ),
                expectedValue = 42.0
            ),

            ProcessorTestCase(
                name = "unknown model should calculate standing area using formula",
                processorResults = mapOf(
                    ChassisNumberProcessor::class to ProcessedField(
                        originProcessor = ChassisNumberProcessor::class,
                        value = "UNKNOWN123",
                        quality = createQuality("UNKNOWN123")
                    ),
                    NumberOfPassengersSeatedProcessor::class to ProcessedField(
                        originProcessor = NumberOfPassengersSeatedProcessor::class,
                        value = 40,
                        quality = createQuality(40)
                    ),
                    VehicleLengthProcessor::class to ProcessedField(
                        originProcessor = VehicleLengthProcessor::class,
                        value = 18.0,
                        quality = createQuality(18.0)
                    )
                ),
                expectedValue = 16.67 // (18.0 * 1.539611185) + (-0.21831636 * 40) + (-2.311033554) ~= 16.67
            ),
            
            // Error cases
            ProcessorTestCase(
                name = "missing all dependencies should return error",
                processorResults = emptyMap(),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = StandingAreaProcessor::class,
                    message = "Missing vehicle length or seats"
                )
            ),
            
            ProcessorTestCase(
                name = "zero seats and length should return error",
                processorResults = mapOf(
                    ChassisNumberProcessor::class to ProcessedField(
                        originProcessor = ChassisNumberProcessor::class,
                        value = null,
                        quality = createQuality("UNKNOWN123")
                    ),
                    NumberOfPassengersSeatedProcessor::class to ProcessedField(
                        originProcessor = NumberOfPassengersSeatedProcessor::class,
                        value = 0,
                        quality = createQuality(0)
                    ),
                    VehicleLengthProcessor::class to ProcessedField(
                        originProcessor = VehicleLengthProcessor::class,
                        value = 0.0,
                        quality = createQuality(0.0)
                    )
                ),
                expectedError = ProcessingError.MissingRequiredData(
                    processor = StandingAreaProcessor::class,
                    message = "Missing vehicle length or seats"
                )
            )
        )
        
        private fun createQuality(value: Any) = when (value) {
            is String -> VehicleFieldQuality(value = value, type = QualityType.VALID)
            is Int -> VehicleFieldQuality(value = value, type = QualityType.VALID)
            is Double -> VehicleFieldQuality(value = value, type = QualityType.VALID)
            else -> throw IllegalArgumentException("Unsupported type: ${value::class.simpleName}")
        }
    }
    
    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    protected open fun `test standing area processor`(testCase: ProcessorTestCase<Int>) {
        runTestCases(listOf(testCase))
    }
}
