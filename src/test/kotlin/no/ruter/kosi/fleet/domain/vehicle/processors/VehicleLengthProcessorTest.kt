package no.ruter.kosi.fleet.domain.vehicle.processors

import no.ruter.kosi.fleet.domain.vehicle.processors.common.ProcessingError
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

open class VehicleLengthProcessorTest: BaseProcessorTest<VehicleLengthProcessor>() {
    override val processor = VehicleLengthProcessor()

    companion object{
        @JvmStatic
        fun testCases() = listOf(
            // Happy cases
            ProcessorTestCase(
                name =  "Long bus length from Autosys",
                input = mapOf("godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "18750"),
                expectedValue = 18.75,
                includeEvent = EventOptions(source = "AUTOSYS")

            ),
            ProcessorTestCase(
                name =  "Short bus length FRIDA",
                input =  mapOf("customAttr_Fartøy_lengde" to "24,7"),
                expectedValue = 24.7,
            ),
            ProcessorTestCase(
                name =  "Long length value from FRIDA",
                input =  mapOf("vehicleLength" to "21.5"),
                expectedValue = 21.5
            ),
            ProcessorTestCase(
                name =  "Short value from FRIDA",
                input =  mapOf("customAttr_Fordonslängd" to "34,166"),
                expectedValue = 34.166
            ),
            ProcessorTestCase(
                name =  "Short value from Autosys",
                input =  mapOf("godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "6069"),
                expectedValue = 6.069,
                includeEvent = EventOptions(source =  "AUTOSYS")
            ),
            // Negative cases
            ProcessorTestCase(
                name =  "Invalid length from Autosys - too short",
                input =  mapOf("godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "200"),
                expectedError = ProcessingError.InvalidFormat(
                    processor = VehicleLengthProcessor::class,
                    value = 200.0,
                    message = "Imported length 200.0 out of valid range (4.0 - 40.0 meters)"
                )
            ),
            ProcessorTestCase(
                name =  "Invalid length from Autosys - too long",
                input =  mapOf("godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "53659"),
                expectedError = ProcessingError.InvalidFormat(
                    processor = VehicleLengthProcessor::class,
                    value = 53659.0,
                    message = "Imported length 53659.0 out of valid range (4.0 - 40.0 meters)"
                )
            ),
            ProcessorTestCase(
                name =  "Invalid length from Frida - too short",
                input =  mapOf("vehicleLength" to "1.23"),
                expectedError = ProcessingError.InvalidFormat(
                    processor = VehicleLengthProcessor::class,
                    value = 1.23,
                    message = "Imported length 1.23 out of valid range (4.0 - 40.0 meters)"
                )
            ),
        )
    }


    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    protected open fun `test length processor`(testCase: ProcessorTestCase<String>){
        runTestCases(listOf(testCase))
    }
}