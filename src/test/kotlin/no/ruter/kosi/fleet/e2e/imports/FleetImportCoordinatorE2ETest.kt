package no.ruter.kosi.fleet.e2e.imports

import arrow.core.Either
import arrow.core.right
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.autosysclient.models.EnkeltOppslagKjoretoydata
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.EntityVehicleCapacityNpraDetails
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleCapacityRepository
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import no.ruter.kosi.fleet.integration.config.FleetImportIntegrationTestConfig
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import no.ruter.kosi.fleet.utils.TestDatabaseCleaner
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Timeout
import org.quartz.JobDataMap
import org.quartz.JobExecutionContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import io.zonky.test.db.postgres.embedded.EmbeddedPostgres
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.test.context.ActiveProfiles
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import javax.sql.DataSource
import no.ruter.kosi.fleet.domain.event.DataSource as FleetDataSource

@SpringBootTest(properties = ["spring.main.allow-bean-definition-overriding=true"])
@ActiveProfiles("test")
@Import(FleetImportIntegrationTestConfig::class, TestConfiguration::class)
class FleetImportCoordinatorE2ETest {
    @Autowired
    lateinit var completeImportJob: CompleteImportJob

    @Autowired
    lateinit var jobRepository: AuroraJobRepository

    @Autowired
    lateinit var jobService: JobService

    @Autowired
    lateinit var vehicleRepository: AuroraInternalVehicleRepository

    @MockkBean
    private lateinit var tokenProvider: TokenProvider

    @MockkBean
    private lateinit var vehicleCapacityRepository: AuroraVehicleCapacityRepository

    @MockkBean
    private lateinit var fridaApiService: FridaApiService

    @Autowired
    lateinit var testDatabaseCleaner: TestDatabaseCleaner

    @BeforeEach
    fun setupMocks() {
        testDatabaseCleaner.cleanDatabase()
        every { fridaApiService.fetchContracts() } returns Either.Right(emptyList())
        every { vehicleCapacityRepository.findAll() } returns emptyList()
    }

    @MockkBean(relaxed = true)
    lateinit var autosysApiService: AutosysApiService

    @MockkBean(relaxed = true)
    private lateinit var kafkaTemplate: KafkaTemplate<String, VehicleV3>

    @MockkBean(relaxed = true)
    private lateinit var vehicleCapacityKafkaTemplate: KafkaTemplate<String, EntityVehicleCapacityNpraDetails>

    @BeforeEach
    fun setup() {
        every { tokenProvider.getAccessToken() } returns "mock-access-token"
    }

    @Test
    @Timeout(30)
    fun `should perform full import and persist all vehicles and send them to kafka`() {
        val testJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = true,
                quartzJobId = "test-job-id",
                quartzJobGroup = "test-job-group",
            )

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", testJob.jobId)
                put("testMode", true)
            }
        val context =
            mockk<JobExecutionContext>(relaxed = true) {
                every { mergedJobDataMap } returns jobDataMap
            }

        val mockAutosysData = mockk<EnkeltOppslagKjoretoydata>(relaxed = true)
        every { mockAutosysData.kjennemerke } returns listOf(mockk(relaxed = true))

        every { autosysApiService.getVehicleData(any()) } returns mockAutosysData.right()

        val fridaVehicle = mockk<ApiVehicleV2Model>(relaxed = true)
        every { fridaVehicle.id } returns 12345
        every { fridaVehicle.chassiNumber } returns "TEST-VIN-123"

        every { fridaApiService.fetchVehicles(any(), any()) } returns listOf(fridaVehicle).right()

        every { kafkaTemplate.send(any(), any(), any()) } returns mockk(relaxed = true)
        every { vehicleCapacityKafkaTemplate.send(any(), any(), any()) } returns mockk(relaxed = true)

        val dummyVehicle =
            InternalVehicle(
                dataSource = FleetDataSource.FRIDA,
                sourceId = 123,
                vehicleRef = "ABC123",
            )
        vehicleRepository.save(dummyVehicle)

        completeImportJob.execute(context)

        println("Waiting for job to complete...")
        val jobCompleted = waitForJobCompletion(testJob.jobId, timeoutSeconds = 15)
        assertTrue(jobCompleted, "Job should complete within the timeout period")

        val completedJob = jobRepository.findById(testJob.jobId)
        assertNotNull(completedJob, "Job should still exist")
        assertEquals(FleetJob.JobStatus.COMPLETED, completedJob?.status, "Job should be marked COMPLETED")

        val childJobs = jobRepository.findByParentJobId(testJob.jobId)
        assertTrue(childJobs.isNotEmpty(), "Child jobs should be created")

        val fridaJob = childJobs.find { it.type == FleetJob.JobType.IMPORT_FRIDA_VEHICLES }
        val autosysJob = childJobs.find { it.type == FleetJob.JobType.IMPORT_AUTOSYS }
        val reconstructionJob = childJobs.find { it.type == FleetJob.JobType.VEHICLE_RECONSTRUCTION }
        val publishingKafkaJob = childJobs.find { it.type == FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA }
        val publishingSnowflakeJob = childJobs.find { it.type == FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA }

        assertNotNull(fridaJob, "Should have a FRIDA import job")
        assertNotNull(autosysJob, "Should have an AUTOSYS import job")
        assertNotNull(reconstructionJob, "Should have a VEHICLE_RECONSTRUCTION job")
        assertNotNull(publishingKafkaJob, "Should have a PUBLISHING job")
        assertNotNull(publishingSnowflakeJob, "Should have a PUBLISHING job")

        assertEquals(FleetJob.JobStatus.COMPLETED, fridaJob?.status, "FRIDA import job should be COMPLETED")
        assertEquals(FleetJob.JobStatus.COMPLETED, autosysJob?.status, "AUTOSYS import job should be COMPLETED")
        assertEquals(FleetJob.JobStatus.COMPLETED, reconstructionJob?.status, "VEHICLE_RECONSTRUCTION job should be COMPLETED")
        assertEquals(FleetJob.JobStatus.COMPLETED, publishingKafkaJob?.status, "PUBLISHING_KAFKA job should be COMPLETED")
        assertEquals(FleetJob.JobStatus.COMPLETED, publishingSnowflakeJob?.status, "PUBLISHING_SNOWFLAKE job should be COMPLETED")
    }

    private fun waitForJobCompletion(
        jobId: Int,
        timeoutSeconds: Long = 15,
    ): Boolean {
        val latch = CountDownLatch(1)
        val startTime = System.currentTimeMillis()
        val timeoutMs = timeoutSeconds * 1000

        Thread {
            try {
                var lastStatus = ""
                while (System.currentTimeMillis() - startTime < timeoutMs) {
                    val job = jobRepository.findById(jobId)
                    if (job != null) {
                        val currentStatus = "${job.status}, progress: ${job.progressPercentage}%"
                        if (currentStatus != lastStatus) {
                            println("Job status: $currentStatus")
                            lastStatus = currentStatus
                        }

                        if (job.isCompleted) {
                            println("Job completed with status: ${job.status}")
                            latch.countDown()
                            break
                        }
                    }
                    Thread.sleep(200)
                }

                if (latch.count != 0L) {
                    val finalJob = jobRepository.findById(jobId)
                    if (finalJob?.isCompleted == true) {
                        println("Job completed with status: ${finalJob.status}")
                        latch.countDown()
                    } else {
                        println("Job did not complete within the timeout. Current status: ${finalJob?.status ?: "Unknown"}")
                    }
                }
            } catch (e: Exception) {
                println("Error while waiting for job completion: ${e.message}")
                latch.countDown()
            }
        }.start()

        return latch.await(timeoutSeconds, TimeUnit.SECONDS)
    }

    // TODO unify this
    @TestConfiguration
    class OverridingDataSourceConfig {
        @Bean
        @Primary
        @Qualifier("snowflakeDataSource")
        fun snowflakeDataSource(): DataSource =
            EmbeddedPostgres.builder()
                .setPort(0)
                .start()
                .postgresDatabase

        @Bean
        @Primary
        @Qualifier("snowflakeVehicleRepositoryPort")
        fun mockSnowflakeVehicleRepository(): Any {
            return mockk(relaxed = true)
        }
    }
}
