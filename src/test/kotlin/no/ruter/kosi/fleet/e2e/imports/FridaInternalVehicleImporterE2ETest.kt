package no.ruter.kosi.fleet.e2e.imports

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.fleet.configuration.BaseIntegrationTest
import no.ruter.kosi.fleet.configuration.SCD2IntegrationTestConfig
import no.ruter.kosi.fleet.configuration.TestConfiguration
import no.ruter.kosi.fleet.configuration.TestDataSourceConfiguration
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.domain.vehicle.KafkaVehicleProducerService
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import no.ruter.kosi.fleet.infrastructure.quartz.FridaVehicleImportJob
import no.ruter.kosi.fleet.utils.TestDatabaseCleaner
import no.ruter.kosi.fridaclient.client.ApiResponse
import no.ruter.kosi.fridaclient.client.ApiVehicleVehiclesClient
import no.ruter.kosi.fridaclient.models.ApiListResponseModelOfApiVehicleV2Model
import okhttp3.Headers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.quartz.JobDataMap
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.JobKey
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import kotlin.test.assertTrue

@Import(SCD2IntegrationTestConfig::class, TestConfiguration::class, TestDataSourceConfiguration::class)
@SpringBootTest(properties = ["spring.main.allow-bean-definition-overriding=true"])
@ActiveProfiles("test")
class FridaInternalVehicleImporterE2ETest : BaseIntegrationTest() {

    @Autowired
    private lateinit var vehicleRepository: AuroraInternalVehicleRepository

    @Autowired
    private lateinit var eventRepository: AuroraEventRepository

    @Autowired
    private lateinit var eventDataRepository: AuroraEventDataRepository

    @Autowired
    private lateinit var vehicleSnapshotRepository: AuroraVehicleRepository

    @Autowired
    private lateinit var jobRepository: AuroraJobRepository

    @Autowired
    private lateinit var jobService: JobService

    @Autowired
    private lateinit var fridaVehicleImportJob: FridaVehicleImportJob

    @Autowired
    private lateinit var objectMapper: ObjectMapper
    @MockkBean
    private lateinit var vehiclesClient: ApiVehicleVehiclesClient

    @MockkBean
    private lateinit var tokenProvider: TokenProvider

    @MockkBean
    private lateinit var kafkaVehicleProducerService: KafkaVehicleProducerService

    @Autowired
    private lateinit var testDatabaseCleaner: TestDatabaseCleaner

    private lateinit var jobKey: JobKey
    private lateinit var jobDetail: JobDetail

    @BeforeEach
    fun setup() {
        testDatabaseCleaner.cleanDatabase()

        jobKey = mockk()
        every { jobKey.name } returns "test-job"
        jobDetail = mockk()
        every { jobDetail.key } returns jobKey

        every { tokenProvider.getAccessToken() } returns "mock-access-token"
        
        every { kafkaVehicleProducerService.sendMessage(any(), any()) } returns Either.Right(Unit)
    }

    @Test
    fun `50 vehicles should be persisted to the database`() {
        val apiResponse = readVehiclesFromJson("vehiclesvehicles.json")
        every { vehiclesClient.vehiclesVGetAllVehicles() } returns apiResponse
        every { vehiclesClient.vehiclesVGetAllVehicles(1, 0) } returns apiResponse

        val fridaJob = executeDirectFridaImport()

        assertEquals(FleetJob.JobStatus.COMPLETED, fridaJob.status, "Job should be completed")

        val savedVehicles = vehicleRepository.findAll()
        assertEquals(50, savedVehicles.size, "There should be 50 vehicles in the repository")
    }

    @Test
    fun `first and last vehicle should be correct`() {
        val apiResponse = readVehiclesFromJson("vehiclesvehicles.json")
        every { vehiclesClient.vehiclesVGetAllVehicles() } returns apiResponse
        every { vehiclesClient.vehiclesVGetAllVehicles(1, 0) } returns apiResponse

        executeDirectFridaImport()

        val savedVehicles = vehicleRepository.findAll()
        assertTrue(savedVehicles.firstOrNull { it.sourceId == 8981 } != null, "Vehicle with ID 8981 should exist")
        assertTrue(savedVehicles.lastOrNull { it.sourceId == 9412 } != null, "Vehicle with ID 9412 should exist")
    }

    @Test
    fun `should fetch vehicles and persist only the changes into the database`() {
        val apiResponse = readVehiclesFromJson("vehiclesvehicles.json")
        every { vehiclesClient.vehiclesVGetAllVehicles() } returns apiResponse
        every { vehiclesClient.vehiclesVGetAllVehicles(1, 0) } returns apiResponse

        executeDirectFridaImport()

        val newVehicle =
            apiResponse.data?.items?.first()
                ?: throw IllegalStateException("can't read vehicle from file")

        val newAdjustedVehicle =
            newVehicle.copy(
                planetTrafficPermit = 69,
                seatBelts = "0-punktsbelte",
            )

        val apiResponse2 =
            ApiResponse(
                headers = Headers.headersOf("mockheader", "na"),
                data = ApiListResponseModelOfApiVehicleV2Model(items = listOf(newAdjustedVehicle)),
                statusCode = 200,
            )
        every { vehiclesClient.vehiclesVGetAllVehicles() } returns apiResponse2
        every { vehiclesClient.vehiclesVGetAllVehicles(1, 0) } returns apiResponse2

        executeDirectFridaImport()

        val vehicles = vehicleRepository.findAll()
        val events = eventRepository.findAll()
        assertEquals(50, vehicles.size, "There should still be 50 vehicles")
        assertEquals(51, events.size, "There should be 51 events (50 original + 1 update)")

        val eventData = eventDataRepository.findLatestByVehicleRef("fleetref_8981")

        assertTrue(
            eventData.any { it.fieldName == "planetTrafficPermit" && it.fieldValue == "69" },
            "Should find planetTrafficPermit with value 69",
        )
        assertTrue(
            eventData.any { it.fieldName == "seatBelts" && it.fieldValue == "0-punktsbelte" },
            "Should find seatBelts with value 0-punktsbelte",
        )
    }

    @Test
    fun `should publish snapshots to Kafka and store to Snowflake after import`() {
        val apiResponse = readVehiclesFromJson("vehiclesvehicles.json")
        every { vehiclesClient.vehiclesVGetAllVehicles() } returns apiResponse
        every { vehiclesClient.vehiclesVGetAllVehicles(1, 0) } returns apiResponse

        executeDirectFridaImport()

        val publishToKafkaJob =
            jobService.createJob(
                type = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
                manual = true,
                quartzJobId = "test-publish-job",
                quartzJobGroup = "test-group",
            )

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", publishToKafkaJob.jobId)
            }
        val mockContext = mockk<JobExecutionContext>()
        every { mockContext.mergedJobDataMap } returns jobDataMap

        val savedSnapshots = vehicleSnapshotRepository.findAll()
        val snapshotMap = savedSnapshots.associateBy { snapshot ->
            snapshot.identification.chassisNumber
        }

        snapshotMap.forEach { (_, snapshot) ->
            verify {
                kafkaVehicleProducerService.sendMessage(
                    key = snapshot.vehicleRef,
                    message = any<VehicleV3>(),
                )
            }
            vehicleSnapshotRepository.save(snapshot)
        }

        // TOOD test snowflake storage
    }

    private fun executeDirectFridaImport(): FleetJob {
        val fridaFleetJob =
            jobService.createJob(
                type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                manual = true,
                quartzJobId = "test-frida-job",
                quartzJobGroup = "test-group",
            )

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", fridaFleetJob.jobId)
            }
        val jobKey = mockk<JobKey>()
        every { jobKey.name } returns "test-frida-job"
        val jobDetail = mockk<JobDetail>()
        every { jobDetail.key } returns jobKey
        val mockContext = mockk<JobExecutionContext>()
        every { mockContext.mergedJobDataMap } returns jobDataMap
        every { mockContext.jobDetail } returns jobDetail
        every { mockContext.result } returns null

        fridaVehicleImportJob.execute(mockContext)

        return jobRepository.findById(fridaFleetJob.jobId) ?: throw IllegalStateException("can't find job")
    }

    private fun readVehiclesFromJson(fileName: String): ApiResponse<ApiListResponseModelOfApiVehicleV2Model> {
        val fileContent =
            this::class.java.classLoader
                .getResource(fileName)
                ?.readText()
                ?: throw IllegalArgumentException("File $fileName not found in resources")
        return objectMapper.readValue(fileContent)
    }
}