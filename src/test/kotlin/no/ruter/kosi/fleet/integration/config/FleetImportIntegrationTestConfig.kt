package no.ruter.kosi.fleet.integration.config

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.mockk.mockk
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.quarantine.QuarantineService
import no.ruter.kosi.fleet.domain.vehicle.VehicleReconstructionService
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessorRegistry
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import no.ruter.kosi.fleet.utils.generators.createExpectedVehicleQuality
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Instant

@Configuration
class FleetImportIntegrationTestConfig {
    @Bean
    fun vehicleReconstructionService(
        internalVehicleRepository: AuroraInternalVehicleRepository,
        vehicleRepository: AuroraVehicleRepository,
        eventDataRepository: AuroraEventDataRepository,
        quarantineService: QuarantineService,
        fieldProcessorRegistry: VehicleFieldProcessorRegistry,
    ): VehicleReconstructionService = MockVehicleReconstructionService(
        internalVehicleRepository,
        vehicleRepository,
        eventDataRepository,
        fieldProcessorRegistry,
        quarantineService
    )

    class MockVehicleReconstructionService(
        internalVehicleRepository: AuroraInternalVehicleRepository,
        vehicleRepository: AuroraVehicleRepository,
        eventDataRepository: AuroraEventDataRepository,
        fieldProcessorRegistry: VehicleFieldProcessorRegistry,
        quarantineService: QuarantineService,
    ) : VehicleReconstructionService(
        internalVehicleRepository = internalVehicleRepository,
        eventDataRepository = eventDataRepository,
        quarantineService = quarantineService,
        snapshotRepository = vehicleRepository,
        fieldProcessorRegistry = fieldProcessorRegistry,
        objectMapper = jacksonObjectMapper()
        ) {
        override fun reconstructVehicle(
            vehicleId: Int,
            timestamp: Instant?,
        ): Pair<VehicleDto, VehicleQualityDto> = createExpectedVehicleQuality()

        override fun reconstructVehicle(
            vehicleRef: String,
            timestamp: Instant?,
        ): Pair<VehicleDto, VehicleQualityDto> = createExpectedVehicleQuality()

        override fun reconstructVehicle(
            vehicle: InternalVehicle,
            timestamp: Instant?,
        ): Pair<VehicleDto, VehicleQualityDto>? = createExpectedVehicleQuality()
    }
}
