package no.ruter.kosi.fleet.integration.repository

import no.ruter.kosi.fleet.domain.event.DataSource as FleetDataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssue
import no.ruter.kosi.fleet.domain.eventdata.EventDataQualityIssueRepositoryPort
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.quality.QualityType
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataQualityIssueRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataInternalVehicleRepository
import no.ruter.kosi.fleet.configuration.BaseIntegrationTest
import no.ruter.kosi.fleet.configuration.TestDataSourceConfiguration
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataJobRepository
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageRequest
import java.time.Instant

@SpringBootTest
@Import(TestDataSourceConfiguration::class)
class EventDataQualityIssueRepositorySCD2Test : BaseIntegrationTest() {

    @Autowired
    private lateinit var repository: EventDataQualityIssueRepositoryPort

    @Autowired
    private lateinit var springDataRepository: SpringDataEventDataQualityIssueRepository

    @Autowired
    private lateinit var eventDataRepository: AuroraEventDataRepository

    @Autowired
    private lateinit var springDataEventDataRepository: SpringDataEventDataRepository

    @Autowired
    private lateinit var springDataEventRepository: SpringDataEventRepository

    @Autowired
    private lateinit var springDataInternalVehicleRepository: SpringDataInternalVehicleRepository

    @Autowired
    private lateinit var springDataJobRepository: SpringDataJobRepository

    @BeforeEach
    fun setUp() {
        springDataRepository.deleteAll()
        springDataEventDataRepository.deleteAll()
        springDataEventRepository.deleteAll()
        springDataInternalVehicleRepository.deleteAll()
        springDataJobRepository.deleteAll()
    }

    @Test
    fun `should save and retrieve event data quality issue`() {
        val result = createTestEventDataAndIssue()
        val eventData = result.eventData
        val issue = result.qualityIssue

        val savedIssue = repository.save(issue)
        val foundIssue = repository.findById(savedIssue.id!!)

        assertTrue(foundIssue != null)
        foundIssue?.let {
            assertEquals(eventData.id, it.eventDataId)
            assertEquals(issue.fieldName, it.fieldName)
            assertEquals(issue.actualValue, it.actualValue)
            assertEquals(issue.expectedValue, it.expectedValue)
            assertEquals(issue.type, it.type)
            assertTrue(it.isCurrent)
            assertNotNull(it.effectiveFrom)
            assertNull(it.effectiveTo)
            assertTrue(it.businessKey.isNotBlank())
        }
    }

    @Test
    fun `should create new version when updating with different business data`() {
        val result = createTestEventDataAndIssue()
        val eventData = result.eventData
        val issue = result.qualityIssue

        val original = repository.save(issue)

        val updated = repository.save(
            EventDataQualityIssue.createNewVersion(
                previous = original,
                newActualValue = "newValue",
                newType = no.ruter.kosi.fleet.domain.quality.QualityType.INVALID_VALUE
            )
        )

        val originalFromDb = repository.findById(original.id!!)
        val allVersions = springDataRepository.findByBusinessKeyOrderByEffectiveFrom(original.businessKey)

        assertAll(
            { assertNotNull(originalFromDb) },
            { assertFalse(originalFromDb!!.isCurrent) },
            { assertNotNull(originalFromDb?.effectiveTo) },
            { assertTrue(updated.isCurrent) },
            { assertNull(updated.effectiveTo) },
            { assertNotEquals(original.id, updated.id) },
            { assertEquals(original.businessKey, updated.businessKey) },
            { assertEquals(2, allVersions.size) },
            { assertEquals(original.id, allVersions[0].id) },
            { assertEquals(updated.id, allVersions[1].id) },
            { assertTrue(updated.effectiveFrom.isAfter(original.effectiveFrom)) }
        )
    }

    @Test
    fun `should find current version by business key`() {
        val result1 = createTestEventDataAndIssue(fieldName = "field1", type = QualityType.MISSING_VALUE)
        val eventData1 = result1.eventData
        val issue1 = result1.qualityIssue

        val result2 = createTestEventDataAndIssue(fieldName = "field2", type = QualityType.MISSING_VALUE)
        val eventData2 = result2.eventData
        val issue2 = result2.qualityIssue

        val businessKey1 = "test-business-key-1-${Instant.now().toEpochMilli()}"
        val businessKey2 = "test-business-key-2-${Instant.now().toEpochMilli()}"
        issue1.businessKey = businessKey1
        issue2.businessKey = businessKey2

        val savedIssue1 = repository.save(issue1)
        val savedIssue2 = repository.save(issue2)

        val updatedIssue = EventDataQualityIssue.createNewVersion(
            previous = savedIssue1,
            newActualValue = "updatedValue",
            businessKey = businessKey1
        )

        val savedUpdatedIssue = repository.save(updatedIssue)

        val oldVersion = repository.findById(savedIssue1.id!!)
        assertNotNull(oldVersion)
        assertFalse(oldVersion!!.isCurrent)

        val currentIssue1 = repository.findById(savedUpdatedIssue.id!!)
        val currentIssue2 = repository.findById(savedIssue2.id!!)

        assertAll(
            { assertNotNull(currentIssue1) },
            { assertNotNull(currentIssue2) },
            { assertEquals("updatedValue", currentIssue1?.actualValue) },
            { assertEquals(issue2.actualValue, currentIssue2?.actualValue) },
            { assertEquals(savedUpdatedIssue.id, currentIssue1?.id) },
            { assertEquals(savedIssue2.id, currentIssue2?.id) },
            { assertTrue(currentIssue1!!.isCurrent) },
            { assertTrue(currentIssue2!!.isCurrent) }
        )
    }

    @Test
    fun `should find issues by event data id`() {
        val result1 = createTestEventDataAndIssue(fieldName = "field1")
        val eventData1 = result1.eventData
        val issue1 = result1.qualityIssue

        val issue2 = EventDataQualityIssue(
            eventDataId = eventData1.id,
            fieldName = "field2",
            actualValue = "testValue",
            expectedValue = "expectedValue",
            type = QualityType.MISSING_VALUE,
            timestamp = Instant.now(),
            businessKey = "${eventData1.id}-field2-${QualityType.MISSING_VALUE.name}"
        )

        val result3 = createTestEventDataAndIssue(fieldName = "field1")
        val eventData3 = result3.eventData
        val issue3 = result3.qualityIssue

        repository.save(issue1)
        repository.save(issue2)
        repository.save(issue3)

        val issues = repository.findByEventDataId(eventData1.id)

        assertEquals(2, issues.size)
        assertTrue(issues.all { it.eventDataId == eventData1.id })
    }

    @Test
    fun `should find current issues by event data id`() {
        val result1 = createTestEventDataAndIssue(fieldName = "field1")
        val eventData1 = result1.eventData
        val issue1 = result1.qualityIssue

        val issue2 = EventDataQualityIssue(
            eventDataId = eventData1.id,
            fieldName = "field2",
            actualValue = "testValue",
            expectedValue = "expectedValue",
            type = QualityType.MISSING_VALUE,
            timestamp = Instant.now(),
            businessKey = "${eventData1.id}-field2-${QualityType.MISSING_VALUE.name}"
        )

        val savedIssue1 = repository.save(issue1)
        repository.save(issue2)

        repository.save(
            EventDataQualityIssue.createNewVersion(
                previous = savedIssue1,
                newActualValue = "updatedValue"
            )
        )

        val currentIssues = repository.findCurrentByEventDataId(eventData1.id)

        assertEquals(2, currentIssues.size)
        assertTrue(currentIssues.all { it.isCurrent })
        assertTrue(currentIssues.any { it.actualValue == "updatedValue" })
    }

    @Test
    fun `should find current issues by multiple event data ids`() {
        val result1 = createTestEventDataAndIssue(fieldName = "field1")
        val eventData1 = result1.eventData
        val issue1 = result1.qualityIssue

        val result2 = createTestEventDataAndIssue(fieldName = "field2")
        val eventData2 = result2.eventData
        val issue2 = result2.qualityIssue

        val result3 = createTestEventDataAndIssue(fieldName = "field3")
        val eventData3 = result3.eventData
        val issue3 = result3.qualityIssue

        repository.save(issue1)
        repository.save(issue2)
        repository.save(issue3)

        val eventDataIds = listOf(eventData1.id, eventData2.id, eventData3.id)

        val currentIssues = repository.findCurrentByEventDataIds(eventDataIds.take(2))

        assertEquals(2, currentIssues.size)
        assertTrue(currentIssues.all { it.eventDataId in eventDataIds.take(2) })
        assertTrue(currentIssues.all { it.isCurrent })
    }

    @Test
    fun `should find issues with filters`() {
        val result1 = createTestEventDataAndIssue(fieldName = "field1", type = QualityType.INVALID_VALUE)
        val eventData1 = result1.eventData
        val issue1 = result1.qualityIssue

        val issue2 = EventDataQualityIssue(
            eventDataId = eventData1.id,
            fieldName = "field2",
            actualValue = "testValue",
            expectedValue = "expectedValue",
            type = QualityType.MISSING_VALUE,
            timestamp = Instant.now(),
            businessKey = "${eventData1.id}-field2-${QualityType.MISSING_VALUE.name}"
        )

        val result3 = createTestEventDataAndIssue(fieldName = "field1", type = QualityType.INVALID_VALUE)
        val eventData3 = result3.eventData
        val issue3 = result3.qualityIssue

        repository.save(issue1)
        repository.save(issue2)
        repository.save(issue3)

        val page1 = repository.findWithFilters(
            eventDataId = eventData1.id,
            fieldName = null,
            type = "INVALID_VALUE",
            isCurrent = true,
            pageable = PageRequest.of(0, 10)
        )

        assertEquals(1, page1.totalElements)
        assertEquals(1, page1.content.size)
        assertEquals("field1", page1.content[0].fieldName)
        assertEquals(QualityType.INVALID_VALUE, page1.content[0].type)
    }

    @Test
    fun `should create and use both eventdata and eventdataqualityissue`() {
        val result = createTestEventDataAndIssue(fieldName = "testField", type = QualityType.MISSING_VALUE)
        val eventData = result.eventData
        val qualityIssue = result.qualityIssue

        val savedIssue = repository.save(qualityIssue)

        assertEquals(eventData.id, savedIssue.eventDataId)
        assertEquals(eventData.fieldName, savedIssue.fieldName)

        val issues = repository.findByEventDataId(eventData.id)

        assertEquals(1, issues.size)
        assertEquals(eventData.id, issues[0].eventDataId)
        assertEquals(eventData.fieldName, issues[0].fieldName)
        assertEquals(qualityIssue.type, issues[0].type)
    }

    data class EventDataWithQualityIssue(
        val eventData: EventData,
        val qualityIssue: EventDataQualityIssue
    )

    private fun createTestEventDataAndIssue(
        eventDataId: Int? = null,
        fieldName: String = "testField",
        type: QualityType = QualityType.MISSING_VALUE
    ): EventDataWithQualityIssue {
        val dummyJob = FleetJob(
            type = FleetJob.JobType.COMPLETE_IMPORT,
            manual = true,
            quartzJobId = "dummyJob",
            quartzJobGroup = "dummyGroup",
        )

        val dummyVehicle = InternalVehicle(
            dataSource = FleetDataSource.FRIDA,
            sourceId = 1,
            vehicleRef = "TEST-VEHICLE",
        )

        val savedVehicle = springDataInternalVehicleRepository.save(dummyVehicle)
        val savedJob = springDataJobRepository.save(dummyJob)

        val dummyEvent = Event.create(
            vehicle = savedVehicle,
            job = savedJob,
            source = "TEST",
            priority = 1,
        )

        val savedEvent = springDataEventRepository.save(dummyEvent)

        val eventData = EventData(
            event = savedEvent,
            fieldName = fieldName,
            fieldValue = "testValue",
            timestamp = Instant.now(),
            businessKey = "${savedVehicle.vehicleRef}-$fieldName"
        )

        val savedEventData = eventDataRepository.save(eventData)

        val qualityIssue = EventDataQualityIssue(
            eventDataId = eventDataId ?: savedEventData.id,
            fieldName = fieldName,
            actualValue = "testValue",
            expectedValue = "expectedValue",
            type = type,
            timestamp = Instant.now(),
            businessKey = "${savedEventData.id}-$fieldName-${type.name}"
        )

        return EventDataWithQualityIssue(savedEventData, qualityIssue)
    }

    private fun createTestIssue(
        eventDataId: Int = 1,
        fieldName: String = "testField",
        type: QualityType = QualityType.MISSING_VALUE
    ): EventDataQualityIssue {
        val result = createTestEventDataAndIssue(eventDataId, fieldName, type)
        return result.qualityIssue
    }
}
