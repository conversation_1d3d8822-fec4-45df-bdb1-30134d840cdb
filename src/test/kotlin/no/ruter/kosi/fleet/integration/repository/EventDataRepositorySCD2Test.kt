package no.ruter.kosi.fleet.integration.repository

import com.ninjasquad.springmockk.MockkBean
import no.ruter.kosi.fleet.configuration.BaseIntegrationTest
import no.ruter.kosi.fleet.configuration.TestDataSourceConfiguration
import no.ruter.kosi.fleet.domain.event.DataSource as FleetDataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataJobRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeContractRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleCapacityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType
import java.time.Instant
import javax.sql.DataSource

@SpringBootTest
@Import(TestDataSourceConfiguration::class)
class EventDataRepositorySCD2Test : BaseIntegrationTest() {
    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleRepository: SnowflakeVehicleRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleCapacityRepository: SnowflakeVehicleCapacityRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleQualityRepository: SnowflakeVehicleQualityRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeContractRepository: SnowflakeContractRepository

    @Autowired
    lateinit var eventDataRepository: AuroraEventDataRepository

    @Autowired
    lateinit var springDataEventDataRepository: SpringDataEventDataRepository

    @Autowired
    lateinit var springDataEventRepository: SpringDataEventRepository

    @Autowired
    lateinit var springDataInternalVehicleRepository: SpringDataInternalVehicleRepository

    @Autowired
    lateinit var springDataJobRepository: SpringDataJobRepository

    @BeforeEach
    fun setup() {
        springDataEventDataRepository.deleteAll()
        springDataEventRepository.deleteAll()
        springDataInternalVehicleRepository.deleteAll()
        springDataJobRepository.deleteAll()
    }

    @Test
    fun `repository should correctly persist version chain`() {
        val dummyJob =
            FleetJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = true,
                quartzJobId = "dummyJob",
                quartzJobGroup = "dummyGroup",
            )

        val dummyVehicle =
            InternalVehicle(
                dataSource = FleetDataSource.FRIDA,
                sourceId = 1,
                vehicleRef = "TEST-VEHICLE",
            )

        val dummyEvent =
            Event.create(
                vehicle = springDataInternalVehicleRepository.save(dummyVehicle),
                job = springDataJobRepository.save(dummyJob),
                source = "TEST",
                priority = 1,
            )

        val initialEffectiveFrom = Instant.now()
        val eventDataV1 =
            EventData(
                event = springDataEventRepository.save(dummyEvent),
                fieldName = "testField",
                fieldValue = "value1",
                timestamp = initialEffectiveFrom,
                businessKey = "${dummyEvent.vehicle.vehicleRef}-testField"
            )

        val savedV1 = springDataEventDataRepository.save(eventDataV1)

        Thread.sleep(10)
        val secondEffectiveFrom = Instant.now()

        val eventDataV2 =
            EventData.createNewVersion(
                previous = savedV1,
                newValue = "value2",
                businessKey = "${dummyEvent.vehicle.vehicleRef}-testField"
            )

        eventDataRepository.save(eventDataV2)

        val businessKey = "${dummyEvent.vehicle.vehicleRef}-testField"
        val versions = springDataEventDataRepository.findAllVersionsByBusinessKey(businessKey)
        assertEquals(2, versions.size, "There should be 2 versions persisted for the business key")

        val sortedVersions = versions.sortedBy { it.effectiveFrom }
        val firstVersion = sortedVersions[0]
        val secondVersion = sortedVersions[1]

        assertFalse(firstVersion.isCurrent, "The first version should not be isCurrent after an update")
        assertNotNull(firstVersion.effectiveTo, "The first version must have an effectiveTo timestamp set")
        assertEquals(
            secondVersion.effectiveFrom,
            firstVersion.effectiveTo,
            "The first version's effectiveTo should match the second version's effectiveFrom",
        )

        assertTrue(secondVersion.isCurrent, "The new version should be marked as isCurrent")
        assertNull(secondVersion.effectiveTo, "The isCurrent version should not have an effectiveTo value")
    }

    // Data source configuration is handled by TestDataSourceConfiguration
}
