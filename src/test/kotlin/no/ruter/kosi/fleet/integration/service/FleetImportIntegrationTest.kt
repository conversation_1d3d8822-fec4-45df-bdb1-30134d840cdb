package no.ruter.kosi.fleet.integration.service

import arrow.core.right
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import no.ruter.avro.entity.vehicle.details.v2.VehicleV3
import no.ruter.kosi.autosysclient.models.EnkeltOppslagKjoretoydata
import no.ruter.kosi.fleet.domain.vehicle.capacity.entities.EntityVehicleCapacityNpraDetails
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessorRegistry
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.configuration.TokenProvider
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeContractRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleCapacityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleRepository
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import no.ruter.kosi.fleet.integration.config.FleetImportIntegrationTestConfig
import no.ruter.kosi.fleet.utils.TestDatabaseCleaner
import no.ruter.kosi.fridaclient.models.ApiContractV3Model
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.quartz.JobDataMap
import org.quartz.JobExecutionContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import io.zonky.test.db.postgres.embedded.EmbeddedPostgres
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.test.context.ActiveProfiles
import no.ruter.kosi.fleet.domain.event.DataSource as FleetDataSource

@SpringBootTest(properties = ["spring.main.allow-bean-definition-overriding=true"])
@ActiveProfiles("test")
@Import(FleetImportIntegrationTestConfig::class)
class FleetImportIntegrationTest {
    @Autowired
    lateinit var completeImportJob: CompleteImportJob

    @Autowired
    lateinit var jobRepository: AuroraJobRepository

    @Autowired
    lateinit var jobService: JobService

    @Autowired
    lateinit var vehicleRepository: AuroraInternalVehicleRepository

    @Autowired
    lateinit var eventRepository: AuroraEventRepository

    @Autowired
    lateinit var testDatabaseCleaner: TestDatabaseCleaner

    @MockkBean
    private lateinit var tokenProvider: TokenProvider

    @MockkBean
    lateinit var fridaApiService: FridaApiService

    @MockkBean
    lateinit var autosysApiService: AutosysApiService

    @MockkBean(relaxed = true)
    private lateinit var kafkaTemplate: KafkaTemplate<String, VehicleV3>

    @MockkBean(relaxed = true)
    private lateinit var vehicleCapacityKafkaTemplate: KafkaTemplate<String, EntityVehicleCapacityNpraDetails>

    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleRepository: SnowflakeVehicleRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleCapacityRepository: SnowflakeVehicleCapacityRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeVehicleQualityRepository: SnowflakeVehicleQualityRepository

    @MockkBean(relaxed = true)
    private lateinit var snowflakeContractRepository: SnowflakeContractRepository

    @BeforeEach
    fun setup() {
        every { tokenProvider.getAccessToken() } returns "mock-access-token"

        testDatabaseCleaner.cleanDatabase()
    }

    // this has errors and doesn't actually succeed, should rewrite/fix it
    @Test
    fun `should import data from all sources and publish to kafka`() {
        val testJob =
            jobService.createJob(
                type = FleetJob.JobType.COMPLETE_IMPORT,
                manual = true,
                quartzJobId = "test-job-id",
                quartzJobGroup = "test-job-group",
            )

        val jobDataMap =
            JobDataMap().apply {
                put("jobId", testJob.jobId)
                put("testMode", true)
            }
        val context =
            mockk<JobExecutionContext>(relaxed = true) {
                every { mergedJobDataMap } returns jobDataMap
            }

        every { autosysApiService.getVehicleData(any()) } returns mockk<EnkeltOppslagKjoretoydata>(relaxed = true).right()
        every { fridaApiService.fetchVehicles(any(), any()) } returns listOf(mockk<ApiVehicleV2Model>(relaxed = true)).right()
        every { fridaApiService.fetchContracts() } returns listOf(mockk<ApiContractV3Model>(relaxed = true)).right()

        every { kafkaTemplate.send(any(), any(), any()) } returns mockk(relaxed = true)
        every { vehicleCapacityKafkaTemplate.send(any(), any(), any()) } returns mockk(relaxed = true)

        val dummyVehicle =
            InternalVehicle(
                dataSource = FleetDataSource.FRIDA,
                sourceId = 123,
                vehicleRef = "ABC123",
            )
        vehicleRepository.save(dummyVehicle)

        completeImportJob.execute(context)

        val jobCompleted = waitForJobCompletion(testJob.jobId)
        val allJobs = jobRepository.findAll()
        println(allJobs)
        assertTrue(jobCompleted, "Job should complete within the timeout period")

        val completedJob = jobRepository.findById(testJob.jobId)
        assertNotNull(completedJob, "Job should still exist")
        assertEquals(FleetJob.JobStatus.COMPLETED, completedJob?.status, "Job should be marked COMPLETED")

        val childJobs = jobRepository.findByParentJobId(testJob.jobId)
        assertTrue(childJobs.isNotEmpty(), "Child jobs should be created")

        childJobs.forEach { childJob ->
            assertEquals(FleetJob.JobStatus.COMPLETED, childJob.status, "Child job ${childJob.type} should be COMPLETED")
        }
    }

    private fun waitForJobCompletion(
        jobId: Int,
        timeoutSeconds: Long = 30,
    ): Boolean {
        val startTime = System.currentTimeMillis()
        val timeoutMs = timeoutSeconds * 1000

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val job = jobRepository.findById(jobId)
            if (job != null) {
                println("Current job status: ${job.status}, progress: ${job.progressPercentage}%")
                if (job.isCompleted) {
                    println("Job completed with status: ${job.status}")
                    return true
                }
            }
            Thread.sleep(500)
        }
        println("Job did not complete within timeout period")
        return false
    }

    // TODO unify this
    @TestConfiguration
    class OverridingDataSourceConfig {
        @Bean
        @Primary
        @Qualifier("snowflakeDataSource")
        fun snowflakeDataSource(): javax.sql.DataSource =
            EmbeddedPostgres.builder()
                .setPort(0)
                .start()
                .postgresDatabase



    }
}
