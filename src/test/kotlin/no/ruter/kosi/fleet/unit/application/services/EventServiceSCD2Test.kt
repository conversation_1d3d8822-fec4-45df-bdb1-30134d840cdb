package no.ruter.kosi.fleet.unit.application.services

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventServiceSCD2Test {
    private val jobRepository = mockk<AuroraJobRepository>()
    private val eventDataRepository = mockk<AuroraEventDataRepository>()
    private val eventRepository = mockk<AuroraEventRepository>()
    private val mockJpaRepository = mockk<SpringDataEventDataRepository>()

    private val eventDataSCD2Service = SCD2Service<EventData, Int, String>(mockJpaRepository)

    private lateinit var eventService: EventService
    private lateinit var vehicle: InternalVehicle
    private lateinit var testJob: FleetJob

    @BeforeEach
    fun setup() {
        eventService =
            EventService(
                jobRepository = jobRepository,
                eventDataRepository = eventDataRepository,
                eventRepository = eventRepository,
                eventDataSCD2Service = eventDataSCD2Service,
            )

        vehicle = mockk<InternalVehicle>()
        testJob = mockk<FleetJob>()
        every { testJob.jobId } returns 1

        every { vehicle.vehicleId } returns 123
        every { vehicle.sourceId } returns 456
        every { vehicle.vehicleRef } returns "TEST-REF-789"

        every { eventRepository.save(any()) } answers { firstArg() }
        every { jobRepository.findById(any()) } returns testJob
    }

    @Test
    fun `batchStoreChangedFields should use SCD2 versioning when saving fields`() {
        val extractedFields =
            mapOf(
                "chassiNumber" to "TEST-REF-789",
                "wheelChairPlaces" to 2,
            )

        every { eventDataRepository.findByVehicleId(any()) } returns emptyList()
        every { eventDataSCD2Service.findCurrentByBusinessKey(any()) } returns null
        every { mockJpaRepository.findCurrentByBusinessKeys(any()) } returns emptyList()

        val savedEventSlot = slot<List<Event>>()
        every { eventRepository.saveAll(capture(savedEventSlot)) } answers { savedEventSlot.captured }

        val savedEventDataSlot = slot<List<EventData>>()
        every { eventDataRepository.saveAll(capture(savedEventDataSlot)) } answers { savedEventDataSlot.captured }

        every { mockJpaRepository.saveAll(capture(savedEventDataSlot)) } answers { savedEventDataSlot.captured }

        eventService.batchStoreChangedFields(
            vehiclesWithFields = listOf(vehicle to extractedFields),
            dataSource = DataSource.FRIDA,
            job = testJob,
        )

        verify(exactly = 1) { eventRepository.saveAll(any()) }

        val savedEventData = savedEventDataSlot.captured
        savedEventData.forEach { savedEventData ->
            assertTrue(savedEventData.isCurrent, "Saved EventData should be marked as isCurrent")
            assertNotNull(savedEventData.effectiveFrom, "EffectiveFrom should be set")
            assertNull(savedEventData.effectiveTo, "EffectiveTo should be null for isCurrent version")
        }
    }

    @Test
    fun `changing field value should create new version and close old version`() {
        val businessKey = "${vehicle.vehicleRef!!}-wheelChairPlaces"

        val oldData =
            EventData(
                event = Event.create(vehicle = vehicle, source = "FRIDA", priority = 1, job = testJob),
                fieldName = "wheelChairPlaces",
                fieldValue = "1",
                businessKey = businessKey,
            ).also {
                it.effectiveFrom = Instant.now().minus(1, ChronoUnit.DAYS)
                it.isCurrent = true
            }

        every { mockJpaRepository.findCurrentByBusinessKey(businessKey) } returns oldData

        val savedEventDataSlot = slot<EventData>()
        every { mockJpaRepository.save(capture(savedEventDataSlot)) } answers { savedEventDataSlot.captured }

        val newData =
            EventData(
                event = Event.create(vehicle = vehicle, source = "FRIDA", priority = 1, job = testJob),
                fieldName = "wheelChairPlaces",
                fieldValue = "2",
                businessKey = businessKey,
            )

        val result = eventDataSCD2Service.saveNewVersion(newData)

        assertFalse(oldData.isCurrent, "Old version should be marked as not isCurrent")
        assertEquals(
            newData.effectiveFrom,
            oldData.effectiveTo,
            "Old version's effectiveTo should be set to the new version's timestamp",
        )

        assertTrue(result.isCurrent, "New version should be marked as isCurrent")
        assertNull(result.effectiveTo, "New version's effectiveTo should be null")
        assertEquals(oldData.effectiveTo, result.effectiveFrom, "New version's effectiveFrom should be set")
        assertEquals("2", result.fieldValue, "New version should have the updated value")
    }
}
