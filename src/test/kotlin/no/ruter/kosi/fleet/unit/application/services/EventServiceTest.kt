package no.ruter.kosi.fleet.unit.application.services

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertFalse
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Instant

class EventServiceTest {
    private val vehicleRepository = mockk<AuroraInternalVehicleRepository>()
    private val eventDataRepository = mockk<AuroraEventDataRepository>()
    private val eventRepository = mockk<AuroraEventRepository>()
    private val vehicleService = mockk<VehicleService>()
    private val vehicleSnapshotRepository = mockk<AuroraVehicleRepository>()
    private val jobRepository = mockk<AuroraJobRepository>()

    private val eventDataSCD2Service = mockk<SCD2Service<EventData, Int, String>>()

    private lateinit var eventService: EventService

    @BeforeEach
    fun setup() {
        eventService =
            EventService(
                jobRepository = jobRepository,
                eventDataRepository = eventDataRepository,
                eventRepository = eventRepository,
                eventDataSCD2Service = eventDataSCD2Service,
            )
    }

    @Test
    fun `storeChangedFieldsFromVehicle - no new event when no fields changed`() {
        val vehicleId = 100
        val existingInternalVehicle =
            InternalVehicle(
                vehicleId = 1,
                dataSource = DataSource.FRIDA,
                sourceId = vehicleId,
                vehicleRef = "YV3R8R324A1139756",
            )
        val testImport =
            FleetJob(
                jobId = 123,
                type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                quartzJobId = "dummyId",
                quartzJobGroup = "dummyGroup",
            )

        every { vehicleRepository.findBySourceId(vehicleId) } returns existingInternalVehicle

        every { eventDataRepository.findByVehicleId(existingInternalVehicle.vehicleId) } returns
            listOf(
                EventData(
                    id = 99,
                    event =
                        Event.create(
                            vehicle = existingInternalVehicle,
                            source = DataSource.FRIDA.name,
                            priority = 1,
                            job = testImport,
                        ),
                    fieldName = "chassiNumber",
                    fieldValue = "YV3R8R324A1139756",
                    businessKey = "${existingInternalVehicle.vehicleRef}-chassiNumber",
                ),
                EventData(
                    id = 100,
                    event =
                        Event.create(
                            vehicle = existingInternalVehicle,
                            source = DataSource.FRIDA.name,
                            priority = 1,
                            job = testImport,
                        ),
                    fieldName = "planetTrafficPermit",
                    fieldValue = "42",
                    businessKey = "${existingInternalVehicle.vehicleRef}-planetTrafficPermit",
                ),
            )

        val extractedFields =
            mapOf(
                "chassiNumber" to "YV3R8R324A1139756",
                "planetTrafficPermit" to 42,
            )

        every { eventRepository.save(any()) } returns mockk()
        every { vehicleSnapshotRepository.save(any()) } returns mockk()
        every { vehicleService.getLatestVehicle(any()) } returns mockk()
        every { vehicleRepository.save(any()) } answers { firstArg() }
        every { jobRepository.save(any()) } answers { firstArg() }

        every { eventDataSCD2Service.saveNewVersion(any(), any()) } answers { firstArg() }

        eventService.storeChangedFieldsFromVehicle(
            existingInternalVehicle,
            extractedFields,
            DataSource.FRIDA,
            testImport,
        )

        verify(exactly = 0) { eventRepository.save(any()) }
        verify(exactly = 0) { vehicleSnapshotRepository.save(any()) }
    }

    @Test
    fun `extractChangedFields - detects changed fields`() {
        val vehicle =
            InternalVehicle(
                vehicleId = 999,
                dataSource = DataSource.FRIDA,
                sourceId = 123,
                vehicleRef = "ABC123",
            )
        val testImport =
            FleetJob(
                jobId = 123,
                type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                quartzJobId = "dummyId",
                quartzJobGroup = "dummyGroup",
            )

        val existingData =
            listOf(
                EventData(
                    id = 1,
                    event = Event.create(1, vehicle, testImport, "FRIDA", 1, emptyList(), Instant.now()),
                    fieldName = "fieldA",
                    fieldValue = "OldValueA",
                    businessKey = "${vehicle.vehicleRef}-fieldA",

                ),
                EventData(
                    id = 2,
                    event = Event.create(2, vehicle, testImport, "FRIDA", 1, emptyList(), Instant.now()),
                    fieldName = "fieldB",
                    fieldValue = "UnchangedB",
                    businessKey = "${vehicle.vehicleRef}-fieldB",
                ),
            )
        every { eventDataRepository.findByVehicleId(vehicle.vehicleId) } returns existingData

        // new fields
        val extractedFields =
            mapOf(
                "fieldA" to "NewValueA",
                "fieldB" to "UnchangedB",
                "fieldC" to "BrandNewC",
            )

        val changedFields = eventService.extractChangedFields(vehicle, extractedFields)

        assertEquals(2, changedFields.size)
        assertEquals("NewValueA", changedFields["fieldA"])
        assertEquals("BrandNewC", changedFields["fieldC"])
        assertFalse(changedFields.contains("fieldB"))
    }

    @Test
    fun `createEventWithData - creates event with appropriate data`() {
        val vehicle = mockk<InternalVehicle>()
        val testImport = mockk<FleetJob>()
        val importId = 123

        every { vehicle.vehicleId } returns 456
        every { vehicle.vehicleRef } returns "ABC456"
        every { jobRepository.findById(importId) } returns testImport

        val eventSlot = slot<Event>()
        every { eventRepository.save(capture(eventSlot)) } answers { eventSlot.captured }

        val eventDataSlot = slot<EventData>()
        every { eventDataRepository.save(capture(eventDataSlot)) } answers { eventDataSlot.captured }

        every { eventDataSCD2Service.saveNewVersion(any(), any()) } answers { firstArg() }

        val fields = mapOf("field1" to "value1", "field2" to 123)
        eventService.createEventWithData(vehicle, fields, DataSource.FRIDA, importId)

        verify { eventRepository.save(any()) }
        verify { eventDataSCD2Service.saveNewVersion(any(), any()) }
    }
}
