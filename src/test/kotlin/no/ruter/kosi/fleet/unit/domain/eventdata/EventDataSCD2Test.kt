package no.ruter.kosi.fleet.unit.domain.eventdata

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventDataSCD2Test {
    private lateinit var mockJpaRepository: SpringDataEventDataRepository
    private lateinit var eventDataRepository: AuroraEventDataRepository
    private lateinit var mockVehicle: InternalVehicle
    private lateinit var mockEvent: Event
    private lateinit var mockJob: FleetJob

    @BeforeEach
    fun setup() {
        mockJpaRepository = mockk()
        val scd2Service = SCD2Service<EventData, Int, String>(mockJpaRepository)

        eventDataRepository = AuroraEventDataRepository(mockJpaRepository, scd2Service)

        mockVehicle = mockk()
        mockEvent = mockk()
        mockJob = mockk()

        every { mockVehicle.vehicleId } returns 1
        every { mockVehicle.vehicleRef } returns "TEST-REF-123"

        every { mockEvent.vehicle } returns mockVehicle
        every { mockEvent.eventId } returns 1
        every { mockEvent.source } returns DataSource.FRIDA.toString()
        every { mockEvent.timestamp } returns Instant.now()
        every { mockEvent.job } returns mockJob
    }

    @Test
    fun `should maintain correct versioning when field value changes multiple times`() {
        val currentVersions = mutableMapOf<String, EventData?>()

        every { mockJpaRepository.findCurrentByBusinessKey(any()) } answers {
            val key = firstArg<String>()
            currentVersions[key]
        }
        every { mockJpaRepository.save(any<EventData>()) } answers {
            val ed = firstArg<EventData>()
            val key = ed.businessKey
            if (ed.isCurrent) {
                currentVersions[key] = ed
            }
            ed
        }

        val t1 = Instant.now()
        val eventDataV1 =
            EventData(
                event = mockEvent,
                fieldName = "testField",
                fieldValue = "value1",
                timestamp = t1,
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-testField",
            )
        val savedV1 = eventDataRepository.save(eventDataV1)

        Thread.sleep(10)
        val t2 = Instant.now()
        val eventDataV2 =
            EventData.createNewVersion(
                previous = savedV1,
                newValue = "value2",
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-testField",
            ).also {
                it.effectiveFrom = t2
            }
        val savedV2 = eventDataRepository.save(eventDataV2)

        Thread.sleep(10)
        val t3 = Instant.now()
        val eventDataV3 =
            EventData.createNewVersion(
                previous = savedV2,
                newValue = "value3",
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-testField",
            ).also {
                it.effectiveFrom = t3
            }
        val savedV3 = eventDataRepository.save(eventDataV3)

        assertFalse(savedV1.isCurrent, "V1 should not be isCurrent")
        assertEquals(t2, savedV1.effectiveTo, "V1 effectiveTo should be t2")
        assertFalse(savedV2.isCurrent, "V2 should not be isCurrent")
        assertEquals(t3, savedV2.effectiveTo, "V2 effectiveTo should be t3")
        assertTrue(savedV3.isCurrent, "V3 should be isCurrent")
        assertNull(savedV3.effectiveTo, "V3 effectiveTo should be null")
    }

    @Test
    fun `should handle updates to multiple fields independently`() {
        val currentVersions = mutableMapOf<String, EventData?>()

        every { mockJpaRepository.findCurrentByBusinessKey(any()) } answers {
            val key = firstArg<String>()
            currentVersions[key]
        }

        every { mockJpaRepository.save(any<EventData>()) } answers {
            val ed = firstArg<EventData>()
            val key = ed.businessKey
            if (ed.isCurrent) {
                currentVersions[key] = ed
            }
            ed
        }

        val t1 = Instant.now()
        val eventDataA1 =
            EventData(
                event = mockEvent,
                fieldName = "fieldA",
                fieldValue = "A1",
                timestamp = t1,
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-fieldA",
            ).also {
                it.effectiveFrom = t1
                it.effectiveTo = null
                it.isCurrent = true
            }
        val eventDataB1 =
            EventData(
                event = mockEvent,
                fieldName = "fieldB",
                fieldValue = "B1",
                timestamp = t1,
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-fieldB",
            ).also {
                it.effectiveTo = null
                it.effectiveFrom = t1
                it.isCurrent = true
            }
        val savedA1 = eventDataRepository.save(eventDataA1)
        val savedB1 = eventDataRepository.save(eventDataB1)

        // make sure timestamps are different
        Thread.sleep(10)
        val t2 = Instant.now()
        val eventDataA2 =
            EventData.createNewVersion(
                previous = savedA1,
                newValue = "A2",
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-fieldA",
            ).also {
                it.effectiveFrom = t2
            }
        val savedA2 = eventDataRepository.save(eventDataA2)

        Thread.sleep(10)
        val t3 = Instant.now()
        val eventDataB2 =
            EventData.createNewVersion(
                previous = savedB1,
                newValue = "B2",
                businessKey = "${mockEvent.vehicle.vehicleRef!!}-fieldB",
            ).also {
                it.effectiveFrom = t3
            }
        val savedB2 = eventDataRepository.save(eventDataB2)

        assertFalse(savedA1.isCurrent, "FieldA V1 should not be isCurrent")
        assertEquals(t2, savedA1.effectiveTo, "FieldA V1 effectiveTo should be t2")
        assertTrue(savedA2.isCurrent, "FieldA V2 should be isCurrent")
        assertNull(savedA2.effectiveTo, "FieldA V2 effectiveTo should be null")

        assertFalse(savedB1.isCurrent, "FieldB V1 should not be isCurrent")
        assertEquals(t3, savedB1.effectiveTo, "FieldB V1 effectiveTo should be t3")
        assertTrue(savedB2.isCurrent, "FieldB V2 should be isCurrent")
        assertNull(savedB2.effectiveTo, "FieldB V2 effectiveTo should be null")
    }

    @Test
    fun `saving a new field value should create a new version`() {
        val fieldName = "wheelChairPlaces"
        val businessKey = "${mockEvent.vehicle.vehicleRef!!}-$fieldName"
        val oldValue = "1"
        val newValue = "2"
        val now = Instant.now()
        val yesterday = now.minus(1, ChronoUnit.DAYS)

        val oldEventData =
            EventData(
                id = 1,
                event = mockEvent,
                fieldName = fieldName,
                fieldValue = oldValue,
                businessKey = businessKey,
            ).also {
                it.effectiveFrom = yesterday
                it.isCurrent = true
            }

        val newEventData =
            EventData(
                event = mockEvent,
                fieldName = fieldName,
                fieldValue = newValue,
                businessKey = businessKey,
            ).also {
                it.effectiveFrom = now
                it.isCurrent = true
            }

        val savedEntitySlot = slot<EventData>()

        every { mockJpaRepository.findCurrentByBusinessKey(businessKey) } returns oldEventData
        every { mockJpaRepository.save(capture(savedEntitySlot)) } answers { savedEntitySlot.captured }

        val result = eventDataRepository.save(newEventData)

        verify(exactly = 2) { mockJpaRepository.save(any()) }

        assertFalse(oldEventData.isCurrent, "Old version should be marked as not isCurrent")
        assertEquals(now, oldEventData.effectiveTo, "Old version's effectiveTo should be set to the new version's timestamp")

        assertTrue(result.isCurrent, "New version should be marked as isCurrent")
        assertNull(result.effectiveTo, "New version's effectiveTo should be null")
        assertEquals("2", result.fieldValue, "New version should have the updated value")
    }

    @Test
    fun `finding current vehicle data should return latest versions`() {
        val vehicleId = 1

        val fieldName1 = "wheelChairPlaces"
        val fieldName2 = "vehicleLength"

        val eventData1 = mockk<EventData>()
        every { eventData1.fieldName } returns fieldName1
        every { eventData1.fieldValue } returns "2"

        val eventData2 = mockk<EventData>()
        every { eventData2.fieldName } returns fieldName2
        every { eventData2.fieldValue } returns "12000"

        every { mockJpaRepository.findCurrentByVehicleId(vehicleId) } returns
            listOf(
                eventData1,
                eventData2,
            )

        val result = eventDataRepository.findByVehicleId(vehicleId)

        assertEquals(2, result.size)
        assertTrue(result.any { it.fieldName == fieldName1 && it.fieldValue == "2" })
        assertTrue(result.any { it.fieldName == fieldName2 && it.fieldValue == "12000" })
    }
}