package no.ruter.kosi.fleet.unit.domain.import

import arrow.core.left
import arrow.core.right
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiError
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaApiService
import no.ruter.kosi.fleet.infrastructure.client.frida.FridaVehicleImporter
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.quartz.FridaVehicleImportJob
import no.ruter.kosi.fridaclient.models.ApiVehicleV2Model
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.quartz.JobDataMap
import org.quartz.JobExecutionContext
import kotlin.test.assertEquals

class FridaImportFleetJobTest {
    private val fridaVehicleImporter = mockk<FridaVehicleImporter>(relaxed = true)
    private val fridaApiService = mockk<FridaApiService>(relaxed = true)
    private val jobService = mockk<JobService>(relaxed = true)
    private val jobRepository = mockk<AuroraJobRepository>(relaxed = true)
    private val jobExecutionContext = mockk<JobExecutionContext>(relaxed = true)

    private val jobDataMap = JobDataMap()

    private lateinit var fridaVehicleImportJob: FridaVehicleImportJob
    private lateinit var fleetJob: FleetJob

    @BeforeEach
    fun setup() {
        clearAllMocks()

        jobDataMap.apply {
            put("jobId", 1)
        }

        every { jobExecutionContext.mergedJobDataMap } returns jobDataMap

        fleetJob =
            FleetJob(
                jobId = 1,
                type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                status = FleetJob.JobStatus.PENDING,
                manual = true,
                quartzJobId = "test-job",
                quartzJobGroup = "test-group",
            )

        every { jobRepository.findById(1) } returns fleetJob

        fridaVehicleImportJob =
            FridaVehicleImportJob(
                jobService,
                fridaVehicleImporter,
                fridaApiService,
            )
    }

    @Test
    fun `should fetch and process FRIDA vehicles successfully`() {
        val mockVehicles = listOf(mockk<ApiVehicleV2Model>())
        every { fridaApiService.fetchVehicles(any(), any()) } returns mockVehicles.right()

        val importSlot = slot<FleetJob>()
        val vehiclesSlot = slot<List<ApiVehicleV2Model>>()

        every {
            fridaVehicleImporter.processVehicles(
                capture(vehiclesSlot),
                capture(importSlot),
                any(),
                any(),
            )
        } returns Unit

        fridaVehicleImportJob.execute(jobExecutionContext)

        verify { fridaApiService.fetchVehicles(any(), any()) }
        verify {
            fridaVehicleImporter.processVehicles(
                any(),
                any(),
                any(),
                any(),
            )
        }

        assertEquals(mockVehicles, vehiclesSlot.captured)

        verify { jobService.updateJobProgress(0, 0, mockVehicles.size) }
    }

    @Test
    fun `should handle FRIDA API errors`() {
        val apiError = FridaApiError.NetworkError(RuntimeException("Network error"))
        every { fridaApiService.fetchVehicles(any(), any()) } returns apiError.left()

        assertDoesNotThrow {
            fridaVehicleImportJob.execute(jobExecutionContext)
        }

        verify { jobService.recordJobError(0, match { it.contains("FRIDA API network error") }) }
        verify { jobService.updateJobProgress(0, 0, 0) }
    }

    @Test
    fun `should handle cancellation`() {
        every { jobExecutionContext.result } returns "CANCELLED"

        fridaVehicleImportJob.execute(jobExecutionContext)

        verify { jobService.cancelJob(0) }
        verify(exactly = 0) { fridaApiService.fetchVehicles() }
    }

    @Test
    fun `should handle unexpected exceptions`() {
        every { fridaApiService.fetchVehicles(any(), any()) } throws RuntimeException("Unexpected error")

        assertDoesNotThrow {
            fridaVehicleImportJob.execute(jobExecutionContext)
        }

        verify { jobService.failJob(0, "Unexpected error") }
    }
}
