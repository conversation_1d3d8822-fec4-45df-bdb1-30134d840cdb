package no.ruter.kosi.fleet.unit.domain.scd2

import io.mockk.mockk
import io.mockk.verify
import no.ruter.kosi.fleet.configuration.BaseIntegrationTest
import no.ruter.kosi.fleet.domain.quality.VehicleQualityEntity
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.domain.scd2.SCD2Service
import no.ruter.kosi.fleet.domain.scd2.SCD2ServiceConfiguration
import no.ruter.kosi.fleet.domain.vehicle.KafkaVehicleProducerService
import no.ruter.kosi.fleet.domain.vehicle.VehicleReconstructionService
import no.ruter.kosi.fleet.domain.vehicle.VehicleService
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDtoMapper
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleQualityRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataVehicleRepository
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@SpringBootTest
@Import(SCD2ServiceInjectionTest.TestConfig::class)
class SCD2ServiceInjectionTest : BaseIntegrationTest() {

    @Autowired
    @Qualifier("vehicleSCD2Service")
    private lateinit var vehicleSCD2Service: ISCD2Service<VehicleEntity, Int, String>

    @Autowired
    @Qualifier("vehicleQualitySCD2Service")
    private lateinit var vehicleQualitySCD2Service: ISCD2Service<VehicleQualityEntity, Int, String>

    @Autowired
    private lateinit var vehicleService: VehicleService

    @Test
    fun `vehicleSCD2Service should be properly injected`() {
        assertThat(vehicleSCD2Service).isNotNull
    }

    @Test
    fun `vehicleQualitySCD2Service should be properly injected`() {
        assertThat(vehicleQualitySCD2Service).isNotNull
    }

    @Test
    fun `vehicleService should have SCD2Services properly injected`() {
        assertThat(vehicleService).isNotNull
        try {
            vehicleService.getLatestVehicle("test-key")
        } catch (e: Exception) {
            if (e is NullPointerException && e.message?.contains("SCD2Service") == true) {
                throw AssertionError("SCD2Service is null in VehicleService: ${e.message}", e)
            }
        }
    }

    @TestConfiguration
    @Import(SCD2ServiceConfiguration::class)
    class TestConfig {
        @Bean
        fun springDataVehicleRepository(): SpringDataVehicleRepository = mockk(relaxed = true)

        @Bean
        fun springDataVehicleQualityRepository(): SpringDataVehicleQualityRepository = mockk(relaxed = true)

        @Bean
        fun springDataEventDataRepository(): SpringDataEventDataRepository = mockk(relaxed = true)

        @Bean
        fun auroraVehicleRepository(): AuroraVehicleRepository = mockk(relaxed = true)

        @Bean
        fun auroraVehicleQualityRepository(): AuroraVehicleQualityRepository = mockk(relaxed = true)

        @Bean
        fun vehicleReconstructionService(): VehicleReconstructionService = mockk(relaxed = true)

        @Bean
        fun kafkaVehicleProducerService(): KafkaVehicleProducerService = mockk(relaxed = true)

        @Bean
        fun vehicleDtoMapper(): VehicleDtoMapper = mockk(relaxed = true)

        @Bean
        fun vehicleService(
            vehicleReconstructionService: VehicleReconstructionService,
            @Qualifier("vehicleSCD2Service") vehicleSCD2Service: ISCD2Service<VehicleEntity, Int, String>,
            @Qualifier("vehicleQualitySCD2Service") vehicleQualitySCD2Service: ISCD2Service<VehicleQualityEntity, Int, String>,
            auroraVehicleRepository: AuroraVehicleRepository,
            auroraVehicleQualityRepository: AuroraVehicleQualityRepository,
            kafkaVehicleProducerService: KafkaVehicleProducerService,
            vehicleDtoMapper: VehicleDtoMapper
        ): VehicleService {
            return VehicleService(
                reconstructionService = vehicleReconstructionService,
                vehicleSCD2Service = vehicleSCD2Service,
                vehicleQualitySCD2Service = vehicleQualitySCD2Service,
                vehicleRepository = auroraVehicleRepository,
                vehicleQualityRepository = auroraVehicleQualityRepository,
                kafkaVehicleProducerService = kafkaVehicleProducerService,
                vehicleDtoMapper = vehicleDtoMapper
            )
        }
    }
}
