package no.ruter.kosi.fleet.unit.domain.vehicle

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.quarantine.QuarantineService
import no.ruter.kosi.fleet.domain.vehicle.VehicleReconstructionService
import no.ruter.kosi.fleet.domain.vehicle.dto.CapacityDto
import no.ruter.kosi.fleet.domain.vehicle.dto.ContractorDto
import no.ruter.kosi.fleet.domain.vehicle.dto.DimensionsDto
import no.ruter.kosi.fleet.domain.vehicle.dto.VehicleDto
import no.ruter.kosi.fleet.domain.vehicle.entities.VehicleEntity
import no.ruter.kosi.fleet.domain.vehicle.processors.common.VehicleFieldProcessorRegistry
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraInternalVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraVehicleRepository
import no.ruter.kosi.fleet.infrastructure.persistence.snowflake.repository.SnowflakeVehicleRepository
import no.ruter.kosi.fleet.utils.generators.createExpectedVehicleQuality
import no.ruter.kosi.fleet.utils.generators.createMinimalVehicleDto
import no.ruter.kosi.fleet.utils.generators.toEventData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.time.Instant
import java.time.LocalDate

data class ReconstructionTestCase(
    val name: String,
    val vehicleRef: String,
    val eventData: List<EventData>,
    val expectedVehicleQuality: Pair<VehicleDto, VehicleQualityDto>,
    val lastSnapshot: VehicleEntity? = null,
    val reconstructAtTimestamp: Instant? = Instant.now()
) {
    val vehicleId: Int = 1
}

@SpringBootTest
class VehicleReconstructionServiceTest @Autowired constructor(
    val vehicleFieldProcessorRegistry: VehicleFieldProcessorRegistry
) {
    @Autowired
    private lateinit var jacksonObjectMapper: ObjectMapper
    private lateinit var vehicleReconstructionService: VehicleReconstructionService

    private val eventDataRepository = mockk<AuroraEventDataRepository>()
    private val snapshotRepository = mockk<AuroraVehicleRepository>(relaxed = true)
    private val internalVehicleRepository = mockk<AuroraInternalVehicleRepository>(relaxed = true)
    private val quarantineService = mockk<QuarantineService>(relaxed = true)

    @TestConfiguration
    class TestConfig {
        @Bean(name = ["snowflakeVehicleRepositoryPort"])
        @Primary
        fun snowflakeVehicleRepositoryPort(): AuroraInternalVehicleRepository = mockk<AuroraInternalVehicleRepository>(relaxed = true)

        @Bean
        fun snowflakeVehicleRepository(): SnowflakeVehicleRepository = mockk<SnowflakeVehicleRepository>(relaxed = true)
    }

    @BeforeEach
    fun beforeEach() {
        every { quarantineService.clearQuarantineForVehicle(any()) } just runs
        vehicleReconstructionService =
            VehicleReconstructionService(
                eventDataRepository = eventDataRepository,
                internalVehicleRepository = internalVehicleRepository,
                snapshotRepository = snapshotRepository,
                fieldProcessorRegistry = vehicleFieldProcessorRegistry,
                quarantineService = quarantineService,
                objectMapper = jacksonObjectMapper,
            )
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("testCases")
    fun `should correctly reconstruct vehicle`(testCase: ReconstructionTestCase) {
        val result = vehicleReconstructionService.reconstructVehicleFromEventData(
            vehicleId = testCase.vehicleId,
            eventDataList = testCase.eventData,
            lastSnapshot = testCase.lastSnapshot,
            timestamp = testCase.reconstructAtTimestamp
        )

        assertThat(result).isNotNull()
        val (vehicle, quality) = result!!
        val (expectedVehicle, _) = testCase.expectedVehicleQuality
        
        assertThat(vehicle).isNotNull()
        assertThat(quality).isNotNull()

        assertThat(vehicle)
            .usingRecursiveComparison()
            .ignoringFields("effectiveFrom")
            .isEqualTo(expectedVehicle)
    }
    
    companion object {
        @JvmStatic
        fun testCases() = listOf(
           ReconstructionTestCase(
                name = "Reconstruct vehicle with all fields",
                vehicleRef = "YV3R8R324A1139756",
                eventData = toEventData(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "20",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.rullestolplasser" to "1",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.staplasser" to "0",
                    "customAttr_Fordonslängd" to "18,750",
                    "customAttr_Internnummer_SIS" to "123456",
                    "registrationDate" to "2024-12-05T00:00Z",
                    "chassiNumber" to "YV3R8R324A1139756",
                    "contractor" to "{Id=8, Name=Norled}"
                ),
               expectedVehicleQuality =
                   createMinimalVehicleDto("YV3R8R324A1139756").let {
                       it.copy(
                           capacity = CapacityDto(
                               seated = 20,
                               standing = 0,
                               total = 20,
                               standingArea = 22.19,
                               predictedTotal2PerM2 = 64,
                               predictedTotal3PerM2 = 87,
                               predictedTotal4PerM2 = 109
                           ),
                           contractor = ContractorDto(
                               id = 8,
                               contractIdInFrida = 0,
                               name = "Norled"
                           ),
                           identification = it.identification.copy(
                               vehicleSISId = "123456"
                           ),
                           registration = it.registration.copy(
                               registrationDate = LocalDate.parse("2024-12-05")
                           ),
                           technical = it.technical.copy(
                               dimensions = DimensionsDto(
                                   lengthInMeters = 18.75,
                                   widthInMeters = null,
                                   heightInMeters = null,
                               )
                           )
                       )
                   } to createExpectedVehicleQuality().second
            ),
            ReconstructionTestCase(
                // TODO use this one to fix error handling/reporting
                name = "Reconstruct vehicle with invalid fields",
                vehicleRef = "YV3R8R324A1139756",
                eventData = toEventData(
                    "godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "INVALID",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "69",
                    "customAttr_Internnummer_SIS" to "INVALID",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.rullestolplasser" to "INVALID",
                    "numberOfPassengersSeated" to "INVALID",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.staPlasser" to "INVALID",
                    "chassiNumber" to "YV3R8R324A1139756",
                    "contractor" to "{hello}"
                ),
                expectedVehicleQuality = createMinimalVehicleDto("YV3R8R324A1139756").let {
                    it.copy(
                        capacity = CapacityDto(
                            seated = 69,
                            standing = null,
                            total = 69,
                            standingArea = null,
                            predictedTotal2PerM2 = null,
                            predictedTotal3PerM2 = null,
                            predictedTotal4PerM2 = null
                        ),
                        identification = it.identification.copy(
                            chassisNumber = "YV3R8R324A1139756",
                        ),
                    )} to createExpectedVehicleQuality().second
            ),
            ReconstructionTestCase(
                name = "Reconstruct vehicle with all fields with ISO date-format",
                vehicleRef = "YV3R8R324A1139756",
                eventData = toEventData(
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.sitteplasserTotalt" to "20",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.rullestolplasser" to "1",
                    "godkjenning.tekniskGodkjenning.tekniskeData.persontall.staplasser" to "0",
                    "vehicleLength" to "22",
                    "customAttr_Internnummer_SIS" to "123456",
                    "registrationDate" to "2023-01-15T00:00Z",
                    "chassiNumber" to "YV3R8R324A1139756",
                    "contractor" to "{Id=8, Name=Norled}"
                ),
                expectedVehicleQuality =
                    createMinimalVehicleDto("YV3R8R324A1139756").let {
                        it.copy(
                            capacity = CapacityDto(
                                seated = 20,
                                standing = 0,
                                total = 20,
                                standingArea = 27.19,
                                predictedTotal2PerM2 = 74,
                                predictedTotal3PerM2 = 102,
                                predictedTotal4PerM2 = 129
                            ),
                            contractor = ContractorDto(
                                id = 8,
                                contractIdInFrida = 0,
                                name = "Norled"
                            ),
                            identification = it.identification.copy(
                                vehicleSISId = "123456"
                            ),
                            registration = it.registration.copy(
                                registrationDate = LocalDate.parse("2023-01-15")
                            ),
                            technical = it.technical.copy(
                                dimensions = DimensionsDto(
                                    lengthInMeters = 22.0,
                                    widthInMeters = null,
                                    heightInMeters = null,
                                )
                            )

                            )
                    } to createExpectedVehicleQuality().second
            ),
            // TODO add more testcases
//            ReconstructionTestCase(
//                name = "Reconstruct vehicle with contract",
//                vehicleRef = "YV3R8R324A1139756",
//                eventData = toEventData(
//                    "contractor" to "{Id=2, Name=Connect bus}"
//                ),
//                expectedVehicleQuality = createExpectedVehicleQuality(
//                    vehicleDto = VehicleDto(
//                        contract = VehicleDto.ContractDto(id = 2, name = "Connect bus"),
//                        identification = VehicleDto.IdentificationDto(
//                            vehicleRef = null,
//                            vehicleSisId = null
//                        ),
//                        technical = VehicleDto.TechnicalDto(
//                            dimensions = VehicleDto.DimensionsDto(length = null),
//                            passengerCount = VehicleDto.PassengerCountDto(
//                                total = 0,
//                                seated = 0,
//                                wheelchair = 0
//                            )
//                        )
//                    ),
//                    quality = VehicleQualityDto(
//                        contract = QualityType.VALID,
//                        vehicleRef = QualityType.MISSING_VALUE,
//                        vehicleSisId = QualityType.MISSING_VALUE,
//                        vehicleLength = QualityType.MISSING_VALUE,
//                        totalNumberOfPassengers = QualityType.MISSING_VALUE,
//                        numberOfPassengersSeated = QualityType.MISSING_VALUE,
//                        capacity = QualityType.MISSING_VALUE
//                    )
//                )
//            )
        )
    }
}
