package no.ruter.kosi.fleet.unit.infrastructure.client

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import no.ruter.kosi.autosysclient.models.EnkeltOppslagKjoretoydata
import no.ruter.kosi.autosysclient.models.Kjennemerke
import no.ruter.kosi.fleet.domain.event.EventService
import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysImporter
import no.ruter.kosi.fleet.infrastructure.client.autosys.AutosysImporterError
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AutosysImporterTest {
    private val eventService = mockk<EventService>(relaxed = true)
    private val objectMapper = mockk<ObjectMapper>(relaxed = true)

    private lateinit var autosysImporter: AutosysImporter

    @BeforeEach
    fun setup() {
        autosysImporter = AutosysImporter(eventService, objectMapper)

        every {
            eventService.storeChangedFieldsFromVehicle(any(), any(), any(), any())
        } answers { firstArg() }
    }

    @Test
    fun `processVehicle should extract fields and store changes`() {
        val externalVehicle = createExternalVehicle()
        val internalVehicle = createInternalVehicle()
        val import =
            FleetJob(
                jobId = 1,
                type = FleetJob.JobType.IMPORT_AUTOSYS,
                status = FleetJob.JobStatus.RUNNING,
                manual = true,
                quartzJobId = "test-job",
                quartzJobGroup = "test-group",
            )

        val result = autosysImporter.processVehicle(externalVehicle, internalVehicle, import)

        assertTrue(result.isRight())
        assertEquals(internalVehicle, result.getOrNull())

        verify {
            eventService.storeChangedFieldsFromVehicle(
                eq(internalVehicle),
                match { it.isNotEmpty() },
                eq(DataSource.AUTOSYS),
                eq(import),
            )
        }
    }

    @Test
    fun `processVehicle should handle exceptions and return a Left`() {
        val externalVehicle = createExternalVehicle()
        val internalVehicle = createInternalVehicle()
        val import =
            FleetJob(
                jobId = 1,
                type = FleetJob.JobType.IMPORT_AUTOSYS,
                status = FleetJob.JobStatus.RUNNING,
                manual = true,
                quartzJobId = "test-job",
                quartzJobGroup = "test-group",
            )

        every {
            eventService.storeChangedFieldsFromVehicle(any(), any(), any(), any())
        } throws RuntimeException("Test error")

        val result = autosysImporter.processVehicle(externalVehicle, internalVehicle, import)

        assertTrue(result.isLeft())
        val error = result.swap().getOrNull()
        assertTrue(error is AutosysImporterError.ProcessingFailure)
        assertEquals(internalVehicle.vehicleId, (error).vehicleId)
        assertEquals("Test error", error.cause.message)
    }

    private fun createExternalVehicle(): EnkeltOppslagKjoretoydata =
        mockk<EnkeltOppslagKjoretoydata>().apply {
            every { kjennemerke } returns listOf(Kjennemerke(kjennemerke = "AB1234"))
        }

    private fun createInternalVehicle(): InternalVehicle =
        InternalVehicle(
            vehicleId = 123,
            dataSource = DataSource.FRIDA,
            sourceId = 456,
            vehicleRef = "VIN12345",
        )
}
