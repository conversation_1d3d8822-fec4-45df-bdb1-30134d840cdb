package no.ruter.kosi.fleet.unit.infrastructure.persistence

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.scd2.ISCD2Service
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraEventDataRepository
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.jpa.SpringDataEventDataRepository
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class EventDataRepositoryOptimizationTest {

    private lateinit var jpaRepository: SpringDataEventDataRepository
    private lateinit var scd2Service: ISCD2Service<EventData, Int, String>
    private lateinit var auroraEventDataRepository: AuroraEventDataRepository

    @BeforeEach
    fun setup() {
        jpaRepository = mockk()
        scd2Service = mockk()
        auroraEventDataRepository = AuroraEventDataRepository(jpaRepository, scd2Service)
    }

    @Test
    fun `findCurrentFieldValuesByVehicleId should use optimized query and return field map`() {
        val vehicleId = 123
        val mockResults = listOf(
            arrayOf<Any?>("chassiNumber", "ABC123"),
            arrayOf<Any?>("wheelChairPlaces", "2"),
            arrayOf<Any?>("doors", "3")
        )
        
        every { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) } returns mockResults

        val result = auroraEventDataRepository.findCurrentFieldValuesByVehicleId(vehicleId)

        verify(exactly = 1) { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) }
        
        val expected = mapOf(
            "chassiNumber" to "ABC123",
            "wheelChairPlaces" to "2", 
            "doors" to "3"
        )
        assertEquals(expected, result)
    }

    @Test
    fun `findCurrentFieldValuesByVehicleId should handle null values correctly`() {
        val vehicleId = 456
        val mockResults = listOf(
            arrayOf<Any?>("chassiNumber", "XYZ789"),
            arrayOf<Any?>("wheelChairPlaces", null),
            arrayOf<Any?>("doors", "2")
        )
        
        every { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) } returns mockResults

        val result = auroraEventDataRepository.findCurrentFieldValuesByVehicleId(vehicleId)

        verify(exactly = 1) { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) }
        
        val expected = mapOf(
            "chassiNumber" to "XYZ789",
            "wheelChairPlaces" to null,
            "doors" to "2"
        )
        assertEquals(expected, result)
    }

    @Test
    fun `findCurrentFieldValuesByVehicleId should return empty map when no results`() {
        val vehicleId = 789
        val mockResults = emptyList<Array<Any?>>()
        
        every { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) } returns mockResults

        val result = auroraEventDataRepository.findCurrentFieldValuesByVehicleId(vehicleId)

        verify(exactly = 1) { jpaRepository.findCurrentFieldValuesByVehicleId(vehicleId) }
        assertEquals(emptyMap(), result)
    }
}
