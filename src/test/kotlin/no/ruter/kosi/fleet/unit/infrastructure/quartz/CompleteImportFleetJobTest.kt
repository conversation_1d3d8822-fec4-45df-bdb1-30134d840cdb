package no.ruter.kosi.fleet.unit.infrastructure.quartz

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.job.JobSchedulerManager
import no.ruter.kosi.fleet.domain.job.JobService
import no.ruter.kosi.fleet.infrastructure.persistence.aurora.repository.AuroraJobRepository
import no.ruter.kosi.fleet.infrastructure.quartz.CompleteImportJob
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.quartz.JobDataMap
import org.quartz.JobExecutionContext
import org.quartz.Scheduler

class CompleteImportFleetJobTest {
    private val jobService = mockk<JobService>(relaxed = true)
    private val jobRepository = mockk<AuroraJobRepository>(relaxed = true)
    private val jobExecutionContext = mockk<JobExecutionContext>(relaxed = true)
    private val jobSchedulerManager = mockk<JobSchedulerManager>(relaxed = true)
    private val scheduler = mockk<Scheduler>(relaxed = true)

    private val jobDataMap = JobDataMap()

    private lateinit var completeImportJob: CompleteImportJob

    @BeforeEach
    fun setup() {
        clearAllMocks()

        jobDataMap.apply {
            put("jobId", 1)
        }

        every { jobExecutionContext.mergedJobDataMap } returns jobDataMap

        val parentFleetJob =
            FleetJob(
                jobId = 1,
                type = FleetJob.JobType.COMPLETE_IMPORT,
                status = FleetJob.JobStatus.PENDING,
                manual = true,
                quartzJobId = "test-job",
                quartzJobGroup = "test-group",
            )

        every { jobRepository.findById(1) } returns parentFleetJob

        val parentJobPending = FleetJob(
            jobId = parentFleetJob.jobId,
            type = parentFleetJob.type,
            status = FleetJob.JobStatus.PENDING,
            manual = parentFleetJob.manual,
            parentJobId = parentFleetJob.parentJobId,
            quartzJobId = parentFleetJob.quartzJobId,
            quartzJobGroup = parentFleetJob.quartzJobGroup
        )
        val parentJobRunning = FleetJob(
            jobId = parentFleetJob.jobId,
            type = parentFleetJob.type,
            status = FleetJob.JobStatus.RUNNING,
            manual = parentFleetJob.manual,
            parentJobId = parentFleetJob.parentJobId,
            quartzJobId = parentFleetJob.quartzJobId,
            quartzJobGroup = parentFleetJob.quartzJobGroup
        )
        val parentJobCompleted = FleetJob(
            jobId = parentFleetJob.jobId,
            type = parentFleetJob.type,
            status = FleetJob.JobStatus.COMPLETED,
            manual = parentFleetJob.manual,
            parentJobId = parentFleetJob.parentJobId,
            quartzJobId = parentFleetJob.quartzJobId,
            quartzJobGroup = parentFleetJob.quartzJobGroup
        )
        var parentStatus = FleetJob.JobStatus.PENDING
        every { jobService.findJob(1) } answers {
            when (parentStatus) {
                FleetJob.JobStatus.PENDING -> parentJobPending
                FleetJob.JobStatus.RUNNING -> parentJobRunning
                FleetJob.JobStatus.COMPLETED -> parentJobCompleted
                else -> parentJobPending
            }
        }
        every { jobService.save(any()) } answers {
            parentStatus = FleetJob.JobStatus.RUNNING
            value
        }
        every { jobService.completeJob(1) } answers {
            parentStatus = FleetJob.JobStatus.COMPLETED
            value
        }

        val childJobsPhases = listOf(
            listOf(FleetJob(jobId = 100, type = FleetJob.JobType.IMPORT_FRIDA_CONTRACTS, status = FleetJob.JobStatus.COMPLETED, manual = true, parentJobId = 1, quartzJobId = "test-child-job-1", quartzJobGroup = "test-group")),
            listOf(
                FleetJob(jobId = 100, type = FleetJob.JobType.IMPORT_FRIDA_CONTRACTS, status = FleetJob.JobStatus.COMPLETED, manual = true, parentJobId = 1, quartzJobId = "test-child-job-1", quartzJobGroup = "test-group"),
                FleetJob(jobId = 101, type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES, status = FleetJob.JobStatus.FAILED, manual = true, parentJobId = 1, quartzJobId = "test-child-job-2", quartzJobGroup = "test-group")
            ),
            listOf(
                FleetJob(jobId = 100, type = FleetJob.JobType.IMPORT_FRIDA_CONTRACTS, status = FleetJob.JobStatus.COMPLETED, manual = true, parentJobId = 1, quartzJobId = "test-child-job-1", quartzJobGroup = "test-group"),
                FleetJob(jobId = 101, type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES, status = FleetJob.JobStatus.FAILED, manual = true, parentJobId = 1, quartzJobId = "test-child-job-2", quartzJobGroup = "test-group"),
                FleetJob(jobId = 102, type = FleetJob.JobType.IMPORT_AUTOSYS, status = FleetJob.JobStatus.PENDING, manual = true, parentJobId = 1, quartzJobId = "test-child-job-3", quartzJobGroup = "test-group")
            )
        )
        var phase = 0
        every { jobService.findChildJobs(1) } answers {
            val jobs = childJobsPhases.getOrElse(phase) { childJobsPhases.last() }
            phase++
            jobs
        }

        every { jobService.findJob(100) } returns childJobsPhases[0][0]
        every { jobService.findJob(101) } returns childJobsPhases[1][1]
        (every { jobService.findJob(102) } returns childJobsPhases[2].getOrNull(2))

        every {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = any(),
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        } returns
            mockk {
                every { jobId } returns 100
                every { status } returns FleetJob.JobStatus.COMPLETED
            }

        completeImportJob =
            CompleteImportJob(
                jobService,
                jobSchedulerManager,
            )
    }

    @Test
    fun `complete import job should execute all phases successfully`() {
        val fridaFleetJob =
            FleetJob(
                jobId = 101,
                type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-frida-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        val autosysFleetJob =
            FleetJob(
                jobId = 102,
                type = FleetJob.JobType.IMPORT_AUTOSYS,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-autosys-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        val reconstructionFleetJob =
            FleetJob(
                jobId = 103,
                type = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-reconstruction-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        val publishingToKafkaJob =
            FleetJob(
                jobId = 104,
                type = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-publishing-kafka-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        val publishingToSnowflakeJob =
            FleetJob(
                jobId = 105,
                type = FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-publishing-snowflake-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        val capacityPublishingFleetJob =
            FleetJob(
                jobId = 106,
                type = FleetJob.JobType.VEHICLE_CAPACITY_PUBLISHING,
                status = FleetJob.JobStatus.COMPLETED,
                manual = true,
                quartzJobId = "test-capacity-job",
                quartzJobGroup = "test-group",
                parentJobId = 1,
            )

        every { jobRepository.findById(101) } returns fridaFleetJob
        every { jobRepository.findById(102) } returns autosysFleetJob
        every { jobRepository.findById(103) } returns reconstructionFleetJob
        every { jobRepository.findById(104) } returns publishingToKafkaJob
        every { jobRepository.findById(105) } returns publishingToSnowflakeJob
        every { jobRepository.findById(106) } returns capacityPublishingFleetJob

        assertDoesNotThrow {
            completeImportJob.execute(jobExecutionContext)
        }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                jobClass = any(),
            )
        }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.IMPORT_AUTOSYS,
                jobClass = any(),
            )
        }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.VEHICLE_RECONSTRUCTION,
                jobClass = any(),
            )
        }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.VEHICLE_PUBLISHING_KAFKA,
                jobClass = any(),
            )
        }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.VEHICLE_PUBLISHING_SNOWFLAKE,
                jobClass = any(),
            )
        }

        verify { jobService.completeJob(1) }
    }

    @Test
    fun `should handle cancelled job`() {
        every { jobExecutionContext.result } returns "CANCELLED"

        every { jobService.findJob(1) } returns FleetJob(
            jobId = 1,
            type = FleetJob.JobType.COMPLETE_IMPORT,
            status = FleetJob.JobStatus.CANCELLED,
            manual = true,
            quartzJobId = "test-job",
            quartzJobGroup = "test-group"
        )

        completeImportJob.execute(jobExecutionContext)

        verify { jobService.cancelJob(1) }

        verify(exactly = 0) {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = any(),
                jobClass = any(),
            )
        }
    }

    @Test
    fun `should continue with phases even if one fails`() {
        every { jobService.findJob(101) } returns FleetJob(
            jobId = 101,
            type = FleetJob.JobType.IMPORT_FRIDA_CONTRACTS,
            status = FleetJob.JobStatus.COMPLETED,
            manual = true,
            parentJobId = 1,
            quartzJobId = "test-frida-contracts-job",
            quartzJobGroup = "test-group"
        )
        every { jobService.findJob(102) } returns FleetJob(
            jobId = 102,
            type = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
            status = FleetJob.JobStatus.FAILED,
            manual = true,
            parentJobId = 1,
            quartzJobId = "test-frida-vehicles-job",
            quartzJobGroup = "test-group",
            lastError = "FRIDA API Error"
        )

        every {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = eq(FleetJob.JobType.IMPORT_FRIDA_CONTRACTS),
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        } returns mockk {
            every { jobId } returns 101
            every { status } returns FleetJob.JobStatus.COMPLETED
        }
        every {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = eq(FleetJob.JobType.IMPORT_FRIDA_VEHICLES),
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        } returns mockk {
            every { jobId } returns 102
            every { status } returns FleetJob.JobStatus.FAILED
            every { lastError } returns "FRIDA API Error"
        }

        assertDoesNotThrow {
            completeImportJob.execute(jobExecutionContext)
        }

        verify { jobService.failJob(1, match { it.contains("JobTransitionError") }) }

        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.IMPORT_FRIDA_CONTRACTS,
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        }
        verify {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = FleetJob.JobType.IMPORT_FRIDA_VEHICLES,
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        }
        verify(exactly = 7) {
            jobSchedulerManager.scheduleJob(
                parentFleetJob = any(),
                fleetJobType = any(),
                jobClass = any(),
                parameters = any(),
                startNow = any(),
            )
        }

        verify { jobService.failJob(
            1,
            error = any()
        ) }
    }
}
