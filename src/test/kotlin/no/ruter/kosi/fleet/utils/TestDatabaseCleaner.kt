package no.ruter.kosi.fleet.utils

import jakarta.persistence.EntityManager
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class TestDatabaseCleaner(
    private val entityManager: EntityManager,
) {
    private val logger = LoggerFactory.getLogger(TestDatabaseCleaner::class.java)

    @Transactional("auroraTransactionManager")
    fun cleanDatabase() {
        try {
            logger.info("Cleaning test database...")

            // Use TRUNCATE CASCADE for PostgreSQL - this removes all data and resets sequences
            // while handling foreign key constraints automatically
            val tables = listOf(
                "event_data",
                "events",
                "quarantined_fields",
                "vehicle_field_quality",
                "vehicle_quality",
                "contract_events",
                "vehicles",
                "internal_vehicles",
                "jobs"
            )

            // TRUNCATE CASCADE will handle foreign key constraints automatically
            entityManager.createNativeQuery("TRUNCATE TABLE ${tables.joinToString(", ")} CASCADE").executeUpdate()

            logger.info("Test database cleaned successfully")
        } catch (e: Exception) {
            logger.error("Error during database cleanup: ${e.message}", e)
            throw e
        }
    }
}
