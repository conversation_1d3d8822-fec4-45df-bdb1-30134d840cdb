package no.ruter.kosi.fleet.utils

import jakarta.persistence.EntityManager
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class TestDatabaseCleaner(
    private val entityManager: EntityManager,
) {
    private val logger = LoggerFactory.getLogger(TestDatabaseCleaner::class.java)

    @Transactional("auroraTransactionManager")
    fun cleanDatabase() {
        try {
            entityManager.createNativeQuery("SET REFERENTIAL_INTEGRITY FALSE").executeUpdate()

            logger.info("Cleaning test database...")

            entityManager.createNativeQuery("DELETE FROM event_data").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM events").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM quarantined_fields").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM vehicle_field_quality").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM vehicle_quality").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM contract_events").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM vehicles").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM internal_vehicles").executeUpdate()
            entityManager.createNativeQuery("DELETE FROM jobs").executeUpdate()

            entityManager.createNativeQuery("SET REFERENTIAL_INTEGRITY TRUE").executeUpdate()

            logger.info("Test database cleaned successfully")
        } catch (e: Exception) {
            logger.error("Error during database cleanup: ${e.message}", e)
            throw e
        }
    }
}
