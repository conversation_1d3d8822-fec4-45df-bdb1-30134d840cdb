package no.ruter.kosi.fleet.utils.generators

import no.ruter.kosi.fleet.domain.event.DataSource
import no.ruter.kosi.fleet.domain.event.entities.Event
import no.ruter.kosi.fleet.domain.eventdata.entities.EventData
import no.ruter.kosi.fleet.domain.internalvehicle.entities.InternalVehicle
import no.ruter.kosi.fleet.domain.job.FleetJob
import no.ruter.kosi.fleet.domain.quality.dto.VehicleFieldQualityDto
import no.ruter.kosi.fleet.domain.quality.dto.VehicleQualityDto
import no.ruter.kosi.fleet.domain.vehicle.dto.*
import java.time.Instant
import no.ruter.kosi.fleet.domain.quality.QualityType as QualityTypeEnum

val testEvent = Event.create(
    eventId = 0,
    vehicle = createTestInternalVehicle(),
    timestamp = Instant.now(),
    source = DataSource.MANUAL.name,
    priority = 0,
    job = createTestJob(),
)

fun createTestInternalVehicle(
    vehicleRef: String = "YV3R8R324A1139756",
    sourceId: Int = 123,
): InternalVehicle =
    InternalVehicle(
        vehicleId = 1,
        dataSource = DataSource.FRIDA,
        sourceId = sourceId,
        vehicleRef = vehicleRef,
    )

fun createTestJob(): FleetJob =
    FleetJob(
        jobId = 1,
        type = FleetJob.JobType.IMPORT_AUTOSYS,
        status = FleetJob.JobStatus.RUNNING,
        manual = true,
        quartzJobId = "dummyId",
        quartzJobGroup = "dummyGroup",
    )

fun createMinimalVehicleDto(
    vehicleRef: String = ""
): VehicleDto {
    return VehicleDto(
        vehicleId = 0,
        internalVehicleId = 1,
        vehicleRef = "YV3R8R324A1139756",
        contractor = null,
        identification = IdentificationDto(
            vehicleId = 1,
            vehicleSISId = null,
            publicId = "",
            licensePlate = "",
            chassisNumber = vehicleRef,
            manufacturer = ManufacturerDto(
                chassis = "",
                brand = "",
                producer = "",
                chassisManufacturer = ""
            ),
            modelYear = "",
            model = ModelInfoDto(
                typeDesignation = "",
                commercialName = ""
            ),
            color = "",
            busType = BusType.BYBUSS
        ),
        registration = RegistrationDto(
            registrationDate = null,
            firstRegistrationNorwayDate = "",
            firstRegistrationOnOwnership = "",
            status = RegistrationStatus.UKJENT,
            use = VehicleUse.UKJENT,
            businessCode = "",
            businessDescription = "",
            periodicControl = PeriodicControlDto(
                lastApproved = "",
                nextDue = ""
            ),
            permitPeriod = PermitPeriodDto(
                start = "",
                end = ""
            ),
            taxiCountyLetter = "",
        ),
        features = FeaturesDto(
            comfort = ComfortDto(
                airConditioning = false,
                heater = null,
                readingLights = null,
                armrest = null,
                backrest = null,
                foldingSeats = null
            ),
            accessibility = AccessibilityDto(
                lowFloor = false,
                lowEntry = "",
                lift = null,
                ramp = false,
                kneeling = null,
                accessibilityRamp = null,
                wheelchairPlaces = null,
                walkerPlaces = null,
                covid19CapacityLimit = null
            ),
            safety = SafetyDto(
                seatBelts = "",
                seatBeltReminder = false,
                visualBeltReminder = null,
                airbagPassenger = null,
                alcoLock = "",
                fireExtinguisher = false,
                fireExtinguisherInEngineCompartment = false,
                defibrillators = null,
                cameraSurveillanceSafety = false,
                cameraSurveillanceTrafficSafety = false,
            ),
            signage = SignageDto(
                destinationSign = "",
                internalInformationSign = false,
                externalDestinationCall = null,
                stopAnnouncements = false
            ),
            apc = APCInfoDto(
                count = 0,
                type = ""
            ),
            other = OtherFeaturesDto(
                bikeRack = null,
                boosterSeat = null,
                childSeats = null,
                strollerPlaces = 0,
                passengerCounter = false,
                ticketing = TicketingDto(
                    cardReaders = 0,
                    machineNumber = "",
                    system = ""
                ),
                vehicleComputer = ""
            )
        ),
        capacity = CapacityDto(
            seated = 0,
            standing = null,
            total = 0,
            standingArea = 0.0,
            predictedTotal2PerM2 = 0,
            predictedTotal3PerM2 = 0,
            predictedTotal4PerM2 = 0
        ),
        technical = TechnicalDto(
            technicalApproval = TechnicalApprovalDto(
                firstApproval = FirstApprovalDto(
                    registeredDate = "",
                    approvalId = "",
                    type = ApprovalType.GODKJENNING,
                    validFrom = "",
                    customsReference = ""
                ),
                technicalApproval = FirstApprovalDto(
                    registeredDate = "",
                    approvalId = "",
                    type = ApprovalType.GODKJENNING,
                    validFrom = "",
                    customsReference = ""
                ),
                classification = ClassificationDto(
                    feeCode = 0,
                    technicalCode = "",
                    specialCharacteristics = ""
                ),
                requirements = ""
            ),
            dimensions = DimensionsDto(
                lengthInMeters = null,
                widthInMeters = null,
                heightInMeters = null
            ),
            weights = WeightsDto(
                curbWeight = null,
                totalWeight = 0.0,
                unitWeights = UnitWeightsDto(
                    frontAxle = 0,
                    rearAxleGroup = 0,
                    total = 0
                ),
                payload = 0,
                grossVehicleWeight = 0
            ),
            axles = AxlesDto(
                count = 0,
                groups = listOf(
                    AxleGroupDto(
                        id = 0,
                        placement = 0,
                        allowedLoad = 0,
                        axles = listOf(
                            AxleDetailDto(
                                placement = 0,
                                drivable = false,
                                distanceToNextAxle = null,
                                weight = null,
                                airSuspension = false
                            )
                        )
                    )
                )
            ),
            wheelsAndBrakes = WheelsAndBrakesDto(
                tyreRimCombinations = listOf(
                    TireRimCombinationDto(
                        axleId = 0,
                        tyre = "",
                        rim = "",
                        loadIndex = 0,
                        speedCode = "",
                        twin = false
                    )
                ),
                trailerBrakeConnection = listOf()
            ),
            engineAndDrivetrain = EngineAndDrivetrainDto(
                engineTypeId = "",
                primaryCategory = "",
                hybridVehicle = false,
                gearbox = GearboxDto(
                    type = "",
                    ratios = listOf()
                ),
                maxSpeed = listOf(),
                motor = MotorDto(
                    maxNetPower = 0.0,
                    displacement = 0,
                    fuel = "",
                    particleFilter = false
                ),
                noise = NoiseDto(
                    interior_dB = 0,
                    external_dB = 0
                )
            ),
            emissionsAndEnvironment = EmissionsAndEnvironmentDto(
                environmentalClass = "",
                euroClass = "",
                emissionUnitId = 0,
                emissionValues = EmissionValuesDto(
                    HC = 0.0,
                    NOx = 0.0,
                    PM = 0.0
                ),
                certification = CertificationDto(
                    certificationUnitId = 0,
                    certificationValueHC = 0.0,
                    certificationValueNOx = 0.0,
                    certificationValuePM = 0.0
                ),
                energyUse = null,
                consumptionMixedDriving = null,
                eevVehicle = false
            ),
            doors = "",
        ),
        effectiveFrom = Instant.now(),
        effectiveTo = null,
    )
}

fun createVehicleQualityDto(
    vehicleId: Int,
    timestamp: Instant = Instant.now(),
    fieldQualities: Map<String, Pair<Any?, QualityTypeEnum>> = emptyMap()
): VehicleQualityDto {
    val defaultFieldQualities = mapOf(
        "identification.vehicleRef" to Pair("TEST123", QualityTypeEnum.VALID),
        "identification.vehicleSisId" to Pair("SIS123", QualityTypeEnum.VALID),
        "features.accessibility.wheelchairPlaces" to Pair(2, QualityTypeEnum.VALID),
        "capacity.total" to Pair(50, QualityTypeEnum.VALID),
        "technical.lengthM" to Pair(18.0, QualityTypeEnum.VALID)
    )

    val allFieldQualities = defaultFieldQualities + fieldQualities

    val fieldQualityDtos = allFieldQualities.map { (fieldName, valueAndType) ->
        val (fieldValue, qualityType) = valueAndType
        VehicleFieldQualityDto(
            id = fieldName.hashCode(),
            fieldName = fieldName,
            fieldValue = fieldValue?.toString(),
            qualityType = qualityType.toString(),
            message = "Test data for $fieldName",
            effectiveFrom = timestamp,
            effectiveTo = null,
            isCurrent = true
        )
    }


    return VehicleQualityDto(
        id = 1,
        vehicleId = vehicleId,
        effectiveFrom = timestamp,
        effectiveTo = null,
        isCurrent = true,
        fieldQualities = fieldQualityDtos
    )
}

fun createExpectedVehicleQuality(
    vehicleId: Int = 1,
    vehicleRef: String = "TEST123",
    vehicleSisId: String = "SIS123",
    timestamp: Instant = Instant.now()
): Pair<VehicleDto, VehicleQualityDto> {
    val vehicleDto = createMinimalVehicleDto(vehicleRef)
    val vehicleQualityDto = createVehicleQualityDto(
        vehicleId = vehicleId,
        timestamp = timestamp,
        fieldQualities = mapOf(
            "identification.vehicleRef" to Pair(vehicleRef, QualityTypeEnum.VALID),
            "identification.vehicleSisId" to Pair(vehicleSisId, QualityTypeEnum.VALID)
        )
    )
    
    return Pair(vehicleDto, vehicleQualityDto)
}

fun defaultEventData(): List<EventData> =
    listOf<Pair<String, String?>>(
        // not really sure what default should be
        "chassiNumber" to "YV3R8R324A1139756",
        "godkjenning.tekniskGodkjenning.tekniskeData.dimensjoner.lengde" to "22000",
        "godkjenning.tekniskGodkjenning.tekniskeData.persontall.rullestolplasser" to "1",
        "numberOfPassengersSeated" to "0"
    ).map { it.toEventData() }

fun addEventData(
    eventData: List<EventData>,
    vararg additionalEventData: Pair<String, String?>) : List<EventData>
{
    return eventData + additionalEventData.map { it.toEventData() }
}

fun Pair<String, String?>.toEventData(): EventData {
    return EventData(
        event = testEvent,
        fieldName = first,
        fieldValue = second,
        businessKey = "${testEvent.vehicle.vehicleRef}-${first}",
    )
}

fun toEventData(vararg additionalEventData: Pair<String, String?>) =
    additionalEventData.map { it.toEventData() }
