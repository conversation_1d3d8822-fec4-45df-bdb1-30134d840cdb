scheduler:
  import:
    cron: "0 0 0 * * ?"
    enabled: false
kafka:
snowflake:
  enabled: false
  url: snowflakeurl
  private-key: secretprivatekey
  schema: KOSI
spring:
  main:
    allow-bean-definition-overriding: true
  kafka:
    topics:
      vehicles: vehicle-snapshots
      entityVehicleCapacityNpraDetails: entity.vehicle.capacity
    schema-registry-url: "mock://"
    bootstrap-servers: ${spring.embedded.kafka.brokers}
    properties:
      schema.registry.url: "mock://"
      specific.avro.reader: true
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
    consumer:
      group-id: testid
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
  config:
    activate:
      on-profile: test
  # datasource configuration is handled by TestDataSourceConfiguration
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        format_sql: false
